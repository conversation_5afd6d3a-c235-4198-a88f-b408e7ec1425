using Avalonia.Data.Converters;
using System;
using System.Globalization;

namespace TranslationAgent.Converters
{
    public class NullToDefaultIntConverter : IValueConverter
    {
        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is null)
            {
                return 0; // Or any other default integer value you prefer
            }
            return value;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is null)
            {
                return 0; // Or any other default integer value you prefer
            }
            return value;
        }
    }
}