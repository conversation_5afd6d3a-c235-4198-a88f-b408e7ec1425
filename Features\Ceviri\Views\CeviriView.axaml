<UserControl
    x:Class="TranslationAgent.Features.Ceviri.Views.CeviriView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="clr-namespace:TranslationAgent.Features.Ceviri.Models"
    xmlns:shared="clr-namespace:TranslationAgent.Shared.Views"
    xmlns:vm="clr-namespace:TranslationAgent.Features.Ceviri.ViewModels"
    xmlns:converters="clr-namespace:TranslationAgent.Converters"
    d:DesignHeight="450"
    d:DesignWidth="800"
    x:DataType="vm:CeviriViewModel"
    mc:Ignorable="d">
    <Design.DataContext>
        <vm:CeviriViewModel />
    </Design.DataContext>
    <Border
        Background="Transparent"
        BorderBrush="Gray"
        BorderThickness="1">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="200" MinHeight="150" />
            </Grid.RowDefinitions>

            <!--  Üst Panel: İşlem Kontrol Butonları  -->
            <Border
                Grid.Row="0"
                Padding="10"
                Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                BorderBrush="Gray"
                BorderThickness="0,0,0,1">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <TextBlock
                        Grid.Column="0"
                        VerticalAlignment="Center"
                        FontSize="16"
                        FontWeight="Bold"
                        Text="Çeviri İşlemleri" />

                    <StackPanel
                        Grid.Column="1"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Center"
                        Orientation="Horizontal"
                        Spacing="10">
                        <Button
                            Command="{Binding ProcessStartCommand}"
                            Content="Başlat"
                            IsEnabled="{Binding !IsProcessing}" />
                        <Button
                            Command="{Binding ProcessPauseToggleCommand}"
                            Content="{Binding PauseToggleButtonText}"
                            IsEnabled="{Binding IsProcessing}" />
                        <Button
                            Command="{Binding ProcessStopCommand}"
                            Content="Durdur"
                            IsEnabled="{Binding IsProcessing}" />
                    </StackPanel>
                </Grid>
            </Border>

            <!--  Ana İçerik Alanı  -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="300" MinWidth="250" />
                </Grid.ColumnDefinitions>

                <!--  Sol Panel: Çeviri Verileri ve Düzenleme Paneli  -->
                <Grid Grid.Column="0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <!--  Çeviri Verileri DataGrid  -->
                    <DataGrid
                        x:Name="TranslationDataGrid"
                        Grid.Row="0"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Stretch"
                        AutoGenerateColumns="False"
                        Background="{DynamicResource SystemControlBackgroundAltHighBrush}"
                        CanUserReorderColumns="True"
                        CanUserResizeColumns="True"
                        GridLinesVisibility="All"
                        HeadersVisibility="All"
                        IsReadOnly="True"
                        ItemsSource="{Binding FilteredTextData}"
                        SelectedItem="{Binding SelectedText}">

                        <DataGrid.ContextMenu>
                            <ContextMenu>
                                <MenuItem
                                    Command="{Binding EditTextCommand}"
                                    CommandParameter="{Binding SelectedText}"
                                    Header="Düzenle" />
                                <Separator />
                                <MenuItem Command="{Binding GoogleSheetsPanelVm.FetchTextDataCommand}" Header="Google Sheets'ten Yenile" />
                            </ContextMenu>
                        </DataGrid.ContextMenu>

                        <DataGrid.Columns>
                            <DataGridTextColumn
                                Width="Auto"
                                Binding="{Binding ID}"
                                CanUserSort="True"
                                Header="#" />
                            <DataGridTextColumn
                                Width="120"
                                Binding="{Binding Namespace}"
                                CanUserSort="True"
                                Header="Namespace"
                                IsVisible="{Binding HasNamespace}" />
                            <DataGridTextColumn
                                Width="120"
                                Binding="{Binding Key}"
                                CanUserSort="True"
                                Header="Key"
                                IsVisible="{Binding HasKey}" />
                            <DataGridTextColumn
                                Width="*"
                                MinWidth="200"
                                Binding="{Binding EN}"
                                CanUserSort="True"
                                Header="EN" />
                            <DataGridTextColumn
                                Width="*"
                                MinWidth="200"
                                Binding="{Binding TR}"
                                CanUserSort="True"
                                Header="TR" />
                            <DataGridTextColumn
                                Width="80"
                                Binding="{Binding StatusText}"
                                CanUserSort="True"
                                Header="Durum" />
                            <!-- <DataGridTextColumn
                                Width="120"
                                Binding="{Binding Note}"
                                CanUserSort="True"
                                Header="Not"
                                IsVisible="{Binding HasNote}" /> -->
                        </DataGrid.Columns>
                    </DataGrid>

                    <!--  Düzenleme Paneli  -->
                    <Border
                        Grid.Row="1"
                        Padding="10"
                        Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                        BorderBrush="Gray"
                        BorderThickness="0,1,0,0"
                        IsVisible="{Binding IsEditMode}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="İngilizce:" />
                            <TextBox
                                Grid.Row="0"
                                Grid.Column="1"
                                Grid.ColumnSpan="3"
                                Margin="0,0,0,5"
                                Text="{Binding EditEN}" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="0"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="Türkçe:" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="1"
                                Grid.ColumnSpan="3"
                                Margin="0,0,0,5"
                                Text="{Binding EditTR}" />

                            <TextBlock
                                Grid.Row="2"
                                Grid.Column="0"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="Durum:" />
                            <ComboBox
                                Grid.Row="2"
                                Grid.Column="1"
                                Margin="0,0,10,5"
                                SelectedItem="{Binding EditStatus}">
                                <ComboBox.Items>
                                    <models:TranslationStatus>EN</models:TranslationStatus>
                                    <models:TranslationStatus>TR</models:TranslationStatus>
                                    <models:TranslationStatus>NULL</models:TranslationStatus>
                                    <models:TranslationStatus>DUPE</models:TranslationStatus>
                                </ComboBox.Items>
                            </ComboBox>

                            <TextBlock
                                Grid.Row="2"
                                Grid.Column="2"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                IsVisible="{Binding HasNamespace}"
                                Text="Namespace:" />
                            <TextBox
                                Grid.Row="2"
                                Grid.Column="3"
                                Margin="0,0,0,5"
                                IsVisible="{Binding HasNamespace}"
                                Text="{Binding EditNamespace}" />

                            <TextBlock
                                Grid.Row="3"
                                Grid.Column="0"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                IsVisible="{Binding HasKey}"
                                Text="Key:" />
                            <TextBox
                                Grid.Row="3"
                                Grid.Column="1"
                                Margin="0,0,10,5"
                                IsVisible="{Binding HasKey}"
                                Text="{Binding EditKey}" />

                            <TextBlock
                                Grid.Row="3"
                                Grid.Column="2"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                IsVisible="{Binding HasNote}"
                                Text="Not:" />
                            <TextBox
                                Grid.Row="3"
                                Grid.Column="3"
                                Margin="0,0,0,5"
                                IsVisible="{Binding HasNote}"
                                Text="{Binding EditNote}" />


                            <!--  Butonlar  -->
                            <StackPanel
                                Grid.Row="4"
                                Grid.Column="2"
                                Grid.ColumnSpan="3"
                                Margin="10,5,0,0"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal"
                                Spacing="10">
                                <Button
                                    Padding="15,5"
                                    Command="{Binding SaveEditCommand}"
                                    Content="Kaydet" />
                                <Button
                                    Padding="15,5"
                                    Command="{Binding CancelEditCommand}"
                                    Content="İptal" />
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>

                <!--  Splitter  -->
                <GridSplitter
                    Grid.Column="1"
                    Width="5"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                    ResizeDirection="Columns" />

                <!--  Sağ Panel: Ara ve Çeviri Sekmeleri  -->
                <Grid Grid.Column="2">
                    <TabControl>
                        <TabItem FontSize="16" Header="Ara">
                            <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
                                <StackPanel Margin="10" Spacing="15">
                                    <!--  Arama ve Filtreleme Başlığı  -->
                                    <TextBlock
                                        FontSize="16"
                                        FontWeight="Bold"
                                        Text="Arama ve Filtreleme" />

                                    <!--  Hızlı Arama  -->
                                    <StackPanel Spacing="5">
                                        <TextBlock FontWeight="SemiBold" Text="Hızlı Arama:" />
                                        <TextBox Text="{Binding QuickSearchText}" Watermark="Tüm sütunlarda ara..." />
                                    </StackPanel>

                                    <!--  Durum Filtresi  -->
                                    <StackPanel Spacing="5">
                                        <TextBlock FontWeight="SemiBold" Text="Durum Filtresi:" />
                                        <ComboBox
                                            Width="200"
                                            HorizontalAlignment="Left"
                                            ItemsSource="{Binding StatusFilterOptions}"
                                            SelectedItem="{Binding SelectedStatusFilter}">
                                            <ComboBox.ItemTemplate>
                                                <DataTemplate>
                                                    <TextBlock Text="{Binding}" />
                                                </DataTemplate>
                                            </ComboBox.ItemTemplate>
                                        </ComboBox>
                                    </StackPanel>

                                    <!--  Gelişmiş Filtreler  -->
                                    <StackPanel Spacing="10">
                                        <StackPanel Orientation="Horizontal" Spacing="10">
                                            <TextBlock
                                                VerticalAlignment="Center"
                                                FontWeight="SemiBold"
                                                Text="Gelişmiş Filtreler:" />
                                            <Button
                                                Padding="8,4"
                                                Command="{Binding AddCeviriFilterCommand}"
                                                Content="+"
                                                FontSize="12" />
                                        </StackPanel>

                                        <!--  Filtre Listesi  -->
                                        <ItemsControl ItemsSource="{Binding CeviriFilterEntries}">
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate>
                                                    <Border
                                                        Margin="0,5"
                                                        Padding="8"
                                                        BorderBrush="Gray"
                                                        BorderThickness="1"
                                                        CornerRadius="4">
                                                        <Grid RowDefinitions="Auto,5,Auto">
                                                            <!--  Sütun ve Sil Butonu  -->
                                                            <Grid Grid.Row="0" ColumnDefinitions="*,5,*,5,Auto">
                                                                <ComboBox
                                                                    Grid.Column="0"
                                                                    Height="32"
                                                                    HorizontalAlignment="Stretch"
                                                                    VerticalAlignment="Center"
                                                                    ItemsSource="{Binding $parent[ItemsControl].DataContext.CeviriColumnHeaders}"
                                                                    SelectedItem="{Binding ColumnName}" />
                                                                <ComboBox
                                                                    Grid.Column="2"
                                                                    Height="32"
                                                                    HorizontalAlignment="Stretch"
                                                                    VerticalAlignment="Center"
                                                                    ItemsSource="{x:Static models:CeviriFilterEntry.AvailableTypes}"
                                                                    SelectedItem="{Binding SelectedType}" />
                                                                <Button
                                                                    Grid.Column="4"
                                                                    Height="32"
                                                                    HorizontalAlignment="Stretch"
                                                                    VerticalAlignment="Center"
                                                                    Command="{Binding $parent[ItemsControl].DataContext.RemoveCeviriFilterCommand}"
                                                                    CommandParameter="{Binding}"
                                                                    Content="×"
                                                                    FontSize="16" />
                                                            </Grid>


                                                            <!--  Operatör ve Değer  -->
                                                            <Grid Grid.Row="2" ColumnDefinitions="*,5,*">
                                                                <ComboBox
                                                                    Grid.Column="0"
                                                                    Height="32"
                                                                    HorizontalAlignment="Stretch"
                                                                    VerticalAlignment="Center"
                                                                    ItemsSource="{Binding AvailableOperators}"
                                                                    SelectedItem="{Binding SelectedOperator}" />
                                                                <TextBox
                                                                    Grid.Column="2"
                                                                    Height="32"
                                                                    HorizontalAlignment="Stretch"
                                                                    VerticalAlignment="Center"
                                                                    Text="{Binding FilterValue}"
                                                                    Watermark="Filtre değeri..." />
                                                            </Grid>
                                                        </Grid>
                                                    </Border>
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </StackPanel>

                                    <!--  Filtre Uygula Butonu  -->
                                    <Button
                                        HorizontalAlignment="Stretch"
                                        Command="{Binding ApplyFiltersCommand}"
                                        Content="Filtreleri Uygula" />

                                    <!--  Filtreleri Temizle Butonu  -->
                                    <Button
                                        HorizontalAlignment="Stretch"
                                        Command="{Binding ClearFiltersCommand}"
                                        Content="Filtreleri Temizle" />
                                </StackPanel>
                            </ScrollViewer>
                        </TabItem>

                        <TabItem FontSize="16" Header="Çeviri">
                            <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
                                <StackPanel Margin="10" Spacing="15">
                                    <TextBlock
                                        FontSize="16"
                                        FontWeight="Bold"
                                        Text="Çeviri Ayarları" />

                                    <!--  Çeviri Kalitesi  -->
                                    <StackPanel Spacing="5">
                                        <TextBlock FontWeight="SemiBold" Text="Çeviri Kalitesi:" />
                                        <ComboBox
                                            Width="200"
                                            HorizontalAlignment="Left"
                                            ItemsSource="{Binding AvailableQualities}"
                                            SelectedItem="{Binding SelectedQuality}" />
                                        <TextBlock
                                            FontSize="12"
                                            FontStyle="Italic"
                                            Foreground="Gray"
                                            Text="Çeviri kalitesi seviyesini seçin."
                                            TextWrapping="Wrap" />
                                    </StackPanel>

                                    <!--  Metin Bağlam Sütunları  -->
                                    <StackPanel Spacing="5" IsVisible="{Binding IsTextContextColumnVisible}">
                                        <TextBlock FontWeight="SemiBold" Text="Metin Bağlam Sütunları:" />
                                        <Border
                                            Width="200"
                                            Height="80"
                                            HorizontalAlignment="Left"
                                            BorderBrush="Gray"
                                            BorderThickness="1"
                                            CornerRadius="3">
                                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                                <ItemsControl ItemsSource="{Binding AvailableTextContextColumns}">
                                                    <ItemsControl.ItemTemplate>
                                                        <DataTemplate>
                                                            <CheckBox
                                                                 Margin="5,2"
                                                                 Content="{Binding}"
                                                                 IsChecked="{Binding $parent[UserControl].DataContext.SelectedTextContextColumns, Converter={x:Static converters:StringCollectionContainsConverter.Instance}, ConverterParameter={Binding}}, Mode=TwoWay}"
                                                                 Command="{Binding $parent[UserControl].DataContext.ToggleTextContextColumnCommand}"
                                                                 CommandParameter="{Binding}" />
                                                        </DataTemplate>
                                                    </ItemsControl.ItemTemplate>
                                                </ItemsControl>
                                            </ScrollViewer>
                                        </Border>
                                        <TextBlock
                                            FontSize="12"
                                            FontStyle="Italic"
                                            Foreground="Gray"
                                            Text="Çeviri işleminde kullanılacak bağlam sütunlarını seçin."
                                            TextWrapping="Wrap" />
                                    </StackPanel>

                                    <!--  Opsiyonel Prompt  -->
                                    <StackPanel Spacing="5">
                                        <TextBlock FontWeight="SemiBold" Text="Opsiyonel Prompt:" />
                                        <TextBox
                                            Height="60"
                                            AcceptsReturn="True"
                                            Text="{Binding OptionalPrompt}"
                                            TextWrapping="Wrap"/>
                                        <TextBlock
                                            FontSize="12"
                                            FontStyle="Italic"
                                            Foreground="Gray"
                                            Text="AI'ya çeviri için özel talimatlar verebilirsiniz."
                                            TextWrapping="Wrap" />
                                    </StackPanel>
                                </StackPanel>
                            </ScrollViewer>
                        </TabItem>

                        <TabItem FontSize="16" Header="Not">
                            <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
                                <StackPanel Margin="10" Spacing="15">
                                    <TextBlock
                                        FontSize="16"
                                        FontWeight="Bold"
                                        Text="Not İşlemleri" />
                                                           <TextBlock
                                            FontSize="12"
                                            FontStyle="Italic"
                                            Foreground="Gray"
                                            Text="Çeviri işleminde satır için ek not ekleyebilirsiniz."
                                            TextWrapping="Wrap" />

                                    <!--  Tekil Not Ekleme  -->
                                    <StackPanel Spacing="10">
                                        <TextBlock FontWeight="SemiBold" Text="Seçili Satıra Not Ekle:" />
                                        
                                        <!--  Seçili Metin Bilgisi  -->
                                        <Border
                                            Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                                            CornerRadius="5"
                                            Padding="10"
                                            IsVisible="{Binding SelectedText, Converter={x:Static ObjectConverters.IsNotNull}}">
                                            <StackPanel Spacing="5">
                                                <TextBlock FontWeight="SemiBold" Text="Seçili Metin:" />
                                                <TextBlock
                                                    Text="{Binding SelectedText.EN}"
                                                    TextWrapping="Wrap"
                                                    MaxLines="3" />
                                                <TextBlock
                                                    FontSize="12"
                                                    FontStyle="Italic"
                                                    Foreground="Gray"
                                                    Text="{Binding SelectedText.Note, StringFormat='Mevcut Not: {0}'}"
                                                    IsVisible="{Binding SelectedText.HasNote}" />
                                            </StackPanel>
                                        </Border>

                                        <!--  Not Girişi  -->
                                        <TextBox
                                            Height="80"
                                            AcceptsReturn="True"
                                            Text="{Binding NoteText}"
                                            TextWrapping="Wrap"
                                            Watermark="Not metni girin..."
                                            IsEnabled="{Binding SelectedText, Converter={x:Static ObjectConverters.IsNotNull}}" />

                                        <!--  Not Ekle Butonu  -->
                                        <Button
                                            HorizontalAlignment="Left"
                                            Command="{Binding AddNoteCommand}"
                                            Content="Not Ekle" />
                                    </StackPanel>

                                    <!--  Ayırıcı  -->
                                    <Separator />

                                    <!--  Toplu Not Ekleme  -->
                                    <StackPanel Spacing="10">
                                        <TextBlock FontWeight="SemiBold" Text="Toplu Not Ekleme:" />
                                        
                                        <!--  Filtrelenmiş Liste Bilgisi  -->
                                        <Border
                                            Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                                            CornerRadius="5"
                                            Padding="10">
                                            <StackPanel Spacing="5">
                                                <TextBlock
                                                    Text="{Binding FilteredTextData.Count, StringFormat='Filtrelenmiş Liste: {0} satır'}"
                                                    FontWeight="SemiBold" />
                                                <TextBlock
                                                    FontSize="12"
                                                    Foreground="Gray"
                                                    Text="Bu listedeki tüm satırlara aynı not eklenecektir."
                                                    TextWrapping="Wrap" />
                                            </StackPanel>
                                        </Border>

                                        <!--  Toplu Not Girişi  -->
                                        <TextBox
                                            Height="80"
                                            AcceptsReturn="True"
                                            Text="{Binding BulkNoteText}"
                                            TextWrapping="Wrap"
                                            Watermark="Toplu not metni girin..."
                                            IsEnabled="{Binding !IsBulkNoteProcessing}" />

                                        <!--  Toplu İşlem Butonları  -->
                                        <StackPanel Orientation="Horizontal" Spacing="10">
                                            <Button
                                                Command="{Binding AddBulkNoteCommand}"
                                                Content="Toplu Not Ekle"
                                                IsVisible="{Binding !IsBulkNoteProcessing}" />
                                            <Button
                                                Command="{Binding CancelBulkNoteCommand}"
                                                Content="İptal Et"
                                                IsVisible="{Binding IsBulkNoteProcessing}" />
                                        </StackPanel>

                                        <!--  İlerleme Göstergesi  -->
                                        <StackPanel Spacing="5" IsVisible="{Binding IsBulkNoteProcessing}">
                                            <TextBlock
                                                Text="{Binding BulkNoteProgressText}"
                                                FontSize="12"
                                                TextWrapping="Wrap" />
                                            <ProgressBar
                                                Height="20"
                                                Value="{Binding BulkNoteProgress}"
                                                Maximum="100" />
                                            <TextBlock
                                                Text="{Binding BulkNoteProgress, StringFormat='{}{0}% tamamlandı'}"
                                                FontSize="12"
                                                HorizontalAlignment="Center" />
                                        </StackPanel>
                                    </StackPanel>
                                </StackPanel>
                            </ScrollViewer>
                        </TabItem>
                    </TabControl>
                </Grid>
            </Grid>

            <!--  GridSplitter  -->
            <GridSplitter
                Grid.Row="2"
                Height="5"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Center"
                Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                ResizeDirection="Rows" />

            <!--  Alt Panel: Log ve İstatistik Paneli  -->
            <shared:LogStatisticsPanel Grid.Row="3" DataContext="{Binding LogStatisticsPanelVm}" />
        </Grid>
    </Border>
</UserControl>
