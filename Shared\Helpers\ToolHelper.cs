using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Nodes;
using Anthropic.SDK.Common;
using Anthropic.SDK.Messaging;
using AnthropicTool = Anthropic.SDK.Messaging.Tool;
using CommonTool = Anthropic.SDK.Common.Tool;
using Mscc.GenerativeAI;

namespace TranslationAgent.Helpers
{
    public static class ToolHelper
    {
        /// <summary>
        /// JSON string'den Anthropic SDK Tool listesi oluşturur
        /// </summary>
        public static List<CommonTool> CreateAnthropicToolsFromJson(string jsonString)
        {
            try
            {
                var jsonArray = JsonNode.Parse(jsonString)?.AsArray();
                if (jsonArray == null)
                {
                    throw new InvalidOperationException("Geçersiz JSON formatı: Array bekleniyor.");
                }
                var tools = new List<CommonTool>();
                foreach (var jsonNode in jsonArray)
                {
                    var function = new Function(jsonNode["name"]?.GetValue<string>() ?? "" ,jsonNode["description"]?.GetValue<string>() ?? "",jsonNode["parameters"]);
                    tools.Add(function);
                }
                return tools;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"JSON dönüştürme hatası: {ex.Message}", ex);
            }
        }



        /// <summary>
        /// JSON string'den Gemini Tool listesi oluşturur (mevcut kod)
        /// </summary>
        public static List<Mscc.GenerativeAI.Tool> CreateGeminiToolsFromJson(string jsonString)
        {
            try
            {
                var jsonArray = JsonNode.Parse(jsonString)?.AsArray();
                if (jsonArray == null)
                {
                    throw new InvalidOperationException("Geçersiz JSON formatı: Array bekleniyor.");
                }

                var tools = new List<Mscc.GenerativeAI.Tool>();
                var tool = new Mscc.GenerativeAI.Tool { FunctionDeclarations = new List<FunctionDeclaration>() };

                foreach (var jsonNode in jsonArray)
                {
                    var functionDeclaration = new FunctionDeclaration
                    {
                        Name = jsonNode["name"]?.GetValue<string>() ?? "",
                        Description = jsonNode["description"]?.GetValue<string>() ?? "",
                        Parameters = ParseGeminiSchema(jsonNode["parameters"])
                    };

                    tool.FunctionDeclarations.Add(functionDeclaration);
                }

                tools.Add(tool);
                return tools;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"JSON dönüştürme hatası: {ex.Message}", ex);
            }
        }

        private static Schema ParseGeminiSchema(JsonNode? schemaNode)
        {
            if (schemaNode == null) return new Schema();

            var typeStr = schemaNode["type"]?.GetValue<string>() ?? "object";
            var type = GetGeminiParameterType(typeStr);
            var schema = new Schema { Type = type };

            if (type == ParameterType.Object)
            {
                schema.Properties = new Dictionary<string, object>();
                var propertiesNode = schemaNode["properties"];
                if (propertiesNode != null)
                {
                    foreach (var property in propertiesNode.AsObject())
                    {
                        schema.Properties[property.Key] = ParseGeminiSchema(property.Value);
                    }
                }
                // "required" alanı
                var requiredNode = schemaNode["required"];
                if (requiredNode is JsonArray requiredArray)
                {
                    schema.Required = requiredArray.Select(x => x.GetValue<string>()).ToList();
                }
            }
            else if (type == ParameterType.Array)
            {
                var itemsNode = schemaNode["items"];
                if (itemsNode != null)
                {
                    schema.Items = ParseGeminiSchema(itemsNode);
                }
            }

            return schema;
        }

        private static ParameterType GetGeminiParameterType(string? type)
        {
            return type?.ToLower() switch
            {
                "string" => ParameterType.String,
                "integer" => ParameterType.Integer,
                "number" => ParameterType.Number,
                "boolean" => ParameterType.Boolean,
                "array" => ParameterType.Array,
                "object" => ParameterType.Object,
                _ => ParameterType.Object
            };
        }
    }
}