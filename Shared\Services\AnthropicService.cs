using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Anthropic.SDK;
using Anthropic.SDK.Common;
using Anthropic.SDK.Messaging;
using AnthropicTool = Anthropic.SDK.Messaging.Tool;
using CommonTool = Anthropic.SDK.Common.Tool;
using TranslationAgent.Helpers;
using TranslationAgent.Services;
using Anthropic.SDK.Constants;

namespace TranslationAgent.Services
{
    public class AnthropicService
    {
        private readonly AppSettings _settings;
        private AnthropicClient? _client;
        private readonly LogService _logService;
        private string? _currentApiKey;

        public AnthropicService(AppSettings settings, LogService logService)
        {
            _settings = settings;
            _logService = logService;

            if (!string.IsNullOrEmpty(_settings.General.ClaudeApiKey))
            {
                SetupModel();
            }

        }

        private void SetupModel()
        {
            // API anahtarını ayarlardan al
            _currentApiKey = _settings.General.ClaudeApiKey;

            if (string.IsNullOrEmpty(_currentApiKey))
            {
                throw new InvalidOperationException("Anthropic API anahtarı bulunamadı.");
            }

            _client = new AnthropicClient(_currentApiKey);
            _logService.Info($"Anthropic API kullanılıyor", true, LogService.LogState.Translate);
        }

        /// <summary>
        /// LogState'e göre uygun model ve parametreleri belirler
        /// </summary>
        private (string model, decimal temperature, int maxTokens) GetModelConfig(LogService.LogState logState, AIModel aiModel)
        {
            var temperature = logState switch
            {
                LogService.LogState.Context => 0.7m,
                LogService.LogState.Stage1 => 0.7m,
                LogService.LogState.Stage2 => 0.7m,
                LogService.LogState.Stage3 => 1.3m,
                LogService.LogState.Translate => 1.3m,
                _ => 1.0m
            };

            // Anthropic model seçimi
            var model = aiModel switch
            {
                AIModel.Claude35Sonnet => AnthropicModels.Claude35Sonnet,
                AIModel.Claude37Sonnet => AnthropicModels.Claude37Sonnet,
                AIModel.Claude4Sonnet => AnthropicModels.Claude4Sonnet,
                _ => AnthropicModels.Claude35Sonnet
            };

            var maxTokens = 4096; // Anthropic için varsayılan

            return (model, temperature, maxTokens);
        }

        public async Task<JsonElement?> GenerateContentWithToolsAsync(string systemPrompt, string prompt, List<CommonTool> tools, AIModel aiModel = AIModel.Claude35Sonnet, LogService.LogState logState = LogService.LogState.Main, CancellationToken cancellationToken = default)
        {
            if (_client == null)
            {
                SetupModel();
            }

            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                var (model, temperature, maxTokens) = GetModelConfig(logState, aiModel);

                var messages = new List<Message>
                {
                    new Message(RoleType.User, prompt)
                };

                var parameters = new MessageParameters()
                {
                    System = new List<SystemMessage> { new SystemMessage(systemPrompt) },
                    Messages = messages,
                    MaxTokens = maxTokens,
                    Model = model,
                    Stream = false,
                    Temperature = temperature,
                    Tools = tools,
                    ToolChoice = new ToolChoice()
                    {
                        Type = ToolChoiceType.Auto
                    }
                };

                var response = await _client!.Messages.GetClaudeMessageAsync(parameters, cancellationToken);

                if (response?.Content == null || !response.Content.Any())
                {
                    throw new Exception("Model yanıt vermedi.");
                }

                // Tool kullanımını kontrol et
                var toolUseContent = response.Content.OfType<ToolUseContent>().FirstOrDefault();
                if (toolUseContent != null)
                {
                    _logService.Info($"Tool çağrısı: {toolUseContent.Name}", false, logState);

                    // Tool parametrelerini JsonElement olarak döndür
                    var inputJson = JsonSerializer.Serialize(toolUseContent.Input);
                    return JsonSerializer.Deserialize<JsonElement>(inputJson);
                }

                // Eğer tool kullanımı yoksa, text içeriğinden JSON çıkarmaya çalış
                var textContent = response.Content.OfType<TextContent>().FirstOrDefault();
                if (textContent?.Text != null)
                {
                    _logService.Info($"Metin yanıtı alındı", false, logState);

                    try
                    {
                        // Markdown kod bloğundan JSON içeriğini çıkar
                        var match = Regex.Match(textContent.Text, @"```(?:json)?\s*({[\s\S]*?})\s*```");
                        if (match.Success)
                        {
                            return JsonSerializer.Deserialize<JsonElement>(match.Groups[1].Value);
                        }
                        else
                        {
                            return null;
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"JSON ayrıştırma hatası: {ex.Message}", ex);
                    }
                }

                throw new Exception("Tool veya beklenen JSON yapısı bulunamadı");
            }
            catch (Exception ex)
            {
                throw new Exception($"Hata: {ex.Message}", ex);
            }
        }

        public async Task<JsonElement?> GenerateContentWithJsonToolsAsync(string systemPrompt, string prompt, string toolsJson, AIModel aiModel = AIModel.Claude35Sonnet, LogService.LogState logState = LogService.LogState.Main, CancellationToken cancellationToken = default)
        {
            var tools = ToolHelper.CreateAnthropicToolsFromJson(toolsJson);
            return await GenerateContentWithToolsAsync(systemPrompt, prompt, tools, aiModel, logState, cancellationToken);
        }

        public async Task<string?> GenerateContentAsync(string systemPrompt, string prompt, AIModel aiModel = AIModel.Claude35Sonnet, LogService.LogState logState = LogService.LogState.Main, CancellationToken cancellationToken = default)
        {
            if (_client == null)
            {
                SetupModel();
            }
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                var (model, temperature, maxTokens) = GetModelConfig(logState, aiModel);

                var messages = new List<Message>
                {
                    new Message(RoleType.User, prompt)
                };

                var parameters = new MessageParameters()
                {
                    System = new List<SystemMessage> { new SystemMessage(systemPrompt) },
                    Messages = messages,
                    MaxTokens = maxTokens,
                    Model = model,
                    Stream = false,
                    Temperature = temperature,
                };

                var response = await _client!.Messages.GetClaudeMessageAsync(parameters, cancellationToken);

                if (response?.Content == null || !response.Content.Any())
                {
                    throw new Exception("Model yanıt vermedi.");
                }

                var textContent = response.Content.OfType<TextContent>().FirstOrDefault();
                if (textContent?.Text != null)
                {
                    var match = Regex.Match(textContent.Text, @"<rapor>([\s\S]*?)</rapor>");
                    if (match.Success)
                    {
                        return match.Groups[1].Value;
                    }
                    else
                    {
                        _logService.Info($"Rapor tagı bulunamadı", false, logState);
                        return null;
                    }
                }
                else
                {
                    _logService.Info($"Metin içeriği bulunamadı", false, logState);
                }

                throw new Exception("Beklenen Rapor yapısı bulunamadı");
            }
            catch (Exception ex)
            {
                throw new Exception($"İçerik oluşturma hatası: {ex.Message}", ex);
            }
        }

        public async Task<string?> GenerateContentWithStringAsync(string systemPrompt, string prompt, AIModel aiModel = AIModel.Claude35Sonnet, LogService.LogState logState = LogService.LogState.Main, CancellationToken cancellationToken = default)
        {
            return await GenerateContentAsync(systemPrompt, prompt, aiModel, logState, cancellationToken);
        }

        public string GetCurrentModelInfo()
        {
            if (_client == null) return "Model henüz başlatılmadı";
            return "Anthropic Claude";
        }

        public string GetCurrentApiKeyInfo()
        {
            if (string.IsNullOrEmpty(_currentApiKey)) return "API anahtarı bulunamadı";
            return $"API Key: {_currentApiKey.Substring(0, Math.Min(8, _currentApiKey.Length))}...";
        }
    }
}