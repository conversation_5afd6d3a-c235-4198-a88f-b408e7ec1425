using System;
using System.Collections.Generic;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;
using TranslationAgent.Features.Baglam.Models;

namespace TranslationAgent.Converters
{
    public class MainContextHighlightConverter : IMultiValueConverter
    {
        private static readonly IBrush HighlightBrush = new SolidColorBrush(Color.Parse("#FF0000")); // Red
        private static readonly IBrush NormalBrush = new SolidColorBrush(Colors.Transparent);

        private static MainContextHighlightConverter? _instance;
        public static MainContextHighlightConverter Instance => _instance ??= new MainContextHighlightConverter();

        public object? Convert(IList<object?> values, Type targetType, object? parameter, CultureInfo culture)
        {
            if (values.Count >= 2 &&
                values[0] is Context currentContext &&
                values[1] is Context mainContext)
            {
                return ReferenceEquals(currentContext, mainContext) ? HighlightBrush : NormalBrush;
            }
            return NormalBrush;
        }

        public object[]? ConvertBack(object? value, Type[] targetTypes, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
