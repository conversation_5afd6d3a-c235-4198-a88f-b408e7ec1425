using System;
using System.Globalization;
using Avalonia.Data.Converters;
using TranslationAgent.Features.Terminoloji.Models; // TermStatus'ı kullanmak için ekledik

namespace TranslationAgent.Converters
{
    public class TranslationStatusConverter : IValueConverter
    {
        public static readonly TranslationStatusConverter Instance = new();

        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is TermStatus status)
            {
                return status switch
                {
                    TermStatus.İncelemede => "İncelemede",
                    TermStatus.Onaylandı => "Onaylandı",
                    TermStatus.Çevrildi => "Çevrildi",
                    _ => "Bilinmiyor" // Varsayılan durum
                };
            }

            return "Bilinmiyor"; // Eğer değer TermStatus değilse
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}