<Window
    x:Class="TranslationAgent.MainWindow"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:baglam="clr-namespace:TranslationAgent.Features.Baglam.Views"
    xmlns:ceviri="clr-namespace:TranslationAgent.Features.Ceviri.Views"
    xmlns:converters="clr-namespace:TranslationAgent.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:TranslationAgent"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:settings="clr-namespace:TranslationAgent.Features.Settings.Views"
    xmlns:sheets="clr-namespace:TranslationAgent.Features.GoogleSheets.Views"
    xmlns:terminoloji="clr-namespace:TranslationAgent.Features.Terminoloji.Views"
    Title="TranslationAgent"
    d:DesignHeight="450"
    d:DesignWidth="800"
    x:DataType="local:MainWindow"
    mc:Ignorable="d">
    <Design.DataContext>
        <local:MainWindow />
    </Design.DataContext>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <!--  Global Google Sheets Sidebar  -->
        <sheets:GoogleSheetsPanelView Grid.Column="0" DataContext="{Binding GoogleSheetsPanelVm}" />

        <!--  Main Content  -->
        <Grid Grid.Column="1">
            <TabControl>
                <TabItem Header="Çeviri">
                    <ceviri:CeviriView DataContext="{Binding CeviriVm}" />
                </TabItem>
                <TabItem Header="Terimce">
                    <terminoloji:TerminolojiView DataContext="{Binding TerminolojiVm}" />
                </TabItem>

                <TabItem Header="Bağlam">
                    <baglam:BaglamView DataContext="{Binding BaglamVm}" />
                </TabItem>

                <TabItem Header="Ayarlar">
                    <settings:SettingsView DataContext="{Binding SettingsVm}" />
                </TabItem>
            </TabControl>

            <StackPanel
                Margin="0,10,10,0"
                HorizontalAlignment="Right"
                VerticalAlignment="Top"
                Orientation="Horizontal"
                Spacing="10">
                <StackPanel Orientation="Horizontal" Spacing="5">
                    <Ellipse
                        Width="12"
                        Height="12"
                        Stroke="DarkGray"
                        StrokeThickness="1">
                        <Ellipse.Fill>
                            <MultiBinding Converter="{x:Static converters:DataCountToBrushConverter.Instance}">
                                <Binding Path="GoogleSheetsPanelVm.TerminologyData.Count" />
                                <Binding Path="GoogleSheetsPanelVm.IsTerminologyDataFetched" />
                            </MultiBinding>
                        </Ellipse.Fill>
                        <Ellipse.Effect>
                            <DropShadowEffect
                                BlurRadius="8"
                                OffsetX="0"
                                OffsetY="0"
                                Opacity="0.5" />
                        </Ellipse.Effect>
                    </Ellipse>
                    <StackPanel>
                        <TextBlock FontWeight="SemiBold" Text="Terimce" />
                        <TextBlock
                            FontSize="11"
                            Foreground="Gray"
                            Text="{Binding GoogleSheetsPanelVm.TerminologyData.Count, StringFormat='{}{0} terim'}" />
                    </StackPanel>
                </StackPanel>

                <StackPanel Orientation="Horizontal" Spacing="5">
                    <Ellipse
                        Width="12"
                        Height="12"
                        Stroke="DarkGray"
                        StrokeThickness="1">
                        <Ellipse.Fill>
                            <MultiBinding Converter="{x:Static converters:DataCountToBrushConverter.Instance}">
                                <Binding Path="GoogleSheetsPanelVm.TextData.Count" />
                                <Binding Path="GoogleSheetsPanelVm.IsTextDataFetched" />
                            </MultiBinding>
                        </Ellipse.Fill>
                        <Ellipse.Effect>
                            <DropShadowEffect
                                BlurRadius="8"
                                OffsetX="0"
                                OffsetY="0"
                                Opacity="0.5" />
                        </Ellipse.Effect>
                    </Ellipse>
                    <StackPanel>
                        <TextBlock FontWeight="SemiBold" Text="Metinler" />
                        <TextBlock
                            FontSize="11"
                            Foreground="Gray"
                            Text="{Binding GoogleSheetsPanelVm.TextData.Count, StringFormat='{}{0} metin'}" />
                    </StackPanel>
                </StackPanel>

                <StackPanel Orientation="Horizontal" Spacing="5">
                    <Ellipse
                        Width="12"
                        Height="12"
                        Stroke="DarkGray"
                        StrokeThickness="1">
                        <Ellipse.Fill>
                            <MultiBinding Converter="{x:Static converters:DataCountToBrushConverter.Instance}">
                                <Binding Path="GoogleSheetsPanelVm.ContextData.Count" />
                                <Binding Path="GoogleSheetsPanelVm.IsContextDataFetched" />
                            </MultiBinding>
                        </Ellipse.Fill>
                        <Ellipse.Effect>
                            <DropShadowEffect
                                BlurRadius="8"
                                OffsetX="0"
                                OffsetY="0"
                                Opacity="0.5" />
                        </Ellipse.Effect>
                    </Ellipse>
                    <StackPanel>
                        <TextBlock FontWeight="SemiBold" Text="Bağlamlar" />
                        <TextBlock
                            FontSize="11"
                            Foreground="Gray"
                            Text="{Binding GoogleSheetsPanelVm.ContextData.Count, StringFormat='{}{0} bağlam'}" />
                    </StackPanel>
                </StackPanel>

                <ToggleButton
                    Name="ThemeToggle"
                    Width="32"
                    Height="32"
                    IsChecked="{Binding IsDarkTheme, Mode=TwoWay}">
                    <Grid>
                        <Path
                            Width="24"
                            Height="24"
                            Data="M12 7a5 5 0 1 0 0 10 5 5 0 0 0 0-10zM12 1v2m0 18v2M4.22 4.22l1.42 1.42m12.72 12.72 1.42 1.42M1 12h2m18 0h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"
                            Fill="Orange"
                            IsVisible="{Binding !IsChecked, RelativeSource={RelativeSource AncestorType=ToggleButton}}"
                            Stroke="Orange"
                            StrokeThickness="1.5" />
                        <Path
                            Width="24"
                            Height="24"
                            Data="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"
                            Fill="White"
                            IsVisible="{Binding IsChecked, RelativeSource={RelativeSource AncestorType=ToggleButton}}"
                            Stroke="Gray" />
                    </Grid>
                </ToggleButton>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
