using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using SmartComponents.LocalEmbeddings;

namespace TranslationAgent.Features.Terminoloji.Models
{
    public enum TermStatus
    {

        İncelemede,
        Onaylandı,
        Çevrildi
    }

    public class TermViewModel : INotifyPropertyChanged
    {
        private int _id;
        private string _en;
        private string _tr;
        private string _kategori;
        private string _bilgi;
        private string _lemma;
        private EmbeddingF32? _vector;
        private TermStatus _status;

        public int ID
        {
            get => _id;
            set { if (_id != value) { _id = value; OnPropertyChanged(nameof(ID)); } }
        }
        public string EN
        {
            get => _en;
            set { if (_en != value) { _en = value; OnPropertyChanged(nameof(EN)); } }
        }
        public string TR
        {
            get => _tr;
            set { if (_tr != value) { _tr = value; OnPropertyChanged(nameof(TR)); } }
        }
        public string Kategori
        {
            get => _kategori;
            set { if (_kategori != value) { _kategori = value; OnPropertyChanged(nameof(Kategori)); } }
        }
        public string Bilgi
        {
            get => _bilgi;
            set { if (_bilgi != value) { _bilgi = value; OnPropertyChanged(nameof(Bilgi)); } }
        }
        public string Lemma
        {
            get => _lemma;
            set { if (_lemma != value) { _lemma = value; OnPropertyChanged(nameof(Lemma)); } }
        }
        public EmbeddingF32? Vector
        {
            get => _vector;
            set { if (!Equals(_vector, value)) { _vector = value; OnPropertyChanged(nameof(Vector)); } }
        }

        public TermStatus Status
        {
            get => _status;
            set { if (_status != value) { _status = value; OnPropertyChanged(nameof(Status)); } }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string propertyName) => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));

        /// <summary>
        /// Terminoloji sayfası sütun başlıklarının geçerliliğini kontrol eder
        /// </summary>
        /// <param name="headers">Sütun başlıkları</param>
        /// <returns>Validation sonucu</returns>
        public static (bool IsValid, string ErrorMessage, bool HasLemma) ValidateHeaders(List<string> headers)
        {
            var upperHeaders = headers.Select(h => h.ToUpper(new System.Globalization.CultureInfo("tr-TR"))).ToList();

            // Gerekli sütunları kontrol et
            var requiredColumns = new[] { "#", "EN", "TR", "KATEGORİ", "BİLGİ", "DURUM" };
            var missingColumns = requiredColumns.Where(col => !upperHeaders.Contains(col)).ToList();

            if (missingColumns.Any())
            {
                return (false, $"Gerekli sütunlar eksik: {string.Join(", ", missingColumns)}", false);
            }

            // Opsiyonel sütunları kontrol et
            bool hasLemma = upperHeaders.Contains("LEMMA");

            return (true, string.Empty, hasLemma);
        }
    }
}
