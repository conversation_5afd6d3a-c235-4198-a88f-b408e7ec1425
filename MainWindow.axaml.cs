using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.Primitives;
using Avalonia.Interactivity;
using Avalonia.Styling;
using Catalyst;
using Microsoft.Extensions.DependencyInjection;
using SmartComponents.LocalEmbeddings;
using TranslationAgent.Features.Baglam.ViewModels;
using TranslationAgent.Features.Ceviri.ViewModels;
using TranslationAgent.Features.GoogleSheets.ViewModels;
using TranslationAgent.Features.Settings.ViewModels;
using TranslationAgent.Features.Terminoloji.ViewModels;
using TranslationAgent.Services;

namespace TranslationAgent
{
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        private readonly AppSettings _appSettings;
        private readonly GoogleSheetsService _googleSheetsService;
        private readonly LogService _logService;
        private readonly ToastService _toastService;
        private readonly GeminiService _geminiService;
        private readonly AnthropicService _anthropicService;
        private readonly CrawlerService _crawlerService;
        private readonly Pipeline _nlp;
        private readonly LocalEmbedder _localEmbedder;
        public SettingsViewModel SettingsVm { get; private set; }
        public GoogleSheetsPanelViewModel GoogleSheetsPanelVm { get; private set; }
        public TerminolojiViewModel TerminolojiVm { get; private set; }
        public CeviriViewModel CeviriVm { get; private set; }
        public BaglamViewModel BaglamVm { get; private set; }

        private bool _isDarkTheme;
        public bool IsDarkTheme
        {
            get => _isDarkTheme;
            set
            {
                if (_isDarkTheme != value)
                {
                    _isDarkTheme = value;
                    OnPropertyChanged();
                    ApplyTheme();
                }
            }
        }

        public MainWindow()
        {
            try
            {
                InitializeComponent();

                var serviceProvider = Program.GetServiceProvider();
                _toastService = serviceProvider.GetRequiredService<ToastService>();
                _toastService.Initialize(this);

                _logService = serviceProvider.GetRequiredService<LogService>();
                _appSettings = serviceProvider.GetRequiredService<AppSettings>();
                _geminiService = serviceProvider.GetRequiredService<GeminiService>();
                _anthropicService = serviceProvider.GetRequiredService<AnthropicService>();
                _crawlerService = serviceProvider.GetRequiredService<CrawlerService>();
                _nlp = serviceProvider.GetRequiredService<Pipeline>();
                _localEmbedder = serviceProvider.GetRequiredService<LocalEmbedder>();
                _googleSheetsService = new GoogleSheetsService(_logService);

                if (!string.IsNullOrWhiteSpace(_appSettings.General.ServiceAccountJson))
                {
                    _googleSheetsService.AuthenticateAsync(_appSettings.General.ServiceAccountJson)
                        .ContinueWith(
                            t =>
                            {
                                if (t.IsFaulted)
                                {
                                    _logService.Error(
                                        $"Kimlik doğrulama başarısız: {t.Exception?.GetBaseException().Message}",
                                        t.Exception
                                    );
                                }
                                else if (_googleSheetsService.IsAuthenticated)
                                {
                                    _logService.Info("Kimlik doğrulama başarılı.");
                                }
                                else
                                {
                                    _logService.Info(
                                        "Kimlik doğrulama: Servis hesabı JSON mevcut, ancak kimlik doğrulama başarısız (hata olmadan)."
                                    );
                                }
                            },
                            TaskScheduler.FromCurrentSynchronizationContext()
                        );
                }
                else
                {
                    _logService.Info("Servis hesabı JSON yapılandırılmamış. Kimlik doğrulama atlanıyor.");
                }


                GoogleSheetsPanelVm = new GoogleSheetsPanelViewModel(_googleSheetsService, _logService, _appSettings, _nlp, _localEmbedder);
                SettingsVm = new SettingsViewModel(_appSettings, _googleSheetsService, _geminiService, _logService);

                CeviriVm = new CeviriViewModel(_googleSheetsService, GoogleSheetsPanelVm, _geminiService, _anthropicService, _logService, _appSettings);
                BaglamVm = new BaglamViewModel(_appSettings, _crawlerService, _logService, _geminiService, _googleSheetsService, GoogleSheetsPanelVm, _nlp, _localEmbedder);
                TerminolojiVm = new TerminolojiViewModel(_googleSheetsService, GoogleSheetsPanelVm, CeviriVm, _geminiService, _logService, _appSettings, _nlp, _localEmbedder);
                this.DataContext = this;

                App? currentApp = (App?)Application.Current;
                if (currentApp != null)
                {
                    IsDarkTheme = currentApp.ActualThemeVariant == ThemeVariant.Dark;
                }

#if DEBUG
                this.AttachDevTools();
#endif
            }
            catch (Exception ex)
            {
                _logService?.Error("Uygulama başlatılırken bir hata oluştu", ex);
                throw;
            }
        }

        private void ApplyTheme()
        {
            App? app = (App?)Application.Current;
            if (app != null)
            {
                app.RequestedThemeVariant = IsDarkTheme ? ThemeVariant.Dark : ThemeVariant.Light;
            }
        }

        // Event handlers for ThemeToggle (can be removed from XAML if fully bound and handled by IsDarkTheme setter)
        // Or, keep them and they will update the IsDarkTheme property.
        private void ThemeToggle_Checked(object sender, RoutedEventArgs e)
        {
            IsDarkTheme = true;
        }

        private void ThemeToggle_Unchecked(object sender, RoutedEventArgs e)
        {
            IsDarkTheme = false;
        }

        public new event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
