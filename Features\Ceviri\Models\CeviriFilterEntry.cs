using System.Collections.Generic;
using CommunityToolkit.Mvvm.ComponentModel;

namespace TranslationAgent.Features.Ceviri.Models
{
    /// <summary>
    /// <PERSON>ev<PERSON> modülü için filtre girişi
    /// </summary>
    public partial class CeviriFilterEntry : ObservableObject
    {
        [ObservableProperty]
        private string _columnName = string.Empty;

        [ObservableProperty]
        private string _filterValue = string.Empty;

        [ObservableProperty]
        private string _selectedOperator = "=";

        [ObservableProperty]
        private string _selectedType = "String";

        private static readonly Dictionary<string, List<string>> TypeOperators = new()
        {
            ["String"] = new() { "=", "!=", "Contains", "Not Contains", "Regex", "Not Regex" },
            ["Number"] = new() { "=", "!=", ">", ">=", "<", "<=" },
            ["Boolean"] = new() { "=", "!=" }
        };

        public List<string> AvailableOperators => TypeOperators.ContainsKey(SelectedType)
            ? TypeOperators[SelectedType]
            : TypeOperators["String"];

        public static List<string> AvailableTypes => new() { "String", "Number", "Boolean" };

        partial void OnSelectedTypeChanged(string value)
        {
            if (TypeOperators.ContainsKey(value) && !TypeOperators[value].Contains(SelectedOperator))
            {
                SelectedOperator = TypeOperators[value][0];
            }
            OnPropertyChanged(nameof(AvailableOperators));
        }
    }
}
