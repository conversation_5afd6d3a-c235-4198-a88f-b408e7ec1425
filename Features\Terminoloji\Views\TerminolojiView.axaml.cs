using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using TranslationAgent.Features.Terminoloji.Models;

namespace TranslationAgent.Features.Terminoloji.Views
{
    public partial class TerminolojiView : UserControl
    {
        public TerminolojiView()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        private void TerminologyDataGrid_LoadingRow(object? sender, DataGridRowEventArgs e)
        {
            if (e.Row.DataContext is TermViewModel term)
            {
                // Önce varolan tooltip'i temizle
                ToolTip.SetTip(e.Row, null);

                // Sadece Bilgi alanı dolu ise tooltip göster
                if (!string.IsNullOrWhiteSpace(term.Bilgi?.Trim()))
                {
                    var textBlock = new TextBlock
                    {
                        Text = term.Bilgi,
                        MaxWidth = 300,
                        TextWrapping = Avalonia.Media.TextWrapping.Wrap
                    };

                    ToolTip.SetTip(e.Row, textBlock);
                    ToolTip.SetShowDelay(e.Row, 500);
                }
            }
        }
    }
}