using System;
using System.Collections.ObjectModel;
using System.Globalization;
using Avalonia.Data.Converters;

namespace TranslationAgent.Converters
{
    public class StringCollectionContainsConverter : IValueConverter
    {
        private static StringCollectionContainsConverter? _instance;
        public static StringCollectionContainsConverter Instance => _instance ??= new StringCollectionContainsConverter();

        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is ObservableCollection<string> collection && parameter is string item)
            {
                return collection.Contains(item);
            }
            return false;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            // OneWay binding kullanıldığı için ConvertBack gerekli değil
            // Checkbox değişiklikleri ToggleTextContextColumnCommand ile yapılıyor
            return Avalonia.Data.BindingOperations.DoNothing;
        }
    }
}