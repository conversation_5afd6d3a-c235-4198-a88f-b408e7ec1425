# Avalonia .NET Masaüstü Uygulaması

Bu proje, Avalonia UI ile oluşturulmuş bir .NET masaüstü uygulamasıdır. Üstte sekmeli bir üst bar (TabControl) ile iki örnek sekme içermektedir.

## Başlatma

Projeyi başlatmak için aşağıdaki komutu kullanabilirsiniz:

```
dotnet run
```

## Özellikler
- Avalonia UI tabanlı modern arayüz
- Üstte sekmeli (TabControl) üst bar

## Geliştirici Notları
- Sekmeler ve içerikleri `MainWindow.axaml` dosyasında tanımlanmıştır.

## To Do Listesi
- Terimce listesi silme ve düzenleme özelliği eklenecek.
- Benzer terimlere bağlam özelliği eklenecek
- Stage 2 de eklenen terimler lemmatize edilecek. 