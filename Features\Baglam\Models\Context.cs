using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using SmartComponents.LocalEmbeddings;

namespace TranslationAgent.Features.Baglam.Models
{
    public class Context : INotifyPropertyChanged
    {
        private int _id;
        private string? _kategori;
        private string? _altBaslik;
        private string? _icerik;
        private string? _url;
        private List<string>? _keywords;
        private EmbeddingF32? _vector;
        private string? _lemma;

        public int ID
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public string? Kategori
        {
            get => _kategori;
            set => SetProperty(ref _kategori, value);
        }

        public string? AltBaslik
        {
            get => _altBaslik;
            set => SetProperty(ref _altBaslik, value);
        }

        public string? Icerik
        {
            get => _icerik;
            set => SetProperty(ref _icerik, value);
        }

        public string? URL
        {
            get => _url;
            set => SetProperty(ref _url, value);
        }

        public List<string>? Keywords
        {
            get => _keywords;
            set => SetProperty(ref _keywords, value);
        }

        // SharpVector veritabanı için Id
        public EmbeddingF32? Vector
        {
            get => _vector;
            set => SetProperty(ref _vector, value);
        }

        public string? Lemma
        {
            get => _lemma;
            set => SetProperty(ref _lemma, value);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        // Equals ve GetHashCode override - Context nesnelerinin değer eşitliği için
        public override bool Equals(object? obj)
        {
            if (obj is not Context other)
                return false;

            if (ReferenceEquals(this, other))
                return true;

            return string.Equals(Kategori, other.Kategori, StringComparison.OrdinalIgnoreCase) &&
                   string.Equals(AltBaslik, other.AltBaslik, StringComparison.OrdinalIgnoreCase) &&
                   string.Equals(Icerik, other.Icerik, StringComparison.OrdinalIgnoreCase) &&
                   string.Equals(URL, other.URL, StringComparison.OrdinalIgnoreCase) &&
                   KeywordsEqual(Keywords, other.Keywords);
        }

        public override int GetHashCode()
        {
            var hash = new HashCode();
            hash.Add(Kategori?.ToLowerInvariant());
            hash.Add(AltBaslik?.ToLowerInvariant());
            hash.Add(Icerik?.ToLowerInvariant());
            hash.Add(URL?.ToLowerInvariant());

            if (Keywords != null)
            {
                foreach (var keyword in Keywords.OrderBy(k => k, StringComparer.OrdinalIgnoreCase))
                {
                    hash.Add(keyword?.ToLowerInvariant());
                }
            }

            return hash.ToHashCode();
        }

        // Keywords listelerini karşılaştırmak için yardımcı metod
        private bool KeywordsEqual(List<string>? list1, List<string>? list2)
        {
            if (list1 == null && list2 == null)
                return true;

            if (list1 == null || list2 == null)
                return false;

            if (list1.Count != list2.Count)
                return false;

            var sorted1 = list1.OrderBy(k => k, StringComparer.OrdinalIgnoreCase).ToList();
            var sorted2 = list2.OrderBy(k => k, StringComparer.OrdinalIgnoreCase).ToList();

            for (int i = 0; i < sorted1.Count; i++)
            {
                if (!string.Equals(sorted1[i], sorted2[i], StringComparison.OrdinalIgnoreCase))
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Bağlam sayfası sütun başlıklarının geçerliliğini kontrol eder
        /// </summary>
        /// <param name="headers">Sütun başlıkları</param>
        /// <returns>Validation sonucu</returns>
        public static (bool IsValid, string ErrorMessage, bool HasLemma) ValidateHeaders(List<string> headers)
        {
            var upperHeaders = headers.Select(h => h.ToUpper(new System.Globalization.CultureInfo("tr-TR"))).ToList();

            // Gerekli sütunları kontrol et
            var requiredColumns = new[] { "#", "KATEGORİ", "ALT BAŞLIK", "İÇERİK", "URL", "KEYWORDS" };
            var missingColumns = requiredColumns.Where(col => !upperHeaders.Contains(col)).ToList();

            if (missingColumns.Any())
            {
                return (false, $"Gerekli sütunlar eksik: {string.Join(", ", missingColumns)}", false);
            }

            // Opsiyonel sütunları kontrol et
            bool hasLemma = upperHeaders.Contains("LEMMA");

            return (true, string.Empty, hasLemma);
        }
    }
}