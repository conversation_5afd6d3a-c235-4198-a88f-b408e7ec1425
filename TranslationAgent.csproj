<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
      <LocalEmbeddingsModelUrl>https://huggingface.co/sentence-transformers/all-MiniLM-L6-v2/resolve/main/onnx/model.onnx</LocalEmbeddingsModelUrl>
  <LocalEmbeddingsVocabUrl>https://huggingface.co/sentence-transformers/all-MiniLM-L6-v2/resolve/main/vocab.txt</LocalEmbeddingsVocabUrl>
  </PropertyGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Abot" Version="2.0.70" />
    <PackageReference Include="Anthropic.SDK" Version="5.4.1" />
    <PackageReference Include="Avalonia" Version="11.3.1" />
    <PackageReference Include="Avalonia.Desktop" Version="11.3.1" />
    <PackageReference Include="Avalonia.ReactiveUI" Version="11.3.1" />
    <PackageReference Include="Avalonia.Themes.Fluent" Version="11.3.1" />
    <PackageReference Include="Avalonia.Fonts.Inter" Version="11.3.1" />
    <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
    <PackageReference Include="Avalonia.Diagnostics" Version="11.3.1">
      <IncludeAssets Condition="'$(Configuration)' != 'Debug'">None</IncludeAssets>
      <PrivateAssets Condition="'$(Configuration)' != 'Debug'">All</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Build5Nines.SharpVector" Version="2.1.1" />
    <PackageReference Include="Catalyst" Version="1.0.54164" />
    <PackageReference Include="Catalyst.Models.English" Version="1.0.30952" />
    <PackageReference Include="CloudflareSolverRe" Version="1.0.6" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="Google.Apis.Drive.v3" Version="1.69.0.3783" />
    <PackageReference Include="Google.Apis.Sheets.v4" Version="1.69.0.3785" />
    <PackageReference Include="Avalonia.Controls.DataGrid" Version="11.3.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.6" />
    <PackageReference Include="Mosaik.Core" Version="25.5.58373" />
    <PackageReference Include="Mscc.GenerativeAI" Version="2.6.3" />
    <PackageReference Include="SmartComponents.LocalEmbeddings" Version="0.1.0-preview10148" />
    <PackageReference Include="SmartComponents.LocalEmbeddings.SemanticKernel" Version="0.1.0-preview10148" />
    <PackageReference Include="System.Linq.Async" Version="6.0.1" />
    <PackageReference Include="TextStatistics" Version="1.0.5" />
  </ItemGroup>

</Project> 