{
    "files.watcherExclude": {
        "**/node_modules/**": true,
        "**/bin/**": true,
        "**/obj/**": true
    },
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    "editor.formatOnSave": true,
    "omnisharp.enableEditorConfigSupport": true,
    "omnisharp.enableAsyncCompletion": true,
    "omnisharp.useModernNet": true,
    "dotnet.backgroundAnalysis.analyzerDiagnosticsScope": "openFiles",
}