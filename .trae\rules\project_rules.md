Bu projede Avalonia UI ile .NET masaüstü uygulaması geliştirilmektedir.

# Genel Yönergeler
- G<PERSON><PERSON><PERSON>, sür<PERSON>me özgü belgeleri ve kod örneklerini doğrudan çekmek için Context7 MCP aracını kullanabilirsiniz.
- Hata ayıklama için vscode-diagnostics MCP araçlarını kullanın.

# Hata Ayıklama
- Her dosya düzenleme ve oluşturma etkinliğinden sonra, vscode-diagnostics MCP ile dosyanın sorunlarını tespit et.
- vscode-diagnostics/getProblems aracı ile düzenlediğin veya oluşturduğun dosyayı ve ERROR seviyesindeki sorunları filtrele.
- Hataları düzelttikten sonra, sorunun çözülüp çözülmediğini belirlemek için tekrar analiz et.
- Gerekli tüm işlemleri tama<PERSON>ı<PERSON> sonra, HER ZAMAN projeyi derle. <PERSON><PERSON> verirse, sorunları çözmeye çalış.
