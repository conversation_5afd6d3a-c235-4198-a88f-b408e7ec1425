using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using Avalonia;
using CommunityToolkit.Mvvm.ComponentModel;
using TranslationAgent.Services;

public enum AIModel
{
    GeminiFlash2_5_Lite,
    GeminiFlash2_0,
    GeminiFlash2_5,
    Claude35Sonnet,
    Claude37Sonnet,
    Claude4Sonnet
}

public enum TranslationQuality
{
    Free,
    Eco,
    Full
}

public class GeminiApiKey
{
    public string Key { get; set; }
    public int DailyQuotaFlash2_0 { get; set; }
    public int DailyQuotaFlash2_5 { get; set; }
    public int RemainingQuotaFlash2_0 { get; set; }
    public int RemainingQuotaFlash2_5 { get; set; }
    public DateTime LastResetDate { get; set; }

    public GeminiApiKey()
    {
        LastResetDate = DateTime.Today;
        ResetQuotasIfNewDay();
    }

    public void ResetQuotasIfNewDay()
    {
        if (LastResetDate.Date != DateTime.Today)
        {
            RemainingQuotaFlash2_0 = DailyQuotaFlash2_0;
            RemainingQuotaFlash2_5 = DailyQuotaFlash2_5;
            LastResetDate = DateTime.Today;
        }
    }
}

public class GeneralSettings
{
    public string ServiceAccountJson { get; set; }
    public List<GeminiApiKey> GeminiApiKeys { get; set; } = new List<GeminiApiKey>();
    public string ClaudeApiKey { get; set; }
    public string SourceTextColumnHeader { get; set; } = "EN";
    public string TextStatusColumnHeader { get; set; } = "EN-TR";
    public bool HardLog { get; set; } = false;
    public Array AvailableAIModels => Enum.GetValues(typeof(AIModel));
}

public class TerminologySettings : ObservableObject
{
    private string _stage1Prompt = "Lütfen aşağıdaki metni Türkçe'ye çevirin. Teknik terimleri ve özel isimleri koruyun.";
    private string _stage1Function = "translate_to_turkish";
    private int _stage1BatchSize = 1;
    private AIModel _stage1AIModel = AIModel.GeminiFlash2_0;

    private string _stage2Prompt = "Lütfen çeviriyi düzeltin ve daha doğal bir Türkçe kullanın.";
    private string _stage2Function = "improve_translation";
    private int _stage2BatchSize = 1;
    private AIModel _stage2AIModel = AIModel.GeminiFlash2_0;

    private string _stage3Prompt = "Lütfen çeviriyi son kez gözden geçirin ve dilbilgisi, yazım ve noktalama hatalarını düzeltin.";
    private string _stage3Function = "final_review";
    private int _stage3BatchSize = 1;
    private AIModel _stage3AIModel = AIModel.GeminiFlash2_0;

    public string Stage1Prompt
    {
        get => _stage1Prompt;
        set => SetProperty(ref _stage1Prompt, value);
    }

    public string Stage1Function
    {
        get => _stage1Function;
        set => SetProperty(ref _stage1Function, value);
    }

    public int Stage1BatchSize
    {
        get => _stage1BatchSize;
        set => SetProperty(ref _stage1BatchSize, value);
    }

    public AIModel Stage1AIModel
    {
        get => _stage1AIModel;
        set => SetProperty(ref _stage1AIModel, value);
    }

    public string Stage2Prompt
    {
        get => _stage2Prompt;
        set => SetProperty(ref _stage2Prompt, value);
    }

    public string Stage2Function
    {
        get => _stage2Function;
        set => SetProperty(ref _stage2Function, value);
    }

    public int Stage2BatchSize
    {
        get => _stage2BatchSize;
        set => SetProperty(ref _stage2BatchSize, value);
    }

    public AIModel Stage2AIModel
    {
        get => _stage2AIModel;
        set => SetProperty(ref _stage2AIModel, value);
    }

    public string Stage3Prompt
    {
        get => _stage3Prompt;
        set => SetProperty(ref _stage3Prompt, value);
    }

    public string Stage3Function
    {
        get => _stage3Function;
        set => SetProperty(ref _stage3Function, value);
    }

    public int Stage3BatchSize
    {
        get => _stage3BatchSize;
        set => SetProperty(ref _stage3BatchSize, value);
    }

    public AIModel Stage3AIModel
    {
        get => _stage3AIModel;
        set => SetProperty(ref _stage3AIModel, value);
    }


}

public class TranslationSettings : ObservableObject
{
    private AIModel _aiModel = AIModel.GeminiFlash2_5;
    private string _prompt = "Lütfen aşağıdaki metni Türkçe'ye çevirin. Teknik terimleri ve özel isimleri koruyun. Çeviri doğal ve akıcı olmalıdır.";
    private string _prompt2 = "Lütfen çeviriyi gözden geçirin ve daha doğal bir Türkçe kullanarak iyileştirin.";
    private string _function = "translate_text";
    private int _batchSize = 5;
    private TranslationQuality _quality = TranslationQuality.Eco;

    public AIModel AIModel
    {
        get => _aiModel;
        set => SetProperty(ref _aiModel, value);
    }

    public string Prompt
    {
        get => _prompt;
        set => SetProperty(ref _prompt, value);
    }

    public string Prompt2
    {
        get => _prompt2;
        set => SetProperty(ref _prompt2, value);
    }

    public string Function
    {
        get => _function;
        set => SetProperty(ref _function, value);
    }

    public int BatchSize
    {
        get => _batchSize;
        set => SetProperty(ref _batchSize, value);
    }

    public TranslationQuality Quality
    {
        get => _quality;
        set => SetProperty(ref _quality, value);
    }
}

public class ContextSettings : ObservableObject
{
    private AIModel _defaultAIModel = AIModel.GeminiFlash2_5;
    private int _defaultPageCount = 1;
    private int _defaultMaxDepth = 1;
    private string _prompt = "Lütfen aşağıdaki metni Türkçe'ye çevirin. Teknik terimleri ve özel isimleri koruyun.";
    private string _contextFunction = "translate_context";

    public AIModel DefaultAIModel
    {
        get => _defaultAIModel;
        set => SetProperty(ref _defaultAIModel, value);
    }

    public int DefaultPageCount
    {
        get => _defaultPageCount;
        set => SetProperty(ref _defaultPageCount, value);
    }

    public int DefaultMaxDepth
    {
        get => _defaultMaxDepth;
        set => SetProperty(ref _defaultMaxDepth, value);
    }

    public string Prompt
    {
        get => _prompt;
        set => SetProperty(ref _prompt, value);
    }

    public string ContextFunction
    {
        get => _contextFunction;
        set => SetProperty(ref _contextFunction, value);
    }
}

public class AppSettings
{
    private static LogService? _logService;
    public GeneralSettings General { get; set; } = new GeneralSettings();
    public TerminologySettings Terminology { get; set; } = new TerminologySettings();
    public TranslationSettings Translation { get; set; } = new TranslationSettings();
    public ContextSettings Context { get; set; } = new ContextSettings();

    public static void InitializeLogService(LogService logService)
    {
        _logService = logService;
    }

    private static string GetSettingsFilePath()
    {
        // Store settings in a local file. Adjust path as needed.
        // For a real app, consider Environment.SpecialFolder.ApplicationData
        return Path.Combine(Directory.GetCurrentDirectory(), "appsettings.json");
    }

    public void Save()
    {
        try
        {
            var options = new JsonSerializerOptions { WriteIndented = true };
            var json = JsonSerializer.Serialize(this, options);
            File.WriteAllText(GetSettingsFilePath(), json);
            _logService?.Info("Ayarlar başarıyla kaydedildi.");
        }
        catch (Exception ex)
        {
            _logService?.Error("Ayarlar kaydedilirken bir hata oluştu.", ex);
            throw;
        }
    }

    private bool HasChanges(AppSettings other)
    {
        var options = new JsonSerializerOptions { WriteIndented = true };
        var thisJson = JsonSerializer.Serialize(this, options);
        var otherJson = JsonSerializer.Serialize(other, options);
        return thisJson != otherJson;
    }

    public static AppSettings Load()
    {
        var filePath = GetSettingsFilePath();
        var defaultSettings = new AppSettings();
        var originalSettings = defaultSettings;

        if (!File.Exists(filePath))
        {
            _logService?.Warning("appsettings.json dosyası bulunamadı. Varsayılan ayarlar oluşturuluyor.");
            defaultSettings.Save();
            return defaultSettings;
        }

        try
        {
            var json = File.ReadAllText(filePath);
            originalSettings = JsonSerializer.Deserialize<AppSettings>(json) ?? defaultSettings;
            var settings = JsonSerializer.Deserialize<AppSettings>(json);

            if (settings == null)
            {
                _logService?.Error("appsettings.json boş bir nesneye dönüştürüldü.", null);
                return defaultSettings;
            }

            // General ayarlarını kontrol et
            if (settings.General == null)
            {
                _logService?.Warning("General ayarları eksik. Varsayılan değerler kullanılıyor.");
                settings.General = defaultSettings.General;
            }
            else
            {
                // GeminiApiKeys kontrolü
                if (settings.General.GeminiApiKeys == null)
                {
                    _logService?.Warning("GeminiApiKeys null. Boş liste olarak başlatılıyor.");
                    settings.General.GeminiApiKeys = new List<GeminiApiKey>();
                }

                // SourceTextColumnHeader kontrolü
                if (string.IsNullOrEmpty(settings.General.SourceTextColumnHeader))
                {
                    settings.General.SourceTextColumnHeader = defaultSettings.General.SourceTextColumnHeader;
                }

                // TextStatusColumnHeader kontrolü
                if (string.IsNullOrEmpty(settings.General.TextStatusColumnHeader))
                {
                    settings.General.TextStatusColumnHeader = defaultSettings.General.TextStatusColumnHeader;
                }
            }

            // Terminology ayarlarını kontrol et
            if (settings.Terminology == null)
            {
                _logService?.Warning("Terminology ayarları eksik. Varsayılan değerler kullanılıyor.");
                settings.Terminology = defaultSettings.Terminology;
            }
            else
            {
                // Stage1Prompt kontrolü
                if (string.IsNullOrEmpty(settings.Terminology.Stage1Prompt))
                {
                    settings.Terminology.Stage1Prompt = defaultSettings.Terminology.Stage1Prompt;
                }

                // Stage1Function kontrolü
                if (string.IsNullOrEmpty(settings.Terminology.Stage1Function))
                {
                    settings.Terminology.Stage1Function = defaultSettings.Terminology.Stage1Function;
                }

                // Stage2Prompt kontrolü
                if (string.IsNullOrEmpty(settings.Terminology.Stage2Prompt))
                {
                    settings.Terminology.Stage2Prompt = defaultSettings.Terminology.Stage2Prompt;
                }

                // Stage2Function kontrolü
                if (string.IsNullOrEmpty(settings.Terminology.Stage2Function))
                {
                    settings.Terminology.Stage2Function = defaultSettings.Terminology.Stage2Function;
                }

                // BatchSize kontrolü (0 veya negatif değerler için varsayılan değer kullan)
                if (settings.Terminology.Stage1BatchSize <= 0)
                {
                    settings.Terminology.Stage1BatchSize = defaultSettings.Terminology.Stage1BatchSize;
                }
                if (settings.Terminology.Stage2BatchSize <= 0)
                {
                    settings.Terminology.Stage2BatchSize = defaultSettings.Terminology.Stage2BatchSize;
                }

                // Stage3Prompt kontrolü
                if (string.IsNullOrEmpty(settings.Terminology.Stage3Prompt))
                {
                    settings.Terminology.Stage3Prompt = defaultSettings.Terminology.Stage3Prompt;
                }

                // Stage3Function kontrolü
                if (string.IsNullOrEmpty(settings.Terminology.Stage3Function))
                {
                    settings.Terminology.Stage3Function = defaultSettings.Terminology.Stage3Function;
                }

                // Stage3BatchSize kontrolü (0 veya negatif değerler için varsayılan değer kullan)
                if (settings.Terminology.Stage3BatchSize <= 0)
                {
                    settings.Terminology.Stage3BatchSize = defaultSettings.Terminology.Stage3BatchSize;
                }
            }

            // Translation ayarlarını kontrol et
            if (settings.Translation == null)
            {
                _logService?.Warning("Translation ayarları eksik. Varsayılan değerler kullanılıyor.");
                settings.Translation = defaultSettings.Translation;
            }
            else
            {
                // Prompt kontrolü
                if (string.IsNullOrEmpty(settings.Translation.Prompt))
                {
                    settings.Translation.Prompt = defaultSettings.Translation.Prompt;
                }

                // Prompt2 kontrolü
                if (string.IsNullOrEmpty(settings.Translation.Prompt2))
                {
                    settings.Translation.Prompt2 = defaultSettings.Translation.Prompt2;
                }

                // Function kontrolü
                if (string.IsNullOrEmpty(settings.Translation.Function))
                {
                    settings.Translation.Function = defaultSettings.Translation.Function;
                }

                // BatchSize kontrolü (0 veya negatif değerler için varsayılan değer kullan)
                if (settings.Translation.BatchSize <= 0)
                {
                    settings.Translation.BatchSize = defaultSettings.Translation.BatchSize;
                }
            }

            // Context ayarlarını kontrol et
            if (settings.Context == null)
            {
                _logService?.Warning("Context ayarları eksik. Varsayılan değerler kullanılıyor.");
                settings.Context = defaultSettings.Context;
            }
            else
            {
                // DefaultPageCount kontrolü (0 veya negatif değerler için varsayılan değer kullan)
                if (settings.Context.DefaultPageCount <= 0)
                {
                    settings.Context.DefaultPageCount = defaultSettings.Context.DefaultPageCount;
                }

                // Prompt kontrolü
                if (string.IsNullOrEmpty(settings.Context.Prompt))
                {
                    settings.Context.Prompt = defaultSettings.Context.Prompt;
                }

                // ContextFunction kontrolü
                if (string.IsNullOrEmpty(settings.Context.ContextFunction))
                {
                    settings.Context.ContextFunction = defaultSettings.Context.ContextFunction;
                }
            }

            // Gemini API anahtarlarının kotalarını kontrol et
            foreach (var key in settings.General.GeminiApiKeys)
            {
                key.ResetQuotasIfNewDay();
            }

            _logService?.Success("Ayarlar başarıyla yüklendi.");

            // Sadece değişiklik varsa kaydet
            if (settings.HasChanges(originalSettings))
            {
                _logService?.Info("Ayarlarda değişiklik tespit edildi, kaydediliyor...");
                settings.Save();
            }

            return settings;
        }
        catch (JsonException ex)
        {
            _logService?.Error("appsettings.json dosyası ayrıştırılırken hata oluştu.", ex);
            return defaultSettings;
        }
        catch (Exception ex)
        {
            _logService?.Error("appsettings.json yüklenirken beklenmeyen hata.", ex);
            return defaultSettings;
        }
    }

    public void AddGeminiApiKey(string key, int quotaFlash2_0, int quotaFlash2_5)
    {
        try
        {
            General.GeminiApiKeys.Add(new GeminiApiKey
            {
                Key = key,
                DailyQuotaFlash2_0 = quotaFlash2_0,
                DailyQuotaFlash2_5 = quotaFlash2_5,
                RemainingQuotaFlash2_0 = quotaFlash2_0,
                RemainingQuotaFlash2_5 = quotaFlash2_5,
                LastResetDate = DateTime.Today
            });
            _logService?.Info($"Yeni Gemini API anahtarı başarıyla eklendi.");
            Save();
        }
        catch (Exception ex)
        {
            _logService?.Error("Gemini API anahtarı eklenirken hata oluştu.", ex, true);
            throw;
        }
    }

    public bool UseQuota(string key, bool isFlash2_5)
    {
        var apiKey = General.GeminiApiKeys.Find(k => k.Key == key);
        if (apiKey == null)
        {
            _logService?.Warning($"API anahtarı bulunamadı: {key}", true);
            return false;
        }

        apiKey.ResetQuotasIfNewDay();

        if (isFlash2_5)
        {
            if (apiKey.RemainingQuotaFlash2_5 > 0)
            {
                apiKey.RemainingQuotaFlash2_5--;
                Save();
                _logService?.Info($"Flash 2.5 kullanıldı. Kalan: {apiKey.RemainingQuotaFlash2_5}", true);
                return true;
            }
            _logService?.Warning("Flash 2.5 kotası tükendi.", true);
        }
        else
        {
            if (apiKey.RemainingQuotaFlash2_0 > 0)
            {
                apiKey.RemainingQuotaFlash2_0--;
                Save();
                _logService?.Info($"Flash 2.0 kullanıldı. Kalan: {apiKey.RemainingQuotaFlash2_0}", true);
                return true;
            }
            _logService?.Warning("Flash 2.0 kotası tükendi.", true);
        }
        return false;
    }
}