{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "netcoredbg",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/bin/Debug/net9.0/TranslationAgent.dll",
            "args": [],
            "stopAtEntry": true,
            "internalConsoleOptions": "openOnSessionStart",
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "pipeTransport": {
                "pipeProgram": "powershell",
                "pipeArgs": [
                    "-Command",
                    "chcp 65001 | Out-Null;"
                ],
                "debuggerPath": "${workspaceFolder}/.netcoredbg/netcoredbg",
                "debuggerArgs": [
                    "--interpreter=vscode"
                ],
                "quoteArgs": true
            },
            "env": {
                "DOTNET_ENVIRONMENT": "Development"
            },
        }
    ],
}