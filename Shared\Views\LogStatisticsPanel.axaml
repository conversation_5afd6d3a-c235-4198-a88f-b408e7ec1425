<UserControl
    x:Class="TranslationAgent.Shared.Views.LogStatisticsPanel"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:TranslationAgent.Shared.ViewModels"
    d:DesignHeight="200"
    d:DesignWidth="800"
    x:DataType="vm:LogStatisticsPanelViewModel"
    mc:Ignorable="d">

    <Design.DataContext>
        <vm:LogStatisticsPanelViewModel />
    </Design.DataContext>

    <!--  Log ve İstatistik Paneli  -->
    <Grid MinHeight="100" ColumnDefinitions="*, Auto">
        <!--  Sütun 0: Log Paneli  -->
        <Border
            Padding="5"
            BorderBrush="Gray"
            BorderThickness="0,1,0,0">
            <ScrollViewer Name="LogScrollViewer">                
                <TextBox
                    Padding="0"
                    AcceptsReturn="True"
                    Background="Transparent"
                    BorderThickness="0"
                    Focusable="True"
                    IsReadOnly="True"
                    ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                    ScrollViewer.VerticalScrollBarVisibility="Disabled"
                    Text="{Binding LogText}"
                    TextWrapping="Wrap" />
            </ScrollViewer>
        </Border>

        <!--  Sütun 1: İstatistik Alanı  -->
        <Border
            Grid.Column="1"
            MinWidth="250"
            Padding="10"
            Background="{DynamicResource SystemControlMidHighColorBrush}"
            BorderBrush="Gray"
            BorderThickness="1,1,0,0">
            <StackPanel Spacing="5">
                <TextBlock
                    Margin="0,0,0,0"
                    HorizontalAlignment="Center"
                    FontSize="15"
                    FontWeight="Bold"
                    Text="{Binding StatisticsTitle}" />

                <!--  Dinamik İstatistik Öğeleri  -->
                <ItemsControl ItemsSource="{Binding StatisticItems}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Grid Margin="0,2" ColumnDefinitions="Auto, *">
                                <TextBlock
                                    Grid.Column="0"
                                    Margin="0,0,5,0"
                                    FontWeight="SemiBold"
                                    Text="{Binding Label}" />
                                <TextBlock
                                    Grid.Column="1"
                                    HorizontalAlignment="Right"
                                    Text="{Binding Value}" />
                            </Grid>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>

                <!--  Progress Label  -->
                <TextBlock
                    Margin="0,0,0,0"
                    FontWeight="SemiBold"
                    IsVisible="{Binding ShowProgressBar}"
                    Text="{Binding ProgressLabel}" />
                <ProgressBar
                    Height="20"
                    Margin="0,5,0,0"
                    IsVisible="{Binding ShowProgressBar}"
                    Maximum="100"
                    Minimum="0"
                    Value="{Binding OverallProgress}" />
                <TextBlock
                    HorizontalAlignment="Center"
                    IsVisible="{Binding ShowProgressBar}"
                    Text="{Binding OverallProgress, StringFormat={}{0:F1}%}" />
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
