using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Catalyst;
using Mosaik.Core;
using TranslationAgent.Features.GoogleSheets.ViewModels;
using TranslationAgent.Features.Terminoloji.Models;
using TranslationAgent.Features.Ceviri.Models;
using TranslationAgent.Services;
using TranslationAgent.Helpers;
using System.Text.Json;

namespace TranslationAgent.Features.Terminoloji.ViewModels
{
    public class PhaseTwoProcessor
    {
        private readonly TerminolojiViewModel _viewModel;

        private CancellationToken _cancellationToken;
        private string _sistemPrompt = string.Empty;

        public PhaseTwoProcessor(
            TerminolojiViewModel viewModel)
        {
            _viewModel = viewModel;
        }

        public async Task ProcessAsync()
        {
            _cancellationToken = _viewModel.CancellationToken;

            var termsToCheck = PrepareTermsToCheck();
            if (termsToCheck == null || !termsToCheck.Any())
            {
                _viewModel.LogService.Info("İncelenecek terim bulunamadı.", true, LogService.LogState.Stage2);
                return;
            }

            var batches = CreateBatches(termsToCheck);
            _viewModel.LogService.Info($"{_viewModel.AppSettings.Terminology.Stage2Prompt}", false, LogService.LogState.Stage2);
            await ProcessBatches(batches);
        }

        private List<TermViewModel> PrepareTermsToCheck()
        {
            _viewModel.LogService.Info("Terimler incelenmek için hazırlanıyor...", true, LogService.LogState.Stage2);
            _sistemPrompt = _viewModel.AppSettings.Terminology.Stage2Prompt.Replace("[Ana Bağlam]", _viewModel.GoogleSheetsPanelVm.MainContext?.Icerik ?? "(YOK)");
            var termsToTranslate = new List<TermViewModel>();
            foreach (var row in _viewModel.GoogleSheetsPanelVm.TerminologyData)
            {
                _cancellationToken.ThrowIfCancellationRequested();

                if (row.Status == TermStatus.İncelemede)
                {
                    termsToTranslate.Add(row);
                }
            }
            // İstatistikleri güncelle
            _viewModel.TotalItemsToProcess = termsToTranslate.Count;
            _viewModel.ItemsProcessedCount = 0;
            _viewModel.UpdateStatistics();
            _viewModel.LogStatisticsPanelVm.SetProgressBar(0);
            _viewModel.LogService.Info($"İncelencek terim sayısı: {termsToTranslate.Count}", true, LogService.LogState.Stage2);
            return termsToTranslate;
        }

        private List<List<TermViewModel>> CreateBatches(List<TermViewModel> termsToCheck)
        {
            var batchSize = _viewModel.AppSettings.Terminology.Stage2BatchSize;
            _viewModel.LogService.Info($"Batch boyutu: {batchSize}", true, LogService.LogState.Stage2);
            var batches = new List<List<TermViewModel>>();
            for (int i = 0; i < termsToCheck.Count; i += batchSize)
            {
                _cancellationToken.ThrowIfCancellationRequested();
                batches.Add(termsToCheck.Skip(i).Take(batchSize).ToList());
            }
            _viewModel.LogService.Info($"Toplam Batch sayısı: {batches.Count}", true, LogService.LogState.Stage2);
            return batches;
        }

        private async Task ProcessBatches(List<List<TermViewModel>> batches)
        {
            int currentBatch = 0;
            foreach (var batch in batches)
            {
                if (!await ProcessBatch(batch, ++currentBatch, batches.Count))
                    break;
            }
        }

        private async Task<bool> ProcessBatch(List<TermViewModel> batch, int currentBatch, int totalBatches)
        {
            _viewModel.LogService.Info($"Batch {currentBatch}/{totalBatches} işleniyor...", true, LogService.LogState.Stage2);

            // Duraklat kontrolü
            await HandlePauseIfNeeded();
            _cancellationToken.ThrowIfCancellationRequested();

            try
            {
                var success = await ProcessBatchWithRetry(batch);
                if (!success)
                    return false;

                // İstatistikleri güncelle
                _viewModel.ItemsProcessedCount += batch.Count;
                _viewModel.LogService.Info($"Kullanılan Model: {_viewModel.GeminiService.GetCurrentModelInfo()}", false, LogService.LogState.Stage2);
                _viewModel.LogService.Info($"Mevcut Kota Durumu: {_viewModel.GeminiService.GetCurrentQuotaInfo()}", false, LogService.LogState.Stage2);
                _viewModel.LogService.Info($"Batch işlendi: {_viewModel.ItemsProcessedCount}/{_viewModel.TotalItemsToProcess} metin", false, LogService.LogState.Stage2);
                _viewModel.LogService.Info("------------------------------------", true, LogService.LogState.Stage2);




                // Model istatistiklerini güncelle
                _viewModel.UpdateModelQuotaStatistics();
                _viewModel.UpdateStatistics();

                await Task.Delay(100, _cancellationToken);
                return true;
            }
            catch (Exception ex)
            {
                _viewModel.LogService.Error("Batch işleme sürecinde beklenmeyen hata", ex, true, LogService.LogState.Stage2);
                return false;
            }
        }

        private List<dynamic?> GetExampleSentencesForTerm(string term, string lemmatizedTerm, int maxExamples = 3)
        {
            if (string.IsNullOrWhiteSpace(term))
                return new List<dynamic?>();

            // Regex'i önceden derleyerek performans artışı sağla
            var regexOrj = new Regex($@"\b{Regex.Escape(term)}\b", RegexOptions.IgnoreCase | RegexOptions.Compiled);
            var regexLemma = new Regex($@"\b{Regex.Escape(lemmatizedTerm)}\b", RegexOptions.IgnoreCase | RegexOptions.Compiled);

            // Sık kullanılan değerleri cache'le
            var textStatusHeader = _viewModel.AppSettings.General.TextStatusColumnHeader;
            var sourceTextHeader = _viewModel.AppSettings.General.SourceTextColumnHeader;

            var examplesOrj = _viewModel.GoogleSheetsPanelVm.TextData
                .Where(IsValidTextRow)
                .Where(x => HasValidMatch(x, regexOrj, x => x.EN))
                .Where(text => !string.IsNullOrWhiteSpace(text.EN) && !text.EN.Equals(term, StringComparison.OrdinalIgnoreCase))
                .Distinct()
                .Take(maxExamples)
                .ToList();

            if (examplesOrj.Count >= maxExamples)
                return TextProcessingHelper.ProcessExamplesWithLemma(examplesOrj, lemmatizedTerm, _viewModel.Nlp);

            var examplesLemma = _viewModel.GoogleSheetsPanelVm.TextData
                .Where(IsValidTextRow)
                .Where(x => HasValidMatch(x, regexLemma, x => x.Lemma))
                .Where(text => !string.IsNullOrWhiteSpace(text.EN) && !text.EN.Equals(term, StringComparison.OrdinalIgnoreCase) && !examplesOrj.Any(x => x.EN.Equals(text.EN, StringComparison.OrdinalIgnoreCase)))
                .Distinct()
                .Take(maxExamples - examplesOrj.Count)
                .ToList();

            var examples = TextProcessingHelper.ProcessExamplesWithLemma(examplesOrj.Concat(examplesLemma).Distinct().ToList(), lemmatizedTerm, _viewModel.Nlp);

            return examples;

            // Local helper methods for better readability and performance
            bool IsValidTextRow(Text row)
            {
                return row.IsTranslated || row.NeedsTranslation;
            }

            bool HasValidMatch(Text row, Regex compiledRegex, Func<Text, string?> valueSelector)
            {
                var value = valueSelector(row);
                return !string.IsNullOrWhiteSpace(value) && compiledRegex.IsMatch(value);
            }
        }

        private async Task<bool> ProcessBatchWithRetry(List<TermViewModel> batch)
        {
            var termsWithExamples = await Task.WhenAll(batch.Select(async row =>
            {
                var examples = GetExampleSentencesForTerm(row.EN, row.Lemma, 12);
                return new { Term = row.EN, ID = row.ID, Examples = examples };
            }));

            var promptBuilder = new System.Text.StringBuilder();

            foreach (var item in termsWithExamples)
            {
                promptBuilder.AppendLine("ID: " + item.ID.ToString());
                promptBuilder.AppendLine("Terim: " + item.Term);
                promptBuilder.AppendLine();
                if (item.Examples.Any())
                {
                    promptBuilder.AppendLine("Terimin Geçtiği Metinler:");
                    foreach (var text in item.Examples)
                    {

                        if (_viewModel.SelectedTextContextColumns.Any())
                        {
                            foreach (var column in _viewModel.SelectedTextContextColumns)
                            {
                                var contextInfo = _viewModel.GetTextContextInfo(text, column);

                                if (!string.IsNullOrEmpty(contextInfo))
                                {
                                    promptBuilder.AppendLine($"{column}: {contextInfo}");
                                }
                            }
                            promptBuilder.AppendLine($"- {text.EN}");
                            promptBuilder.AppendLine();
                        }
                        else
                        {
                            promptBuilder.AppendLine($"- {text.EN}");
                        }


                    }
                }
            }
            var prompt = promptBuilder.ToString();


            var success = false;
            var retryCount = 0;
            const int maxRetries = 5;

            while (!success && retryCount < maxRetries)
            {
                await HandlePauseIfNeeded();
                _cancellationToken.ThrowIfCancellationRequested();

                try
                {
                    _viewModel.LogService.Info($"AI'ya gönderilen terim ve örnekler:\n{prompt}", false, LogService.LogState.Stage2);
                    var response = await _viewModel.GeminiService.GenerateContentWithJsonToolsAsync(
                        _sistemPrompt,
                        prompt,
                        _viewModel.AppSettings.Terminology.Stage2Function,
                        _viewModel.AppSettings.Terminology.Stage2AIModel, LogService.LogState.Stage2, _cancellationToken
                    );
                    if (response == null)
                    {
                        throw new Exception($"AI yanıtı alınamadı!");
                    }
                    else
                    {
                        if (response.HasValue)
                        {
                            success = await ProcessGeminiResponse(response.Value);
                        }
                        else
                        {
                            success = false;
                        }
                    }

                }
                catch (OperationCanceledException)
                {
                    // İptal edildi, normal durum - hata değil
                    _viewModel.LogService.Warning("İşlem iptal edildi.", true, LogService.LogState.Stage2);
                    return false;
                }
                catch (Exception ex)
                {
                    if (ex.Message.Contains("ServiceUnavailable") && ex.Message.Contains("model is overloaded"))
                    {
                        _viewModel.LogService.Warning($"Deneme {retryCount}/{maxRetries} başarısız oldu. Model aşırı yüklendi. Tekrar deneniyor...", true, LogService.LogState.Stage1);
                    }
                    else
                    {
                        retryCount++;
                        if (retryCount < maxRetries)
                        {


                            _viewModel.LogService.Warning($"Deneme {retryCount}/{maxRetries} başarısız oldu. Tekrar deneniyor... Hata: {ex.Message}", true, LogService.LogState.Stage1);

                            try
                            {
                                await Task.Delay(1000 * retryCount, _cancellationToken);
                            }
                            catch (OperationCanceledException)
                            {
                                _viewModel.LogService.Warning("İşlem iptal edildi.", true, LogService.LogState.Stage1);
                                return false;
                            }
                        }
                        else
                        {
                            _viewModel.LogService.Error($"Batch işlenirken maksimum deneme sayısına ulaşıldı", ex, true, LogService.LogState.Stage1);
                            return false;
                        }
                    }
                }
            }
            return success;
        }

        private async Task<bool> ProcessGeminiResponse(System.Text.Json.JsonElement response)
        {
            try
            {
                _viewModel.LogService.Info($"AI yanıtı: {response.GetRawText()}", false, LogService.LogState.Stage2);



                if (!response.TryGetProperty("terimler", out var terimlerElement2) && response.ValueKind == System.Text.Json.JsonValueKind.Array)
                {
                    terimlerElement2 = response;
                }

                if (terimlerElement2.ValueKind == System.Text.Json.JsonValueKind.Array)
                {

                    foreach (var terimObj in terimlerElement2.EnumerateArray())
                    {
                        int id;
                        if (terimObj.TryGetProperty("id", out var idObj))
                            id = idObj.GetInt32();
                        else
                            id = -1;
                        var kontrol = terimObj.GetProperty("kontrol").GetBoolean();
                        var en = terimObj.GetProperty("en").GetString();
                        if (kontrol)
                        {
                            await VerifyTermToGoogleSheets(id, en);
                        }
                        else
                        {
                            _viewModel.LogService.Info($"'{en}' terimi yanlış tespit edilmiş.", true, LogService.LogState.Stage2);
                            await RemoveTermFromGoogleSheets(id);
                        }
                    }

                    return true;
                }
                else
                {
                    _viewModel.LogService.Error("AI yanıtı beklenen formatta değil (terimler bulunamadı)", null, true, LogService.LogState.Stage2);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _viewModel.LogService.Error("AI yanıtı parse edilirken hata oluştu", ex, true, LogService.LogState.Stage2);
                return false;
            }
        }

        private async Task VerifyTermToGoogleSheets(int id, string en)
        {
            var panelVm = _viewModel.GoogleSheetsPanelVm;
            var terminologyData = panelVm.TerminologyData;

            try
            {
                if (!panelVm.IsTerminologyDataFetched || string.IsNullOrWhiteSpace(panelVm.SpreadsheetId))
                {
                    _viewModel.LogService.Warning("Google Sheets bağlantısı hazır değil. Çeviri eklenemedi.", true, LogService.LogState.Stage2);
                    return;
                }

                var selectedSheet = panelVm.SelectedTerminologySheet;
                if (selectedSheet == null || string.IsNullOrWhiteSpace(selectedSheet.SheetName))
                {
                    _viewModel.LogService.Warning("Terimce sayfası seçili değil. Çeviri eklenemedi.", true, LogService.LogState.Stage2);
                    return;
                }

                var spreadsheetId = panelVm.SpreadsheetId;
                var sheetName = selectedSheet.SheetName;

                var row = terminologyData.FirstOrDefault(t => t.ID == id);

                // Lokal veriyi güncelle veya ekle
                if (row != null)
                {
                    row.Status = TermStatus.Onaylandı;

                    var existingData = await _viewModel.GoogleSheetsService.GetDataAsync(spreadsheetId, sheetName);

                    if (existingData != null)
                    {
                        var rowIndex = existingData
                            .Select((r, idx) => new { r, idx })
                            .Skip(1) // başlık satırını atla
                            .FirstOrDefault(x => x.r.Count > 0 &&
                                                 int.TryParse(x.r[0]?.ToString(), out int rid) &&
                                                 rid == row.ID)?.idx;

                        if (rowIndex.HasValue)
                        {
                            var updatedRowData = new List<object> { row.ID, row.EN, row.TR, row.Kategori, row.Bilgi, row.Status.ToString() };
                            await _viewModel.GoogleSheetsService.UpdateRowAsync(spreadsheetId, sheetName, rowIndex.Value, updatedRowData);
                            _viewModel.LogService.Info($"'{row.EN}' terimi Google Sheets'te güncellendi.", true, LogService.LogState.Stage2);
                            return;
                        }
                    }
                }
                else
                {
                    _viewModel.LogService.Info($"'{en}' terimi Google Sheets'te bulunamadı.", true, LogService.LogState.Stage2);
                }

            }
            catch (Exception ex)
            {
                _viewModel.LogService.Error("Çeviri Google Sheets'e eklenirken hata oluştu", ex, true, LogService.LogState.Stage2);
            }
        }

        private async Task RemoveTermFromGoogleSheets(int id)
        {
            try
            {
                if (!_viewModel.GoogleSheetsPanelVm.IsTerminologyDataFetched || string.IsNullOrWhiteSpace(_viewModel.GoogleSheetsPanelVm.SpreadsheetId))
                {
                    _viewModel.LogService.Warning("Google Sheets bağlantısı hazır değil. Terim silinemedi.", true, LogService.LogState.Stage2);
                    return;
                }

                var selectedSheet = _viewModel.GoogleSheetsPanelVm.SelectedTerminologySheet;
                if (selectedSheet == null || string.IsNullOrWhiteSpace(selectedSheet.SheetName))
                {
                    _viewModel.LogService.Warning("Terimce sayfası seçili değil. Terim silinemedi.", true, LogService.LogState.Stage2);
                    return;
                }

                // Önce yerel listeden kaldır
                var termToRemove = _viewModel.GoogleSheetsPanelVm.TerminologyData.FirstOrDefault(t =>
                    t.ID != null && t.ID == id);

                if (termToRemove != null)
                {
                    _viewModel.GoogleSheetsPanelVm.TerminologyData.Remove(termToRemove);
                    _viewModel.LogService.Info($"'{termToRemove.EN}' terimi listeden silindi.", true, LogService.LogState.Stage2);
                }

                // Google Sheets'ten kaldır - ID ile eşleştir
                var existingData = await _viewModel.GoogleSheetsService.GetDataAsync(_viewModel.GoogleSheetsPanelVm.SpreadsheetId, selectedSheet.SheetName);
                if (existingData != null && termToRemove != null)
                {
                    var rowIndex = existingData
                        .Select((row, idx) => new { row, idx })
                        .Skip(1) // Başlık satırını atla
                        .FirstOrDefault(x => x.row.Count > 0 &&
                                           int.TryParse(x.row[0]?.ToString(), out int id) &&
                                           id == termToRemove.ID)?.idx;

                    if (rowIndex.HasValue)
                    {
                        // Satırı sil
                        await _viewModel.GoogleSheetsService.DeleteRowAsync(_viewModel.GoogleSheetsPanelVm.SpreadsheetId, selectedSheet.SheetName, rowIndex.Value);
                        _viewModel.LogService.Info($"'{termToRemove.EN}' terimi Google Sheets'ten silindi.", true, LogService.LogState.Stage2);
                    }
                }
            }
            catch (Exception ex)
            {
                _viewModel.LogService.Error($"'{id}' ID'li terimi silinirken hata oluştu", ex, true, LogService.LogState.Stage2);
            }
        }

        private async Task HandlePauseIfNeeded()
        {
            if (_viewModel.IsPaused && _viewModel.IsProcessing)
            {
                _viewModel.IsOperationInProgress = false;
                _viewModel.NotifyRelatedCommands();
                _viewModel.LogService.Warning("İşlem duraklatıldı - Devam etmek için 'Devam Et' butonuna basın.", true, LogService.LogState.Stage2);

                try
                {
                    while (_viewModel.IsPaused && _viewModel.IsProcessing)
                    {
                        await Task.Delay(500, _cancellationToken);
                    }

                    if (!_viewModel.IsPaused && _viewModel.IsProcessing)
                    {
                        _viewModel.LogService.Success("İşlem devam ediyor.", true, LogService.LogState.Stage2);
                    }
                }
                catch (OperationCanceledException)
                {
                    // İptal edildi, normal durum
                    _viewModel.LogService.Warning("İşlem iptal edildi.", true, LogService.LogState.Stage2);
                    throw;
                }
            }
        }

        private bool IsTermSimilarWithRegex(string baseTerm, string candidate)
        {
            if (string.IsNullOrWhiteSpace(baseTerm) || string.IsNullOrWhiteSpace(candidate))
                return false;
            if (IsRegexMatchWithInflections(baseTerm, candidate))
                return true;
            if (IsRegexMatchWithInflections(candidate, baseTerm))
                return true;
            if (baseTerm.Equals(candidate, StringComparison.OrdinalIgnoreCase))
                return true;
            return false;

            bool IsRegexMatchWithInflections(string term, string text)
            {
                string pattern = $@"\b{Regex.Escape(term)}\b";
                return Regex.IsMatch(text, pattern, RegexOptions.IgnoreCase);
            }
        }


    }

}