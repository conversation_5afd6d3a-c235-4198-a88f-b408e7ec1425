using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.Presenters;
using Avalonia.Controls.Primitives;
using Avalonia.Layout;
using Avalonia.Media;
using Avalonia.Threading;
using Avalonia.VisualTree;

namespace TranslationAgent.Services
{
    public class ToastService
    {
        private Window? _mainWindow;
        private bool _isInitialized;
        private StackPanel? _containerPanel;
        private readonly Dictionary<Control, DispatcherTimer> _activeToasts = new Dictionary<Control, DispatcherTimer>();
        private const int MaxToasts = 5;
        private readonly Queue<(string message, ToastType type, int durationMs)> _pendingToasts = new Queue<(string, ToastType, int)>();

        public ToastService()
        {
        }

        public void Initialize(Window mainWindow)
        {
            _mainWindow = mainWindow;
            _mainWindow.Loaded += (s, e) =>
            {
                _isInitialized = true;
                InitializeContainer();
                ShowPendingToasts();
            };
        }

        private void ShowPendingToasts()
        {
            while (_pendingToasts.Count > 0)
            {
                var (message, type, durationMs) = _pendingToasts.Dequeue();
                ShowToastInternal(message, type, durationMs);
            }
        }

        private void InitializeContainer()
        {
            if (_mainWindow == null) return;

            // Find the ContentPresenter
            var contentPresenter = _mainWindow.GetVisualDescendants()
                .OfType<ContentPresenter>()
                .FirstOrDefault(cp => cp.Name == "PART_ContentPresenter");

            if (contentPresenter == null) return;

            // Create a Grid to hold both the original content and our toast container
            var grid = new Grid();

            // Get the original content
            var originalContent = contentPresenter.Content;
            contentPresenter.Content = grid;

            // Add the original content back
            if (originalContent != null)
            {
                grid.Children.Add((Control)originalContent);
            }

            // Create and add the toast container
            _containerPanel = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Right,
                VerticalAlignment = VerticalAlignment.Bottom,
                Margin = new Thickness(20),
                MaxWidth = 300,
                MaxHeight = 400,
                IsHitTestVisible = false,
                Spacing = 8 // Toast'lar arası boşluk
            };

            grid.Children.Add(_containerPanel);
        }

        private void ShowToastInternal(string message, ToastType type, int durationMs)
        {
            if (!_isInitialized || _containerPanel == null)
            {
                _pendingToasts.Enqueue((message, type, durationMs));
                return;
            }

            Dispatcher.UIThread.Post(() =>
            {
                // Maksimum toast sayısını kontrol et
                if (_activeToasts.Count >= MaxToasts)
                {
                    var oldestToast = _activeToasts.First();
                    RemoveToast(oldestToast.Key);
                }

                var toastControl = CreateToastContent(message, type);
                _containerPanel.Children.Add(toastControl);

                var timer = new DispatcherTimer
                {
                    Interval = TimeSpan.FromMilliseconds(durationMs)
                };

                timer.Tick += (s, e) =>
                {
                    RemoveToast(toastControl);
                };

                _activeToasts.Add(toastControl, timer);
                timer.Start();
            });
        }

        private void RemoveToast(Control toastControl)
        {
            if (_containerPanel?.Children.Any(x => x.GetHashCode() == toastControl.GetHashCode()) == true)
            {
                _containerPanel.Children.Remove(toastControl);
            }

            if (_activeToasts.TryGetValue(toastControl, out var timer))
            {
                timer.Stop();
                _activeToasts.Remove(toastControl);
            }
        }

        private Control CreateToastContent(string message, ToastType type)
        {
            var background = type switch
            {
                ToastType.Success => new SolidColorBrush(Color.FromRgb(76, 175, 80)),  // Green
                ToastType.Warning => new SolidColorBrush(Color.FromRgb(255, 152, 0)),  // Orange
                ToastType.Error => new SolidColorBrush(Color.FromRgb(244, 67, 54)),    // Red
                _ => new SolidColorBrush(Color.FromRgb(33, 150, 243))                  // Blue (Info)
            };

            return new Border
            {
                Background = background,
                CornerRadius = new CornerRadius(4),
                Padding = new Thickness(16, 8),
                Child = new TextBlock
                {
                    Text = message,
                    Foreground = Brushes.White,
                    TextWrapping = TextWrapping.Wrap,
                    MaxWidth = 300
                }
            };
        }

        public void ShowToast(string message, ToastType type = ToastType.Info, int durationMs = 3000)
        {
            ShowToastInternal(message, type, durationMs);
        }
    }

    public enum ToastType
    {
        Info,
        Success,
        Warning,
        Error
    }
}