using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Mscc.GenerativeAI;
using TranslationAgent.Helpers;

namespace TranslationAgent.Services
{
    public class GeminiService
    {
        private readonly AppSettings _settings;
        private GoogleAI? _currentGoogleAI;
        private GeminiApiKey? _currentKey;
        private GenerativeModel? _currentModel;
        private int _currentKeyIndex = 0;
        private readonly LogService _logService;

        public event EventHandler<string>? QuotaUpdated;

        public GeminiService(AppSettings settings, LogService logService)
        {
            _settings = settings;
            _logService = logService;
            _currentKey = _settings.General.GeminiApiKeys[_currentKeyIndex];
        }


        private bool UseQuota(AIModel aiModel)
        {
            if (_settings.General.GeminiApiKeys.Count == 0)
            {
                return false;
            }

            bool isFlash2_5 = aiModel == AIModel.GeminiFlash2_5;
            int startingIndex = _currentKeyIndex;
            bool foundValidKey = false;

            do
            {

                // Her zaman bir sonrakine geç
                _currentKeyIndex = (_currentKeyIndex + 1) % _settings.General.GeminiApiKeys.Count;
                _currentKey = _settings.General.GeminiApiKeys[_currentKeyIndex];

                if (aiModel == AIModel.GeminiFlash2_5_Lite)
                {
                    return true;
                }

                var previousQuotaFlash2_0 = _currentKey.RemainingQuotaFlash2_0;
                var previousQuotaFlash2_5 = _currentKey.RemainingQuotaFlash2_5;

                // Sadece kotası olan anahtarlar için kota kullan
                if ((isFlash2_5 && _currentKey.RemainingQuotaFlash2_5 > 0) || (!isFlash2_5 && _currentKey.RemainingQuotaFlash2_0 > 0))
                {
                    if (_settings.UseQuota(_currentKey.Key, isFlash2_5))
                    {
                        // Sadece kota değiştiğinde olayı tetikle
                        if (previousQuotaFlash2_0 != _currentKey.RemainingQuotaFlash2_0 ||
                            previousQuotaFlash2_5 != _currentKey.RemainingQuotaFlash2_5)
                        {
                            QuotaUpdated?.Invoke(this, _currentKey.Key);
                        }

                        foundValidKey = true;
                        break;
                    }
                }

            } while (_currentKeyIndex != startingIndex);


            return foundValidKey;
        }

        private void SetupModel(AIModel aiModel, string? systemPrompt = null)
        {
            if (_currentKey == null)
            {
                throw new InvalidOperationException("API anahtarı bulunamadı.");
            }

            var aiModelSelected = aiModel switch
            {
                AIModel.GeminiFlash2_5_Lite => "gemini-2.5-flash-lite-preview-06-17",
                AIModel.GeminiFlash2_0 => Model.Gemini20Flash001,
                AIModel.GeminiFlash2_5 => Model.Gemini25FlashPreview0520,
                _ => Model.Gemini25FlashPreview0520
            };

            _currentGoogleAI = new GoogleAI(apiKey: _currentKey.Key);
            var systemInstruction = systemPrompt != null ? new Content(systemPrompt) : null;
            _currentModel = _currentGoogleAI.GenerativeModel(
                model: aiModelSelected,
                systemInstruction: systemInstruction
            );
            _logService.Info($"Kullanılan API: {_currentKeyIndex}", true, LogService.LogState.Main);
        }


        /// <summary>
        /// LogState'e göre uygun GenerationConfig oluşturur
        /// </summary>
        /// <param name="logState">Log durumu</param>
        /// <returns>Yapılandırılmış GenerationConfig</returns>
        private GenerationConfig CreateGenerationConfig(LogService.LogState logState, AIModel aiModel)
        {
            var temperature = logState switch
            {
                LogService.LogState.Context => 0.7f,
                LogService.LogState.Stage1 => 0.7f,
                LogService.LogState.Stage2 => 0.7f,
                LogService.LogState.Stage3 => 1.3f,
                LogService.LogState.Translate => 1.3f,
                _ => 1.0f
            };

            var maxoutputtokens = aiModel == AIModel.GeminiFlash2_5_Lite || aiModel == AIModel.GeminiFlash2_5 ? 65536 : 8192;


            return new GenerationConfig
            {
                Temperature = temperature,
                MaxOutputTokens = maxoutputtokens
            };
        }

        public async Task<JsonElement?> GenerateContentWithToolsAsync(string systemprompt, string prompt, List<Tool> tools, AIModel aiModel = AIModel.GeminiFlash2_5, LogService.LogState logState = LogService.LogState.Main, CancellationToken cancellationToken = default)
        {


            try
            {
                // CancellationToken kontrolü
                cancellationToken.ThrowIfCancellationRequested();

                var generationConfig = CreateGenerationConfig(logState, aiModel);
                var toolConfig = new ToolConfig
                {
                    FunctionCallingConfig = new FunctionCallingConfig() { Mode = FunctionCallingConfigMode.Any }
                };

                SetupModel(aiModel, systemprompt);
                var requestOptions = new RequestOptions(null, TimeSpan.FromMinutes(2));
                var response = await _currentModel!.GenerateContent(prompt, generationConfig: generationConfig, tools: tools,
                cancellationToken: cancellationToken, requestOptions: requestOptions);
                if (!UseQuota(aiModel))
                {
                    throw new InvalidOperationException("Kullanılabilir API kotası bulunamadı.");
                }

                if (response.Candidates == null || response.Candidates.Count == 0)
                {
                    throw new Exception($"Model yanıt vermedi.");
                }

                var candidate = response.Candidates.First();
                var parts = candidate.Content?.Parts ?? new List<Part>();

                var mainPart = parts.FirstOrDefault(p => p.FunctionCall == null);
                if (mainPart != null)
                {
                    if (mainPart.Text == null)
                    {
                        _logService.Info($"Ana parça metni boş.", false, logState);
                    }
                    else
                    {
                        _logService.Info($"Ana parça: {mainPart.Text}", false, logState);
                    }
                }
                else
                {
                    _logService.Info($"Ana parça bulunamadı", false, logState);
                }

                var selectFunctionPart = parts.FirstOrDefault(p => p.FunctionCall != null);

                if (selectFunctionPart != null)
                {
                    _logService.Info($"Fonksiyon çağrısı: {selectFunctionPart.FunctionCall.Name}", false, logState);
                    //_logService.Info($"Fonksiyon çağrısı metni: {selectFunctionPart.Text}", false, logState);
                    return selectFunctionPart.FunctionCall.Args as JsonElement?;
                }
                else if (mainPart != null && mainPart.Text != null)
                {
                    try
                    {

                        // Markdown kod bloğundan JSON içeriğini çıkar
                        var match = Regex.Match(mainPart.Text, @"```(?:json)?\s*({[\s\S]*?})\s*```");
                        if (match.Success)
                        {

                            return JsonSerializer.Deserialize<JsonElement>(match.Groups[1].Value);
                        }
                        else
                        {
                            return null;
                        }

                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"JSON ayrıştırma hatası: {ex.Message}", ex);
                    }
                }

                throw new Exception($"Tool veya beklenen JSON yapısı bulunamadı");
            }
            catch (Exception ex)
            {
                // Eğer quota aşımı hatası ise ilgili modelin kotasını sıfırla
                if (ex.Message.Contains("TooManyRequests") || ex.Message.Contains("RESOURCE_EXHAUSTED"))
                {
                    try
                    {
                        // Hata mesajından model adını bul

                        var currentKey = _settings.General.GeminiApiKeys[_currentKeyIndex];
                        bool changed = false;

                        if (aiModel == AIModel.GeminiFlash2_0)
                        {
                            if (currentKey.RemainingQuotaFlash2_0 != 0)
                            {
                                currentKey.RemainingQuotaFlash2_0 = 0;
                                changed = true;
                            }
                        }
                        else if (aiModel == AIModel.GeminiFlash2_5)
                        {
                            if (currentKey.RemainingQuotaFlash2_5 != 0)
                            {
                                currentKey.RemainingQuotaFlash2_5 = 0;
                                changed = true;
                            }
                        }

                        if (changed)
                        {
                            _settings.Save();
                            _logService.Warning($"Quota aşımı nedeniyle {(aiModel == AIModel.GeminiFlash2_0 ? "Gemini Flash 2.0" : "Gemini Flash 2.5")} için kalan kota sıfırlandı.");
                        }
                    }
                    catch (Exception quotaEx)
                    {
                        _logService.Error($"Quota sıfırlama sırasında hata: {quotaEx.Message}", quotaEx);
                    }
                }
                throw new Exception($"Hata: {ex.Message}", ex);
            }
        }



        public async Task<JsonElement?> GenerateContentWithJsonToolsAsync(string systemprompt, string prompt, string toolsJson, AIModel aiModel = AIModel.GeminiFlash2_5, LogService.LogState logState = LogService.LogState.Main, CancellationToken cancellationToken = default)
        {
            var tools = ToolHelper.CreateGeminiToolsFromJson(toolsJson);
            return await GenerateContentWithToolsAsync(systemprompt, prompt, tools, aiModel, logState, cancellationToken);
        }



        public async Task<string?> GenerateContentAsync(string systemprompt, string prompt, AIModel aiModel = AIModel.GeminiFlash2_5, LogService.LogState logState = LogService.LogState.Main, CancellationToken cancellationToken = default)
        {


            try
            {
                // CancellationToken kontrolü
                cancellationToken.ThrowIfCancellationRequested();

                var generationConfig = CreateGenerationConfig(logState, aiModel);

                SetupModel(aiModel, systemprompt);
                var requestOptions = new RequestOptions(null, TimeSpan.FromMinutes(2));
                var response = await _currentModel!.GenerateContent(prompt, generationConfig: generationConfig, cancellationToken: cancellationToken, requestOptions: requestOptions);
                if (!UseQuota(aiModel))
                {
                    throw new InvalidOperationException("Kullanılabilir API kotası bulunamadı.");
                }

                if (response.Candidates == null || response.Candidates.Count == 0)
                {
                    throw new Exception($"Model yanıt vermedi.");
                }

                var candidate = response.Candidates.First();
                var parts = candidate.Content?.Parts ?? new List<Part>();

                var mainPart = parts.FirstOrDefault(p => p.Text != null);
                if (mainPart != null)
                {
                    var match = Regex.Match(mainPart.Text, @"<rapor>([\s\S]*?)</rapor>");
                    if (match.Success)
                    {

                        return match.Groups[1].Value;
                    }
                    else
                    {
                        _logService.Info($"Rapor tagı bulunamadı", false, logState);
                        return null;
                    }
                }
                else
                {
                    _logService.Info($"Ana parça bulunamadı", false, logState);
                }
                throw new Exception($"Beklenen Rapor yapısı bulunamadı");
            }
            catch (Exception ex)
            {
                // Eğer quota aşımı hatası ise ilgili modelin kotasını sıfırla
                if (ex.Message.Contains("TooManyRequests") || ex.Message.Contains("RESOURCE_EXHAUSTED"))
                {
                    try
                    {
                        // Hata mesajından model adını bul

                        var currentKey = _settings.General.GeminiApiKeys[_currentKeyIndex];
                        bool changed = false;

                        if (aiModel == AIModel.GeminiFlash2_0)
                        {
                            if (currentKey.RemainingQuotaFlash2_0 != 0)
                            {
                                currentKey.RemainingQuotaFlash2_0 = 0;
                                changed = true;
                            }
                        }
                        else if (aiModel == AIModel.GeminiFlash2_5)
                        {
                            if (currentKey.RemainingQuotaFlash2_5 != 0)
                            {
                                currentKey.RemainingQuotaFlash2_5 = 0;
                                changed = true;
                            }
                        }

                        if (changed)
                        {
                            _settings.Save();
                            _logService.Warning($"Quota aşımı nedeniyle {(aiModel == AIModel.GeminiFlash2_0 ? "Gemini Flash 2.0" : "Gemini Flash 2.5")} için kalan kota sıfırlandı.");
                        }
                    }
                    catch (Exception quotaEx)
                    {
                        _logService.Error($"Quota sıfırlama sırasında hata: {quotaEx.Message}", quotaEx);
                    }
                }
                throw new Exception($"İçerik oluşturma hatası: {ex.Message}", ex);
            }
        }

        public async Task<string?> GenerateContentWithStringAsync(string systemprompt, string prompt, AIModel aiModel = AIModel.GeminiFlash2_5, LogService.LogState logState = LogService.LogState.Main, CancellationToken cancellationToken = default)
        {

            return await GenerateContentAsync(systemprompt, prompt, aiModel, logState, cancellationToken);
        }

        public string GetCurrentModelInfo()
        {
            if (_currentModel == null) return "Model henüz başlatılmadı";
            return _currentModel.Model.ToString();
        }

        public string GetCurrentQuotaInfo()
        {
            if (_settings.General.GeminiApiKeys.Count == 0) return "API anahtarı bulunamadı";
            var currentKey = _settings.General.GeminiApiKeys[_currentKeyIndex];
            return $"Flash 2.0: {currentKey.RemainingQuotaFlash2_0}, Flash 2.5: {currentKey.RemainingQuotaFlash2_5}";
        }
    }
}