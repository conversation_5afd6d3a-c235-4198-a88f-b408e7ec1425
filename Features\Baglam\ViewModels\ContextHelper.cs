using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TranslationAgent.Features.Baglam.Models;
using TranslationAgent.Features.GoogleSheets.ViewModels;
using TranslationAgent.Services;

namespace TranslationAgent.Features.Baglam.ViewModels
{
    /// <summary>
    /// Bağlam yönetimi için yardımcı sınıf - Google Sheets entegrasyonu ve bağlam işlemleri
    /// </summary>
    public static class ContextHelper
    {
        #region Google Sheets Operations (Google Sheets İşlemleri)

        /// <summary>
        /// Google Sheets'te bağlamı günceller
        /// </summary>
        public static async Task UpdateContextInGoogleSheets(
            GoogleSheetsService googleSheetsService,
            GoogleSheetsPanelViewModel googleSheetsPanelVm,
            Context originalContext,
            Context updatedContext,
            LogService logService)
        {
            var selectedSheet = googleSheetsPanelVm.SelectedContextSheet;
            var spreadsheetId = googleSheetsPanelVm.SpreadsheetId;

            if (selectedSheet == null || string.IsNullOrWhiteSpace(spreadsheetId))
            {
                logService.Warning("Google Sheets bilgileri eksik, bağlam güncellenemedi.", true, LogService.LogState.Context);
                return;
            }

            try
            {
                logService.Info($"Bağlam güncelleniyor: {originalContext.AltBaslik}", true, LogService.LogState.Context);

                // Google Sheets'ten mevcut verileri al
                var existingData = await googleSheetsService.GetDataAsync(spreadsheetId, selectedSheet.SheetName);
                if (existingData != null && existingData.Count > 1)
                {
                    // Güncellenecek satırı bul - eski değerlerle tam eşleşme yap
                    var rowIndex = FindContextRowIndex(existingData, originalContext);

                    if (rowIndex.HasValue)
                    {
                        // Güncellenmiş veriyi hazırla
                        var updatedRowData = new List<object>
                        {
                            updatedContext.ID,
                            updatedContext.Kategori ?? "",
                            updatedContext.AltBaslik ?? "",
                            updatedContext.Icerik ?? "",
                            updatedContext.URL ?? "",
                            updatedContext.Keywords != null ? string.Join(", ", updatedContext.Keywords) : ""
                        };

                        // Satırı Google Sheets'te güncelle
                        await googleSheetsService.UpdateRowAsync(spreadsheetId, selectedSheet.SheetName, rowIndex.Value, updatedRowData);
                        logService.Info($"Bağlam Google Sheets'te başarıyla güncellendi: {originalContext.AltBaslik}", true, LogService.LogState.Context);
                    }
                    else
                    {
                        logService.Warning($"Güncellenecek bağlam Google Sheets'te bulunamadı: {originalContext.AltBaslik}", true, LogService.LogState.Context);
                    }
                }
                else
                {
                    logService.Warning("Google Sheets'te veri bulunamadı.", true, LogService.LogState.Context);
                }
            }
            catch (Exception ex)
            {
                logService.Error($"Bağlam Google Sheets'te güncellenirken hata oluştu: {ex.Message}", ex, true, LogService.LogState.Context);
                throw;
            }
        }

        /// <summary>
        /// Google Sheets'ten bağlamı siler
        /// </summary>
        public static async Task DeleteContextFromGoogleSheets(
            GoogleSheetsService googleSheetsService,
            GoogleSheetsPanelViewModel googleSheetsPanelVm,
            Context context,
            LogService logService)
        {
            var selectedSheet = googleSheetsPanelVm.SelectedContextSheet;
            var spreadsheetId = googleSheetsPanelVm.SpreadsheetId;

            if (selectedSheet == null || string.IsNullOrWhiteSpace(spreadsheetId))
            {
                logService.Warning("Google Sheets bilgileri eksik, bağlam silinemedi.", true, LogService.LogState.Context);
                return;
            }

            try
            {
                logService.Info($"Bağlam siliniyor: {context.AltBaslik}", true, LogService.LogState.Context);

                var existingData = await googleSheetsService.GetDataAsync(spreadsheetId, selectedSheet.SheetName);
                if (existingData != null)
                {
                    var rowIndex = existingData
                        .Select((row, idx) => new { row, idx })
                        .Skip(1) // Başlık satırını atla
                        .FirstOrDefault(x => x.row.Count > 0 &&
                                           int.TryParse(x.row[0]?.ToString(), out int id) &&
                                           id == context.ID)?.idx;

                    if (rowIndex.HasValue)
                    {
                        await googleSheetsService.DeleteRowAsync(spreadsheetId, selectedSheet.SheetName, rowIndex.Value);
                        logService.Info($"'{context.AltBaslik}' bağlamı Google Sheets'ten başarıyla silindi.", true, LogService.LogState.Context);
                    }
                    else
                    {
                        logService.Warning($"Silinecek bağlam Google Sheets'te bulunamadı: ID={context.ID}, AltBaslik={context.AltBaslik}", true, LogService.LogState.Context);
                    }
                }
                else
                {
                    logService.Warning("Google Sheets'te veri bulunamadı.", true, LogService.LogState.Context);
                }
            }
            catch (Exception ex)
            {
                logService.Error($"Bağlam Google Sheets'ten silinirken hata oluştu: {ex.Message}", ex, true, LogService.LogState.Context);
                throw;
            }
        }

        /// <summary>
        /// Google Sheets'e yeni bağlam ekler
        /// </summary>
        public static async Task AddContextToGoogleSheets(
            GoogleSheetsService googleSheetsService,
            GoogleSheetsPanelViewModel googleSheetsPanelVm,
            Context context,
            LogService logService)
        {
            var selectedSheet = googleSheetsPanelVm.SelectedContextSheet;
            var spreadsheetId = googleSheetsPanelVm.SpreadsheetId;

            if (selectedSheet == null || string.IsNullOrWhiteSpace(spreadsheetId))
            {
                logService.Warning("Google Sheets bilgileri eksik, bağlam eklenemedi.", true, LogService.LogState.Context);
                return;
            }

            try
            {
                logService.Info($"Yeni bağlam ekleniyor: {context.AltBaslik}", true, LogService.LogState.Context);

                var newRowData = new List<object>
                {
                    context.ID,
                    context.Kategori ?? "",
                    context.AltBaslik ?? "",
                    context.Icerik ?? "",
                    context.URL ?? "",
                    context.Keywords != null ? string.Join(", ", context.Keywords) : ""
                };
                await googleSheetsService.AppendRowAsync(spreadsheetId, selectedSheet.SheetName, newRowData);

                logService.Info($"'{context.AltBaslik}' bağlamı Google Sheets'e başarıyla eklendi.", true, LogService.LogState.Context);
            }
            catch (Exception ex)
            {
                logService.Error($"Bağlam Google Sheets'e eklenirken hata oluştu: {ex.Message}", ex, true, LogService.LogState.Context);
                throw;
            }
        }

        #endregion

        #region Helper Methods (Yardımcı Metodlar)

        /// <summary>
        /// Google Sheets verilerinde bağlamın satır indeksini ID ile bulur
        /// </summary>
        private static int? FindContextRowIndex(IList<IList<object>> data, Context context)
        {
            return data
                .Select((row, idx) => new { row, idx })
                .Skip(1) // Başlık satırını atla
                .FirstOrDefault(x => x.row.Count > 0 &&
                                   int.TryParse(x.row[0]?.ToString(), out int id) &&
                                   id == context.ID)?.idx;
        }

        /// <summary>
        /// Bağlamın kopyasını oluşturur
        /// </summary>
        public static Context CloneContext(Context context)
        {
            return new Context
            {
                ID = context.ID,
                Kategori = context.Kategori,
                AltBaslik = context.AltBaslik,
                Icerik = context.Icerik,
                URL = context.URL,
                Keywords = context.Keywords?.ToList(),
                Vector = context.Vector,
                Lemma = context.Lemma
            };
        }

        /// <summary>
        /// Bağlam verilerini temizler (trim işlemi)
        /// </summary>
        public static Context SanitizeContext(Context context)
        {
            return new Context
            {
                ID = context.ID,
                Kategori = context.Kategori?.Trim() ?? "",
                AltBaslik = context.AltBaslik?.Trim() ?? "",
                Icerik = context.Icerik?.Trim() ?? "",
                URL = context.URL?.Trim() ?? "",
                Keywords = context.Keywords?.Select(k => k?.Trim()).Where(k => !string.IsNullOrEmpty(k)).ToList(),
                Vector = context.Vector,
                Lemma = context.Lemma?.Trim() ?? ""
            };
        }

        #endregion

        #region Edit Operations (Düzenleme İşlemleri)

        /// <summary>
        /// Düzenlenmiş bağlam verilerini alır
        /// </summary>
        public static Context GetEditedContextData(int id, string editKategori, string editAltBaslik, string editIcerik, string editURL, string editKeywords)
        {
            return new Context
            {
                ID = id,
                Kategori = editKategori,
                AltBaslik = editAltBaslik,
                Icerik = editIcerik,
                URL = editURL,
                Keywords = string.IsNullOrWhiteSpace(editKeywords) ? new List<string>() : editKeywords.Split(',').Select(k => k.Trim()).Where(k => !string.IsNullOrEmpty(k)).ToList()
            };
        }

        /// <summary>
        /// Düzenleme değişikliklerini bağlama uygular
        /// </summary>
        public static void ApplyEditChangesToContext(Context context, int id, string editKategori, string editAltBaslik, string editIcerik, string editURL, string editKeywords)
        {
            context.ID = id;
            context.Kategori = editKategori;
            context.AltBaslik = editAltBaslik;
            context.Icerik = editIcerik;
            context.URL = editURL;
            context.Keywords = string.IsNullOrWhiteSpace(editKeywords) ? new List<string>() : editKeywords.Split(',').Select(k => k.Trim()).Where(k => !string.IsNullOrEmpty(k)).ToList();
        }

        /// <summary>
        /// Orijinal bağlam değerlerini alır
        /// </summary>
        public static Context GetOriginalContextValues(Context context, Dictionary<Context, Context> originalContextValues)
        {
            if (originalContextValues.TryGetValue(context, out var storedOriginal))
            {
                return storedOriginal;
            }

            // Eğer orijinal değerler yoksa mevcut değerleri kullan
            return CloneContext(context);
        }


        #endregion

        #region Add Operations (Yeni Bağlam Ekleme İşlemleri)

        /// <summary>
        /// Yeni bağlam verilerini doğrular
        /// </summary>
        public static (bool IsValid, string ErrorMessage) ValidateNewContext(string newKategori, string newIcerik)
        {
            if (string.IsNullOrWhiteSpace(newKategori))
            {
                return (false, "Kategori alanı boş olamaz.");
            }
            if (string.IsNullOrWhiteSpace(newIcerik))
            {
                return (false, "İçerik alanı boş olamaz.");
            }
            return (true, string.Empty);
        }

        /// <summary>
        /// Alanlardan yeni bağlam oluşturur
        /// </summary>
        public static Context CreateNewContextFromFields(int id, string newKategori, string newAltBaslik, string newIcerik, string newURL, string newKeywords)
        {
            return SanitizeContext(new Context
            {
                ID = id,
                Kategori = newKategori,
                AltBaslik = newAltBaslik,
                Icerik = newIcerik,
                URL = newURL,
                Keywords = string.IsNullOrWhiteSpace(newKeywords) ? new List<string>() : newKeywords.Split(',').Select(k => k.Trim()).Where(k => !string.IsNullOrEmpty(k)).ToList()
            });
        }

        #endregion
    }
}
