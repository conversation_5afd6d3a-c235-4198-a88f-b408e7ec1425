using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Google.Apis.Auth.OAuth2;
using Google.Apis.Drive.v3;
using Google.Apis.Drive.v3.Data;
using Google.Apis.Services;
using Google.Apis.Sheets.v4;
using Google.Apis.Sheets.v4.Data;
using TranslationAgent.Features.GoogleSheets.ViewModels;

namespace TranslationAgent.Services
{
    /// <summary>
    /// Google Sheets API ile etkileşim sağlayan servis sınıfı
    /// </summary>
    public class GoogleSheetsService
    {
        private readonly string APPLICATION_NAME = "Translation Agent";
        private SheetsService? _sheetsService;
        private DriveService? _driveService;
        private readonly string _logFilePath;
        private readonly LogService _logService;

        private bool _isAuthenticated;

        /// <summary>
        /// Servisin kimlik doğrulama durumunu gösterir
        /// </summary>
        public bool IsAuthenticated
        {
            get => _isAuthenticated;
            private set
            {
                if (_isAuthenticated != value)
                {
                    _isAuthenticated = value;
                    AuthenticationStatusChanged?.Invoke(this, value);
                }
            }
        }

        /// <summary>
        /// Kimlik doğrulama durumu değiştiğinde tetiklenen olay
        /// </summary>
        public event EventHandler<bool>? AuthenticationStatusChanged;

        /// <summary>
        /// GoogleSheetsService için yeni bir örnek oluşturur
        /// </summary>
        /// <param name="logService">Log servisi</param>
        public GoogleSheetsService(LogService logService)
        {
            _logFilePath = Path.Combine(AppContext.BaseDirectory, "google_sheets_log.txt");
            _logService = logService ?? throw new ArgumentNullException(nameof(logService));
            IsAuthenticated = false;
            _logService.Info("Google Sheets servisi başlatıldı");
        }

        private void LogMessage(string message)
        {
            var logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}{Environment.NewLine}";
            try
            {
                System.IO.File.AppendAllText(_logFilePath, logEntry);
            }
            catch
            {
                // Ignore write errors
            }
        }

        /// <summary>
        /// Google Sheets API için kimlik doğrulama yapar
        /// </summary>
        /// <param name="serviceAccountJson">Servis hesabı JSON içeriği</param>
        /// <returns>Kimlik doğrulama işlemi tamamlandığında dönen Task</returns>
        /// <exception cref="ArgumentNullException">serviceAccountJson null veya boş ise</exception>
        public async Task AuthenticateAsync(string serviceAccountJson)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(serviceAccountJson))
                {
                    throw new ArgumentNullException(nameof(serviceAccountJson), "Servis hesabı JSON boş olamaz.");
                }

                var credential = await Task.Run(() => GoogleCredential.FromJson(serviceAccountJson)
                    .CreateScoped(new[] {
                        SheetsService.Scope.Spreadsheets,
                        DriveService.Scope.DriveReadonly
                    }));

                _sheetsService = new SheetsService(new BaseClientService.Initializer
                {
                    HttpClientInitializer = credential,
                    ApplicationName = APPLICATION_NAME
                });

                _driveService = new DriveService(new BaseClientService.Initializer
                {
                    HttpClientInitializer = credential,
                    ApplicationName = APPLICATION_NAME
                });

                _logService.Info("Google Sheets kimlik doğrulama başarılı");
                IsAuthenticated = true;
            }
            catch (Exception ex)
            {
                IsAuthenticated = false;
                _logService.Error("Google API Kimlik Doğrulama Hatası", ex);
                throw;
            }
        }

        /// <summary>
        /// Erişilebilir Google Sheets dosyalarını listeler
        /// </summary>
        /// <returns>SpreadsheetInfo listesi</returns>
        /// <exception cref="InvalidOperationException">Kimlik doğrulanmamış ise</exception>
        public async Task<IEnumerable<SpreadsheetInfo>> GetAccessibleSpreadsheetsAsync()
        {
            if (!IsAuthenticated || _driveService == null)
            {
                throw new InvalidOperationException("Not authenticated or Drive Service not initialized. Call AuthenticateAsync first.");
            }

            var spreadsheets = new List<SpreadsheetInfo>();
            FilesResource.ListRequest listRequest = _driveService.Files.List();
            listRequest.Q = "mimeType='application/vnd.google-apps.spreadsheet' and trashed = false";
            listRequest.PageSize = 100;
            listRequest.Fields = "nextPageToken, files(id, name)";

            string? pageToken = null;
            do
            {
                listRequest.PageToken = pageToken;
                var result = await listRequest.ExecuteAsync();
                if (result.Files != null && result.Files.Any())
                {
                    foreach (var file in result.Files)
                    {
                        spreadsheets.Add(new SpreadsheetInfo(file.Name, file.Id));
                    }
                }
                pageToken = result.NextPageToken;
            } while (pageToken != null);

            return spreadsheets;
        }

        /// <summary>
        /// Belirtilen spreadsheet'teki sayfaları getirir
        /// </summary>
        /// <param name="spreadsheetId">Spreadsheet ID</param>
        /// <returns>Sayfa listesi</returns>
        /// <exception cref="InvalidOperationException">Kimlik doğrulanmamış ise</exception>
        /// <exception cref="ArgumentNullException">spreadsheetId null veya boş ise</exception>
        public async Task<IList<Sheet>> GetSheetsAsync(string spreadsheetId)
        {
            if (!IsAuthenticated || _sheetsService == null)
            {
                throw new InvalidOperationException("Not authenticated. Call AuthenticateAsync first.");
            }
            if (string.IsNullOrWhiteSpace(spreadsheetId))
            {
                throw new ArgumentNullException(nameof(spreadsheetId), "Spreadsheet ID cannot be empty.");
            }

            var request = _sheetsService.Spreadsheets.Get(spreadsheetId);
            request.Fields = "sheets.properties";
            var spreadsheet = await request.ExecuteAsync();
            return spreadsheet.Sheets;
        }

        /// <summary>
        /// Belirtilen aralıktaki verileri getirir
        /// </summary>
        /// <param name="spreadsheetId">Spreadsheet ID</param>
        /// <param name="sheetName">Sayfa adı</param>
        /// <param name="range">Veri aralığı (opsiyonel)</param>
        /// <returns>Veri listesi</returns>
        /// <exception cref="InvalidOperationException">Kimlik doğrulanmamış ise</exception>
        /// <exception cref="ArgumentNullException">Gerekli parametreler null veya boş ise</exception>
        public async Task<IList<IList<object>>> GetDataAsync(string spreadsheetId, string sheetName, string? range = null)
        {
            if (!IsAuthenticated || _sheetsService == null)
            {
                throw new InvalidOperationException("Not authenticated. Call AuthenticateAsync first.");
            }
            if (string.IsNullOrWhiteSpace(spreadsheetId))
            {
                throw new ArgumentNullException(nameof(spreadsheetId), "Spreadsheet ID cannot be empty.");
            }
            if (string.IsNullOrWhiteSpace(sheetName))
            {
                throw new ArgumentNullException(nameof(sheetName), "Sheet name cannot be empty.");
            }

            var effectiveRange = string.IsNullOrWhiteSpace(range) ? sheetName : $"{sheetName}!{range}";
            var request = _sheetsService.Spreadsheets.Values.Get(spreadsheetId, effectiveRange);
            var response = await request.ExecuteAsync();
            return response.Values;
        }

        /// <summary>
        /// Belirtilen filtrelere göre verileri getirir
        /// </summary>
        /// <param name="spreadsheetId">Spreadsheet ID</param>
        /// <param name="sheetName">Sayfa adı</param>
        /// <param name="filters">Filtreler (opsiyonel)</param>
        /// <returns>Filtrelenmiş veri listesi</returns>
        /// <exception cref="InvalidOperationException">Kimlik doğrulanmamış ise</exception>
        /// <exception cref="ArgumentNullException">Gerekli parametreler null veya boş ise</exception>
        public async Task<IList<IDictionary<string, object>>> GetFilteredDataAsync(string spreadsheetId, string sheetName, Dictionary<string, string>? filters = null)
        {
            if (!IsAuthenticated || _sheetsService == null)
            {
                throw new InvalidOperationException("Not authenticated. Call AuthenticateAsync first.");
            }
            if (string.IsNullOrWhiteSpace(spreadsheetId))
            {
                throw new ArgumentNullException(nameof(spreadsheetId), "Spreadsheet ID cannot be empty.");
            }
            if (string.IsNullOrWhiteSpace(sheetName))
            {
                throw new ArgumentNullException(nameof(sheetName), "Sheet name cannot be empty.");
            }

            var allData = await GetDataAsync(spreadsheetId, sheetName, null);

            if (allData == null || !allData.Any())
            {
                return new List<IDictionary<string, object>>();
            }

            var headers = allData.First().Select(h => h?.ToString() ?? string.Empty).ToList();
            var records = new List<IDictionary<string, object>>();

            for (int i = 1; i < allData.Count; i++)
            {
                var row = allData[i];
                var record = new Dictionary<string, object>();
                bool matchesFilters = true;

                for (int j = 0; j < headers.Count && j < row.Count; j++)
                {
                    record[headers[j]] = row[j] ?? string.Empty;
                }

                if (filters != null && filters.Any())
                {
                    foreach (var filter in filters)
                    {
                        if (!record.TryGetValue(filter.Key, out var value) ||
                            value == null ||
                            !(value.ToString() ?? string.Empty).Equals(filter.Value, StringComparison.OrdinalIgnoreCase))
                        {
                            matchesFilters = false;
                            break;
                        }
                    }
                }

                if (matchesFilters)
                {
                    records.Add(record);
                }
            }
            return records;
        }

        /// <summary>
        /// Belirtilen aralıktaki verileri günceller
        /// </summary>
        /// <param name="spreadsheetId">Spreadsheet ID</param>
        /// <param name="range">Güncellenecek aralık</param>
        /// <param name="values">Yeni değerler</param>
        /// <returns>İşlemin başarı durumu</returns>
        public async Task<bool> UpdateSheetDataAsync(string spreadsheetId, string range, IList<IList<object>> values)
        {
            try
            {
                if (!IsAuthenticated || _sheetsService == null)
                {
                    throw new InvalidOperationException("Kimlik doğrulanmamış. Önce AuthenticateAsync'i çağırın.");
                }

                var valueRange = new ValueRange
                {
                    Values = values
                };

                var updateRequest = _sheetsService.Spreadsheets.Values.Update(valueRange, spreadsheetId, range);
                updateRequest.ValueInputOption = SpreadsheetsResource.ValuesResource.UpdateRequest.ValueInputOptionEnum.RAW;

                var response = await updateRequest.ExecuteAsync();
                _logService.Info($"E-tabloda {response.UpdatedCells} hücre başarıyla güncellendi");
                return true;
            }
            catch (Exception ex)
            {
                _logService.Error($"{range} aralığındaki veriler güncellenirken hata oluştu", ex);
                throw;
            }
        }

        /// <summary>
        /// Belirtilen aralığa yeni veriler ekler
        /// </summary>
        /// <param name="spreadsheetId">Spreadsheet ID</param>
        /// <param name="range">Eklenecek aralık</param>
        /// <param name="values">Eklenecek değerler</param>
        /// <returns>İşlemin başarı durumu</returns>
        public async Task<bool> AppendSheetDataAsync(string spreadsheetId, string range, IList<IList<object>> values)
        {
            try
            {
                if (!IsAuthenticated || _sheetsService == null)
                {
                    throw new InvalidOperationException("Kimlik doğrulanmamış. Önce AuthenticateAsync'i çağırın.");
                }

                var valueRange = new ValueRange
                {
                    Values = values
                };

                var appendRequest = _sheetsService.Spreadsheets.Values.Append(valueRange, spreadsheetId, range);
                appendRequest.ValueInputOption = SpreadsheetsResource.ValuesResource.AppendRequest.ValueInputOptionEnum.RAW;
                appendRequest.InsertDataOption = SpreadsheetsResource.ValuesResource.AppendRequest.InsertDataOptionEnum.INSERTROWS;

                var response = await appendRequest.ExecuteAsync();
                _logService.Info($"E-tabloya {response.Updates.UpdatedRange} aralığında veri başarıyla eklendi");
                return true;
            }
            catch (Exception ex)
            {
                _logService.Error($"{range} aralığına veri eklenirken hata oluştu", ex);
                throw;
            }
        }

        /// <summary>
        /// Belirtilen sayfaya yeni bir satır ekler
        /// </summary>
        /// <param name="spreadsheetId">Spreadsheet ID</param>
        /// <param name="sheetName">Sayfa adı</param>
        /// <param name="rowData">Eklenecek satır verileri</param>
        /// <returns>İşlemin başarı durumu</returns>
        public async Task<bool> AppendRowAsync(string spreadsheetId, string sheetName, IList<object> rowData)
        {
            try
            {
                if (!IsAuthenticated || _sheetsService == null)
                {
                    throw new InvalidOperationException("Kimlik doğrulanmamış. Önce AuthenticateAsync'i çağırın.");
                }

                var values = new List<IList<object>> { rowData };
                return await AppendSheetDataAsync(spreadsheetId, sheetName, values);
            }
            catch (Exception ex)
            {
                _logService.Error($"{sheetName} sayfasına satır eklenirken hata oluştu", ex);
                throw;
            }
        }

        /// <summary>
        /// Belirtilen aralıktaki verileri temizler
        /// </summary>
        /// <param name="spreadsheetId">Spreadsheet ID</param>
        /// <param name="range">Temizlenecek aralık</param>
        /// <returns>İşlemin başarı durumu</returns>
        public async Task<bool> ClearSheetDataAsync(string spreadsheetId, string range)
        {
            try
            {
                if (!IsAuthenticated || _sheetsService == null)
                {
                    throw new InvalidOperationException("Kimlik doğrulanmamış. Önce AuthenticateAsync'i çağırın.");
                }

                var requestBody = new ClearValuesRequest();
                var deleteRequest = _sheetsService.Spreadsheets.Values.Clear(requestBody, spreadsheetId, range);
                var response = await deleteRequest.ExecuteAsync();

                _logService.Info($"{range} aralığındaki veriler başarıyla temizlendi");
                return true;
            }
            catch (Exception ex)
            {
                _logService.Error($"{range} aralığındaki veriler temizlenirken hata oluştu", ex);
                throw;
            }
        }

        /// <summary>
        /// Yeni bir sayfa oluşturur
        /// </summary>
        /// <param name="spreadsheetId">Spreadsheet ID</param>
        /// <param name="sheetTitle">Oluşturulacak sayfa başlığı</param>
        /// <param name="headers">Sütun başlıkları</param>
        /// <returns>Oluşturulan sayfa</returns>
        public async Task<Sheet> CreateSheetAsync(string spreadsheetId, string sheetTitle, IList<object> headers)
        {
            if (!IsAuthenticated || _sheetsService == null)
            {
                throw new InvalidOperationException("Kimlik doğrulanmamış. Önce AuthenticateAsync'i çağırın.");
            }

            try
            {
                // Önce yeni sayfayı oluştur
                var addSheetRequest = new AddSheetRequest
                {
                    Properties = new SheetProperties
                    {
                        Title = sheetTitle
                    }
                };

                var batchUpdateRequest = new BatchUpdateSpreadsheetRequest
                {
                    Requests = new List<Request>
                    {
                        new Request
                        {
                            AddSheet = addSheetRequest
                        }
                    }
                };

                var response = await _sheetsService.Spreadsheets.BatchUpdate(batchUpdateRequest, spreadsheetId).ExecuteAsync();
                var newSheet = response.Replies[0].AddSheet.Properties;

                // Başlıkları ekle
                var range = $"{sheetTitle}!A1:{(char)('A' + headers.Count - 1)}1";
                var valueRange = new ValueRange
                {
                    Values = new List<IList<object>> { headers }
                };

                var updateRequest = _sheetsService.Spreadsheets.Values.Update(valueRange, spreadsheetId, range);
                updateRequest.ValueInputOption = SpreadsheetsResource.ValuesResource.UpdateRequest.ValueInputOptionEnum.RAW;
                await updateRequest.ExecuteAsync();

                _logService.Info($"'{sheetTitle}' sayfası başarıyla oluşturuldu ve başlıklar eklendi");
                return new Sheet { Properties = newSheet };
            }
            catch (Exception ex)
            {
                _logService.Error($"'{sheetTitle}' sayfası oluşturulurken hata oluştu", ex);
                throw;
            }
        }

        /// <summary>
        /// Belirtilen sayfada, belirtilen satır indeksindeki verileri günceller
        /// </summary>
        /// <param name="spreadsheetId">Spreadsheet ID</param>
        /// <param name="sheetName">Sayfa adı</param>
        /// <param name="rowIndex">Güncellenecek satırın indeksi (0 tabanlı)</param>
        /// <param name="rowData">Yeni satır verileri</param>
        /// <returns>İşlemin başarı durumu</returns>
        public async Task<bool> UpdateRowAsync(string spreadsheetId, string sheetName, int rowIndex, IList<object> rowData)
        {
            try
            {
                if (!IsAuthenticated || _sheetsService == null)
                {
                    throw new InvalidOperationException("Kimlik doğrulanmamış. Önce AuthenticateAsync'i çağırın.");
                }

                // Satır aralığını oluştur (ör: 3. satır için: Sheet1!A3:D3)
                // 0 tabanlı index + 1 (başlık dahil) + 1 (satır numarası 1'den başlar)
                var startRow = rowIndex + 1;
                var endRow = startRow;
                // Sütun sayısı kadar harf oluştur
                char startCol = 'A';
                char endCol = (char)('A' + rowData.Count - 1);
                var range = $"{sheetName}!{startCol}{startRow}:{endCol}{endRow}";
                var values = new List<IList<object>> { rowData };
                return await UpdateSheetDataAsync(spreadsheetId, range, values);
            }
            catch (Exception ex)
            {
                _logService.Error($"{sheetName} sayfasında {rowIndex}. satır güncellenirken hata oluştu", ex);
                throw;
            }
        }

        /// <summary>
        /// Belirtilen sayfada, belirtilen satır indeksindeki satırı siler
        /// </summary>
        /// <param name="spreadsheetId">Spreadsheet ID</param>
        /// <param name="sheetName">Sayfa adı</param>
        /// <param name="rowIndex">Silinecek satırın indeksi (0 tabanlı)</param>
        /// <returns>İşlemin başarı durumu</returns>
        public async Task<bool> DeleteRowAsync(string spreadsheetId, string sheetName, int rowIndex)
        {
            try
            {
                if (!IsAuthenticated || _sheetsService == null)
                {
                    throw new InvalidOperationException("Kimlik doğrulanmamış. Önce AuthenticateAsync'i çağırın.");
                }

                // Önce sayfanın ID'sini al
                var spreadsheet = await _sheetsService.Spreadsheets.Get(spreadsheetId).ExecuteAsync();
                var sheet = spreadsheet.Sheets.FirstOrDefault(s => s.Properties.Title == sheetName);

                if (sheet == null)
                {
                    throw new InvalidOperationException($"'{sheetName}' sayfası bulunamadı.");
                }

                // Satırı silmek için batch update isteği oluştur
                var deleteRequest = new Google.Apis.Sheets.v4.Data.Request
                {
                    DeleteDimension = new Google.Apis.Sheets.v4.Data.DeleteDimensionRequest
                    {
                        Range = new Google.Apis.Sheets.v4.Data.DimensionRange
                        {
                            SheetId = sheet.Properties.SheetId,
                            Dimension = "ROWS",
                            StartIndex = rowIndex,
                            EndIndex = rowIndex + 1
                        }
                    }
                };

                var batchUpdateRequest = new Google.Apis.Sheets.v4.Data.BatchUpdateSpreadsheetRequest
                {
                    Requests = new List<Google.Apis.Sheets.v4.Data.Request> { deleteRequest }
                };

                var response = await _sheetsService.Spreadsheets.BatchUpdate(batchUpdateRequest, spreadsheetId).ExecuteAsync();
                _logService.Info($"E-tabloda {sheetName} sayfasından {rowIndex + 1}. satır başarıyla silindi");
                return true;
            }
            catch (Exception ex)
            {
                _logService.Error($"{sheetName} sayfasından {rowIndex + 1}. satır silinirken hata oluştu", ex);
                throw;
            }
        }

    }
}