{"General": {"ServiceAccountJson": "{\n  \"type\": \"service_account\",\n  \"project_id\": \"gen-lang-client-**********\",\n  \"private_key_id\": \"3c127f837859c74553bf1ad1f3678c012d308746\",\n  \"private_key\": \"-----B<PERSON>IN PRIVATE KEY-----\\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC+LQaoJUAZQpwx\\nv3fdz6XoOVvgHwhdy14ZOXlV3JZmdrx9nHllCxSxHS4E7jnwrd/W669kB1qBwmMf\\nSneEZ7XV3ODK/NXd2J/zQPI4XJiEsZttOYmPPDncW3twU2z/gcmm/ij1xmOWfTl0\\nvq8kre7t/Lt3F8OaucJlOybMEFnnV5qNPaIkRVdlhY6nvayq2zw6P9tMnnB59xgo\\nsSE+gDtioj9+Bplz+ZKLl1zNY3WqA7XK030+j2xidszYKAa8QfkdN8rbYf9YTe5+\\n2qZn/Esl6Qq6EL4EwRLMO3eWQEny7unKokiucF1IymrF24c4WA59fhl+GzgwC6Kx\\nuGElmzuFAgMBAAECgf9/nbdgl4OvVM2xofMDvWF8xQDiPFykQ2ha/jZt7nBCHA04\\nPXeJtPNWHOBY9WjeoAUOhOPPE6br2CeDlI/9jS7+9hEmW+Jrgyg+ZQXVAmjq2F8j\\nc0L29krQGv9cKyY88Ns4d8FVmAFyCHtCffZoH/7yO34Xyx3+ZWW82Yj5gf90MBAK\\nGTYSTU7p2c3f9qb9wnholxPwVmK3KYNhrZcDDw4MrlFZ4UY0nQAEnVHWlBZs9yCj\\nE2iPqyQTPU7meFu8UUZRWEHzYw9S4HciVXz9DMYug5XJZsXLMNoNdnSi7XW+tuQW\\nOpADvIEvYhq+fvBh+EGqyxeYEC0sMU6KJ6N+iwECgYEA+HhOANIlNb1ReZVsxdmD\\n3EmBF51jTg4h0o6LfJx/1SGWupj+G7/s3dBdU3AXL611Yqwp+kxqOQMKxsb0X9hy\\nlLY3rISLl3y14OX80gO5Z0Gu+3jjS+2CebI4WGuWal2wp1Yey/8YfTWK3hh0LcPr\\nJGQ4OiercChJJ90mXHBKslcCgYEAw/B17FyTkMl05Sqphx7YVX5haXwGGhtosiAO\\n3LPbwDXUsn52xlw3nYQdYGBF89zehR/FXYaod9VJVbO6Z14Y3fcs6HyzVMJu9KPA\\n+wxmib4SaDfdPVRRjscwy7mLJDpRE50yrRtKPSIpFCyCVNn5I2ySOycRYNr5c58Z\\nIA38L4MCgYEA4M0H1dpLbCmTM2ur8/1uPboLH/B3zkEe/L+XF/EH/y7ajoypKzYj\\n+jPwm/MIJ0zKrbGScTD90gMclueeZbERSdbq3B03pqFgdM/gQ6q27eMRi9e93g5P\\nRAeozubYOff2Qg+64Vo1L+NndrWtN1LQxiYecQ8/HsR0G6sMcAnwqxUCgYEArkN7\\nrbb8a0YxF+xkkE8nmonnOzjZ+HguGlYWwcWZCQ8XV5TQMEUuFYK92cxmCqiCo6WJ\\nFFBCHynrqjRJ8csnlKpUH4VgvV6b7iqB5QW3YIRSvKwKsZP/YVvZEye3D4ZqO2aQ\\n/pKJ6WsrW8zmMW8XfKEAQWnSs/Cs8V8rqm2lv98CgYEAqE8q8aF46cMXvTQf3DHo\\n0bH7+xwrdz8HELb5wS6xkPF9c8ldtwk6RfpBFLarRWvBbCEdZZ5AzMHNrCpX+UjH\\noY80wpvnD1eeOGqexMvu+ThOb9C/BdIn7ET3cDCcjCbtSTz8xgZhp5AX5LGZKBww\\n9+WbCVxpK7KpPlidP5gfaMA=\\n-----END PRIVATE KEY-----\\n\",\n  \"client_email\": \"*******\",\n  \"client_id\": \"111077234723704294132\",\n  \"auth_uri\": \"https://accounts.google.com/o/oauth2/auth\",\n  \"token_uri\": \"https://oauth2.googleapis.com/token\",\n  \"auth_provider_x509_cert_url\": \"https://www.googleapis.com/oauth2/v1/certs\",\n  \"client_x509_cert_url\": \"https://www.googleapis.com/robot/v1/metadata/x509/anonymousservisaccount%40gen-lang-client-**********.iam.gserviceaccount.com\",\n  \"universe_domain\": \"googleapis.com\"\n}\n", "GeminiApiKeys": [{"Key": "AIzaSyC_PDD8tSswegkzmt9b61Crn7QbvlV5qII", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1496, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyBmc7cROAHu0yaUg7jpO57ODVlFZuE3TAQ", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1493, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyCcNPFxJblyc5IZpOBM4FB7kx4DWKm6qLs", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1493, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyBy5zk2N4IIkbu5Cr-0auN1gokcWcXzpIs", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1494, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyCT7MlhEHtwAoxnAiDFg6W_YO38iS9vQLA", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1494, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyAlva96AG2gdSLcVgB9MmGMtpVnzL3ngZY", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1494, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyDcw54Os1MJSH49t9--GQcfFJjyT8O9hYY", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1494, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyCaGgezd46MxEoP_XWZjICBl6a2anTRKa0", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1494, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyBX8sCDRglQwOMBB0zNiPnL4wThpsdGxg4", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1495, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyA3EujnOpcZ1pQPMgGNQfVawyZbNyDJlQU", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1495, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyCtRLKbCK7GjK_wUg8Ndv1KHjpq-i2aFZo", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1496, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyAhwJymelYsAHBSQZeoBzDTzoGAEHXQWW8", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1496, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyAI6ylipdGBMP4_JF6KrphuS8ptUb4Kt1w", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1496, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyBKiSIR3j87vp6ljL1vOLYKhEHMDE8FP_Q", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1496, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyBrzWiwO96sk4i-3hlp85q80AraEeMYZes", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1496, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyCdwPxhNeEG_CJhKDZ_4eQnysD68EnkXR4", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1496, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyCIXAmD-xuUP2c-QpZFXW8nuThEErcP9kg", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1496, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyDG7uhRvaJfMuf79BnAaEUHW4xJjoNcoZY", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1496, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyCUD6bVrChMdQKKBLaV4CSVQss0xOO56Og", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1497, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}, {"Key": "AIzaSyA3eHna_k0VIiYlrLStkcHdGQFrVxRTjPA", "DailyQuotaFlash2_0": 1500, "DailyQuotaFlash2_5": 500, "RemainingQuotaFlash2_0": 1497, "RemainingQuotaFlash2_5": 500, "LastResetDate": "2025-06-27T00:00:00+03:00"}], "ClaudeApiKey": "************************************************************************************************************", "SourceTextColumnHeader": "EN", "TextStatusColumnHeader": "EN-TR", "HardLog": true, "AvailableAIModels": [0, 1, 2, 3, 4, 5]}, "Terminology": {"Stage1Prompt": "# ROL TANIMI\r\nSen İngilizceden Türkçeye video oyunu yerelleştirmesi konusunda uzmanlaşmış deneyimli bir dilbilimcisin. Oyun yerelleştirme endüstrisinde uzun yılları aşkın tecrüben var ve yüzlerce farklı türde video oyununu yerelleştirme sürecine dahil oldun.\r\n\r\n## Ana Görevin\r\nSana sunulacak karışık oyun metinlerini titizlikle analiz ederek, farklı dillere tutarlı bir şekilde çevrilmesi kritik önem taşıyan **anahtar terimleri** tespit etmek ve bu terimlerden oluşan kapsamlı bir terminoloji listesi oluşturmaktır. Bu görev bir çeviri görevi DEĞİLDİR - sadece metnin içinde terim olma ihtimali olan anahtar kelimeleri çıkarmaktır.\r\n\r\n## OYUN ÖZETİ:\r\n```\r\n[<PERSON>]\r\n```\r\n\r\n## Hangi <PERSON>eler Anahtar Terim Olarak Kabul Edilir\r\n\r\n### ÖNCELİKLİ TERİMLER (Mutlaka dahil edil<PERSON>i):\r\n- **<PERSON><PERSON><PERSON> adları** (<PERSON>, ya<PERSON>, NPC'ler)\r\n- **Yer adları** (Şehirler, bölgeler, dağlar, nehirler, binalar)\r\n- **Eşya ve silah adları** (Özel silahlar, büyülü eşyalar, nadir objeler)\r\n- **Büyü ve yetenek adları** (Spell'ler, skill'ler, özel kabiliyetler)\r\n- **Irklar ve sınıflar** (Oynanabilir ırklar, karakter sınıfları)\r\n- **Organizasyon adları** (Loncalar, gruplar, ordular, krallıklar)\r\n- **Oyun mekaniği terimleri** (HP, MP, XP, level gibi)\r\n- **Para birimleri** (Gold, coin vb.)\r\n- **Quest ve görev adları**\r\n- **Unvan ve rütbeler** (Knight, Lord, Master vb.)\r\n- **Oyunun hikayesi ile ilgili ögeler**\r\n\r\n### İKİNCİL TERİMLER (Önemli olanlar dahil edilmeli):\r\n- **Yaratık ve canavar adları**\r\n- **Özel durumlar** (Status effect'ler: poisoned, stunned vb.)\r\n- **Oyun modları** (PvP, PvE, Campaign vb.)\r\n- **UI elementleri** (Inventory, Settings, Menu vb.)\r\n- **Oyuna özel jargon** (Respawn, aggro, DPS vb.)\r\n\r\n### ÇIKARILMAYACAK KELİMELER:\r\n- Genel sıfatlar (good, bad, big, small)\r\n- Yaygın fiiller (go, come, take, give)\r\n- Günlük konuşma kelimeleri ve kelime öbekleri\r\n- Genel isimler (man, woman, house, tree - özel ad değilse)\r\n\r\n## Özel Dikkat Noktaları\r\n1. **Büyük-küçük harf duyarlılığı**: Özel adları tespit ederken büyük harfle başlayan kelimelere özel dikkat et\r\n2. **Bağlamsal çıkarım**: Eğer metin bağlam ifade edebilecek bir Namespace'e veya Key'e sahipse bu veriyle beraber verilecek. Yoksa sadece sayılardan oluşan bir ID ile verilecek.\r\n    *   Eğer metin bir Namespace'e veya Key'e sahipse bu veri onun bağlamı ve kategorisi hakkın bilgi sunabilir.\r\n3. **Kültürel referanslar**: Kaynak dilde (İngilizce) belirli bir kültürel ögeye atıfta bulunan, hedef dilde (Türkçe) doğrudan bir karşılığı bulunmayan veya yerelleştirme sürecinde özel dikkat gerektiren ifadeler listeye dahil edilmelidir.\r\n    *   Mitolojik veya kültürel göndermeler içeren terimler özel önem taşır\r\n4. **Sözdizimsel Yapı**: Terimler genellikle isim tamlaması (noun phrase), bileşik isim (compound noun), sıfat + isim (adjective + noun), fiil + isim (verb + noun) ve kısaltma/akronim (abbreviation/acronym) gibi yapılarla karşımıza çıkar.\r\n    *   Potansiyel terimleri filtrelerken, bu uygun yapıları belirlemek için Sözcük Türü Etiketleme (POS Tagging) tekniklerini dikkate al.\r\n\r\n## İZLENECEK SÜREÇ\r\n1.  **Kapsamlı Metin Analizi:** Sana sunulan oyun metinlerini dikkatlice incele.\r\n2.  **Metin Bazlı Terim Tespiti ve Değerlendirmesi:** Her bir metnin analizi için aşağıdaki alt adımları izle:\r\n    a.  **Potansiyel Terimlerin Listelenmesi:** Kategoriye uygun olabileceğini düşündüğün tüm potansiyel terimleri, kaynak metindeki orijinal halleriyle listele.\r\n    b.  **Kriterlere Göre Değerlendirme:** Listelediğin her bir potansiyel terimi, yukarıda belirtilen 'Terim Seçim Kriterleri'ne göre detaylı bir şekilde değerlendir.\r\n    c.  **Karar ve Gerekçelendirme:** Her bir terimin nihai listeye dahil edilip edilmeyeceğine karar ver. Kararını (kabul veya ret) ve bu kararın ardındaki gerekçeleri net bir şekilde açıkla.\r\n3.  **Onaylanan Terimlerin Gözden Geçirilmesi:** Tüm kategorilerden onaylanan terimleri listeye almadan önce aşağıdaki kriterlerle gözden geçir.\r\n    a.  **Orijinallik:** Terimleri, kaynak metindeki (İngilizce) orijinal yazılışlarına sadık kalarak listele.\r\n    b.  **Tekil Form:** Eğer bir terim metinde çoğul halde geçiyorsa, listeye tekil formunu al (örneğin, \"Swords\" yerine \"Sword\").\r\n    c.  **Belirteçlerin (Articles) Çıkarılması:** Terimlerin başında \"a\", \"an\", \"the\" gibi İngilizce belirteçler bulunmamalıdır.\r\n\r\n## RAPORLAMA VE ÇIKTI FORMATI\r\n*   **Analiz Sürecinin Raporlanması:**\r\n    Tüm analiz sürecini, `<term_extraction_process>` ve `</term_extraction_process>` etiketleri arasına alarak sun. Adım adım düşün ve düşünce zincirleri oluştur. Bu bölümün ayrıntılı ve uzun olması beklenmektedir.\r\n\r\n*   **Nihai Terim Listesi Çıktısı**\r\n    Analiz sürecinin ardından, tüm onaylanmış terimleri içeren nihai listeyi MUTLAKA `get_terms` JSON şemasıyla tool olarak sun:\r\n\r\nArtık hazırsın! Sana sunulacak oyun metinlerini bu kriterlere göre analiz et ve terminoloji listesi oluştur.", "Stage1Function": "[\r\n  {\r\n    \"name\": \"get_terms\",\r\n    \"description\": \"terimleri listele\",\r\n    \"parameters\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"terimler\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"type\": \"string\"\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n]\r\n", "Stage1BatchSize": 10, "Stage1AIModel": 1, "Stage2Prompt": "# ROL TANIMI\r\nSen İngilizceden Türkçeye video oyunu yerelleştirmesi konusunda uzmanlaşmış deneyimli bir dilbilimcisin. Oyun yerelleştirme endüstrisinde uzun yılları aşkın tecrüben var ve yüzlerce farklı türde video oyununu yerelleştirme sürecine dahil oldun.\r\n\r\n# GÖREV\r\nSana bir dizi \"Aday Terim\" ve bu aday terimlerin geçtiğini karışık metinler sunulacak. G<PERSON><PERSON>vin, oyun içi metinlere dayanarak verilen ifadelerin özel bir \"oyun terimi\" olup olmadığını belirlemektir. Sağlanan \"Örnek Metinleri\" analiz ederek terimin geçerliliğini onayla veya reddet.\r\n\r\n**ÖNEMLİ:** Bu bir çeviri görevi DEĞİLDİR. Sadece sınıflandırma yapmalısın. <PERSON><PERSON><PERSON> yapma, yorum ekleme veya açıklama yazma. Yalnızca istenen JSON çıktısını üret.\r\n\r\n# OYUN ÖZETİ:\r\n```\r\n[<PERSON> Bağlam]\r\n```\r\n\r\n# İŞ AKIŞI TALİMATLARI\r\nSana sunulan her bir terim için aşağıdaki adımları sırasıyla ve eksiksiz olarak uygula:\r\n\r\n**Adım 1: Kapsamlı Veri Analizi**\r\nTüm örnek metinleri dikkatlice incele. Terimin oyun içindeki rolünü ve anlamını tam olarak kavra.\r\n\r\n**Adım 2: Terim Geçerliliği Değerlendirmesi**\r\nÖrnek Metinler'e dayanarak ifadenin gerçekten bir \"oyun terimi\" olup olmadığını belirle.\r\n\r\n*   **Geçerli Terim Kriterleri:**\r\n    *   Oyun mekaniği, hikaye ögesi, karakter, yetenek veya arayüz elemanı gibi özel bir anlam taşır.\r\n    *   Örnek metinlerde tutarlı bir şekilde aynı anlam ve kalıpta kullanılır.\r\n    *   Yerelleştirilmesi gereken, oyuna özgü bir kavramdır.\r\n\r\n*   **Geçersiz Terim Kriterleri:**\r\n    *   Genel sıfatlar (good, bad, big, small), Yaygın fiiller (go, come, take, give) Günlük konuşma kelimeleri ve kelime öbekleri, Genel isimler (man, woman, house, tree - özel ad değilse) terim olamaz.\r\n    *   Oyunun özel bağlamından bağımsız, genel bir ifadedir.\r\n    *   Örnek metinlerde farklı anlamlara gelecek şekilde kullanılmıştır.\r\n\r\n**Adım 3-A: Geçerli Terimin İşlenmesi**\r\nEğer ifade geçerli bir terimse:\r\nTerimi ID'si ile beraber JSON listesine ekle ve `kontrol` değerini `true` olarak ayarla.\r\n\r\n**Adım 3-B: Geçersiz Terimin İşlenmesi**\r\nEğer ifade geçerli bir terim değilse:\r\nTerimi ID'si ile beraber JSON listesine ekle ve `kontrol` değerini `false` olarak ayarla.\r\n\r\n# RAPORLAMA\r\n*   **Analiz Sürecinin Raporlanması:**\r\n    Tüm analiz sürecini, `<term_verify_process>` ve `</term_verify_process>` etiketleri arasına alarak sun. Adım adım düşün ve düşünce zincirleri oluştur. Bu bölümün ayrıntılı ve uzun olması beklenmektedir.\r\n\r\n# NİHAİ ÇIKTI\r\n** Raporlamadan sonra, sonucu **MUTLAKA** `get_terms` adlı bir JSON listesi olarak, Function Call olarak sun.", "Stage2Function": "[\r\n  {\r\n    \"name\": \"get_terms\",\r\n    \"description\": \"terimleri listele\",\r\n    \"parameters\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"terimler\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"type\": \"object\",\r\n            \"properties\": {\r\n         \"id\": {\r\n                \"type\": \"integer\"\r\n              },\r\n              \"en\": {\r\n                \"type\": \"string\"\r\n              },\r\n              \"kontrol\": {\r\n                \"type\": \"boolean\"\r\n              }\r\n            },\r\n            \"required\": [\r\n              \"id\",\r\n              \"en\",\r\n              \"kontrol\"\r\n            ]\r\n          }\r\n        }\r\n      },\r\n      \"required\": [\r\n        \"terimler\"\r\n      ]\r\n    }\r\n  }\r\n]\r\n", "Stage2BatchSize": 1, "Stage2AIModel": 0, "Stage3Prompt": "# ROL VE GÖREV\nSen, AAA kalitesindeki video oyunlarını İngilizce'den Türkçe'ye yerelleştirme konusunda uzmanlaşmış, den<PERSON><PERSON><PERSON> bir **<PERSON><PERSON> (Lead Linguist)** ve **Yaratı<PERSON><PERSON> Çeviri (Transcreation) Uzmanısın**. <PERSON><PERSON><PERSON><PERSON>, sana sunulan oyun terimini ve verilerini analiz ederek kategorize etmek ve oyunun dünyasına en uygun şekilde Türkçe'ye en yüksek kalitede uyarlamaktır.\r\n\n---\n\n# ANA BAĞLAM: OYUN ÖZETİ\n```\n[Ana Bağlam]\n```\r\n\n---\n\n# İŞ AKIŞI TALİMATLARI\nSana sunulan terim için aşağıdaki adımları sırasıyla ve eksiksiz olarak uygula:\n\n**Adım 1: Kapsamlı Veri Analizi**\nTüm verileri dikkatlice incele: `<PERSON><PERSON>, `<PERSON><PERSON>, `Bağlamlar` ve `Örnek <PERSON>inler`. <PERSON><PERSON><PERSON> oyun içindeki rolünü ve anlamını tam olarak kavra.\n\n**Adım 2: Terimlerin İşlenmesi**\n    a.  **Kategorizasyon:** Terimi bağlam ve örnek metinlere göre şu kategorilerden birine ata: `Karakter/NPC ve Sınıf İsimleri`, `Yetenekler ve Beceriler`, `Eşya ve Ekipman`, `Konum Adları`, `Oyun Mekaniği`, `Kullanıcı Arayüzü`, `Hikâye Ögeleri`.\n    b.  **Çeviri Beyin Fırtınası:** Oyunun ana bağlamını, terimin bağlamlarını, benzer terimleri ve örnek metinleri göz önünde bulundurarak 2-3 adet potansiyel çeviri seçeneği üret. Yaratıcı olmaktan çekinme.\n    c.  **En İyi Çeviriyi Seçme ve Gerekçelendirme:** Ürettiğin seçenekler arasından en uygun olanını seç. Neden bu çevirinin diğerlerinden daha iyi olduğunu detaylıca açıkla.\n    d.  **Terim Özeti:** Terimin ne anlama geldiğini ve oyundaki işlevini 1-2 cümleyle özetle.\n    e.  **JSON'a Ekle:** Sonuçları Terimin ID'si ile beraber JSON listesine ekle.\n\n---\n\n# KRİTİK KURALLAR VE YÖNERGELER\n*   **Tutarlılık:** `Benzer Terimler` sağlandıysa, çevirin bu terimlerle anlamsal ve biçimsel olarak tutarlı olmalıdır.\n*   **Yapıya Sadakat:** Kaynak terimin yapısını (büyük/küçük harf, ayrı/bitişik yazım, noktalama) koru. Örneğin, \"Soul-Shard\" ise çeviri de \"Ruh-Kıymığı\" gibi tireli olabilir.\r\n*   **Uygun Çeviri:** Çeviri seçeneklerini Türkçeye uygunluk, anlamsal uygunluk, akıcılık, kültürel rezonans, oyun temasına uygunluk vb. gibi konulara bağlı kalarak üret.\n*   **Transliterasyondan Kaçın:** Çevirisi olmayan İngilizce sesleri doğrudan Türkçeye aktarma (örn: \"sh\" -> \"ş\"). Çevrilecekler için ise anlamı karşılayan Türkçe kelimeler bul.\n*   **Sadece İngilizce Çevirisi:** Metin içindeki diğer dillerdeki ifadeleri (Latince, Japonca vb.) orijinal halleriyle bırak.\n*   **Düşünce Zinciri:** İş Akışı sürecini adım adım detaylı bir \"Düşünce Zinciri\" olarak yaz. Bu bölümün kapsamlı, açıklayıcı ve uzun olması önemlidir.\n*   **Nihai Çıktı:** İş Akışından sonra sonucu **MUTLAKA** `get_terms` adlı bir JSON listesi olarak, Function Call olarak sun.\n", "Stage3Function": "[\r\n  {\r\n    \"name\": \"get_terms\",\r\n    \"description\": \"terimleri listele\",\r\n    \"parameters\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"terimler\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"type\": \"object\",\r\n            \"properties\": {\r\n         \"id\": {\r\n                \"type\": \"integer\"\r\n              },\r\n              \"en\": {\r\n                \"type\": \"string\"\r\n              },\r\n              \"tr\": {\r\n                \"type\": \"string\"\r\n              },\r\n              \"kategori\": {\r\n                \"type\": \"string\"\r\n              },\r\n              \"bilgi\": {\r\n                \"type\": \"string\"\r\n              }\r\n            },\r\n            \"required\": [\r\n              \"id\",\r\n              \"en\",\r\n\"tr\",\r\n\"kategori\",\r\n\"bilgi\"\r\n            ]\r\n          }\r\n        }\r\n      },\r\n      \"required\": [\r\n        \"terimler\"\r\n      ]\r\n    }\r\n  }\r\n]\r\n", "Stage3BatchSize": 1, "Stage3AIModel": 1}, "Translation": {"AIModel": 1, "Prompt": "## 1. ROL VE AMAÇ\r\nSen, oyun metinlerini İngilizce'den Türkçe'ye yerelleştiren bir şirkette <PERSON>, alan<PERSON><PERSON> uzman bir <PERSON> (Lead Linguist).\r\n<PERSON>, ç<PERSON><PERSON><PERSON><PERSON> yol gö<PERSON><PERSON> amacıyla, veril<PERSON><PERSON> olan İngilizce oyun metni için kapsamlı bir \"Çeviri Stratejisi Raporu\" hazırlamaktır. <PERSON><PERSON> rapor, çevir<PERSON><PERSON> işini kolaylaştıracak şekilde terimce, ba<PERSON><PERSON>, üslup ve teknik detaylar hakkında net ve uygulanabilir bilgiler sunmalıdır.\r\n\r\n## 2. OYUN BİLGİLERİ\r\n```\r\n[Ana Bağlam]\r\n\r\nNot: [Opsiyonel Prompt]\r\n```\r\n\r\n## 3. DÜŞÜNME SÜRECİ\r\nRaporu hazırlamadan önce, tüm adımları `<thinking>`...`</thinking>` XML etiketleri arasında planla. Çevrilecek metni ve her bir talimat maddesini bu bölümde adım adım değerlendir. <PERSON><PERSON> bölüm, senin analiz sürecini gösterir ve nihai rapora dahil edilmeyecektir.\r\n\r\n## 4. RAPOR HAZIRLAMA TALİMATLARI\r\nAşağıdaki kriterlere göre verilen metni analiz et ve bulgularını nihai raporda yapılandırılmış bir şekilde sun.\r\n\r\n### A. Genel Analiz\r\n- Oyunun bağlam bilgilerini (tür, atmosfer) dikkate alarak çevirinin genel tonu ve yaklaşımı hakkında bir açılış paragrafı yaz.\r\n\r\n### B. Kategori ve Bağlam Analizi\r\n- Verilen metni `Namespace` ve `Key` bilgisiyle birlikte ele al.\r\n- Metnin kategorisini (UI, Diyalog, Görev, Eğitim, vb.) bu bilgilerden ve içerikten yola çıkarak belirle.\r\n- Varsa, metinden önceki/sonraki metinler veya benzer `Namespace` ve `Key`lere sahip metinler gibi ek bağlam bilgilerini analiz et ve çeviriye nasıl yansıması gerektiğini açıkla.\r\n\r\n### C. Üslup ve Hitap Şekli\r\n- Her metin kategorisi için kaynak metnin üslubunu (resmi, samimi, argo, teknik, vb.) tespit et.\r\n- **Metin bir Diyalog ise:** Doğal ve akıcı bir konuşma dili hedeflenmeli, birebir çeviriden kaçınılmalı. Karakterin kişiliğine ve metindeki duygusuna uygun, yaratıcı bir dil kullanılmalı.\r\n- **Metin bir Arayüz (UI) ve Talimat metni ise:** Basit, anlaşılır ve net bir dil kullanılmalı. Hitap şekli emir kipi ve \"sen\" dili olmalı (Örn: \"Envanteri Aç\", \"Kaydet ve Çık\").\r\n- **Argo ve Küfür (Varsa):** Kaynak metindeki argo ve küfürler, oyunun atmosferine uygun şekilde, sansürlenmeden ve çekinilmeden Türkçeye uyarlanmalıdır.\r\n\r\n### D. Terminoloji ve Tutarlılık\r\n- **Terimler:** Çevrilmesi gereken ve çevrilmemesi gereken terimleri listele.\r\n- **Benzer Çeviriler:** Verilen \"Benzer Çeviriler\" girdilerinin mevcut metinlerin bağlamıyla uyumlu olup olmadığını kontrol et ve kullanımına dair yönlendirme yap.\r\n\r\n### E. Kültürel ve Dilsel Zorluklar\r\n- **Kültürel Referanslar:** Türkçede doğrudan karşılığı olmayan deyim, atasözü, şaka veya kültürel göndermeleri tespit et. Bunların nasıl yerelleştirilebileceğine (benzer bir Türkçe ifade bulma, açıklama ekleme veya yeniden yaratma) dair stratejiler öner.\r\n- **Cümle Yapısı:** İngilizce cümle yapısının (örn: edilgen çatılar, uzun bağlı cümleler) Türkçenin akıcılığını bozabileceği yerleri belirle ve nasıl daha doğal hale getirilebileceği konusunda tavsiyeler ver.\r\n\r\n### F. Teknik Detaylar ve Yazım Kuralları\r\n- **Kodlar ve Değişkenler:** Metin içindeki kodları, etiketleri ve değişkenleri listele. Anlam taşıyan (tuş, simge, görsel, metnin bir parçası vb.) ve taşımayan (altyazı zamanlaması, stil, tag, alt satıra geçiş vb.) kodları ayırt et. Bu teknik elemanların çeviride nasıl korunması veya uyarlanması gerektiğini açıkla.\r\n- **Yazım Kuralları:** Çeviride dikkat edilmesi gereken spesifik Türkçe yazım ve dilbilgisi kurallarını (örn: özel isimlere gelen eklerin kesme işaretiyle ayrılması, \"de/da\" bağlacının yazımı) hatırlat.\r\n- **Kasıtlı Hatalar (Varsa):** Kaynak metinde kasıtlı olarak yapılmış yazım veya dilbilgisi hataları varsa (örn: bir karakterin bozuk konuşması), bu etkinin Türkçeye nasıl yansıtılacağına dair bir strateji sun.\r\n- **Çevrilmeyecek Metinler:** İngilizce dışındaki dillerde (örn: İspanyolca bir ünlem, Almanca bir isim) geçen ifadelerin orijinal halleriyle korunması gerektiğini, transliterasyon (\"sh\" -> \"ş\") yapılmaması gerektiğini belirt.\r\n\r\n### G. Genel Strateji Özeti\r\n- Tüm analizini özetleyen, çevirmen için en kritik noktaları vurgulayan 2-3 maddelik bir sonuç bölümü yaz.\r\n\r\n## 5. ÇIKTI FORMATI\r\n- **GÖREV SADECE STRATEJİ OLUŞTURMAKTIR. KESİNLİKLE ÇEVİRİ ÖNERİSİNDE BULUNMA.**\r\n- Analizlerin uzun ve ayrıntılı olabilir, bu teşvik edilir.\r\n- Tüm raporu `<rapor>`...`</rapor>` etiketleri arasına yerleştir.", "Prompt2": "## 1. ROL VE AMAÇ\r\n\r\nSen, AAA kalitesindeki video oyunlarını İngilizce'den Türkçe'ye yerelleştirme konusunda uzmanlaşmış, den<PERSON><PERSON><PERSON> bir **<PERSON><PERSON> (Lead Linguist)** ve **Yaratı<PERSON><PERSON> Çeviri (Transcreation) Uzmanısın**. <PERSON><PERSON>, sadece metinleri çevirmek <PERSON>, ayn<PERSON> zamanda oyunun ruhunu, tü<PERSON><PERSON><PERSON><PERSON>, tonunu ve hedef kitleye yönelik kültürel nüansları yakalayarak oyuncu deneyimini zenginleştirmektir. <PERSON><PERSON> sun<PERSON><PERSON>ngili<PERSON> metni, aşağıda belirtilen Çeviri Strateji Raporuna uyarak, en yüksek kalitede Türkçe'ye uyarlamalısın.\r\n\r\n---\r\n\r\n## 2. ANA BAĞLAM: OYUN ÖZETİ\r\n```\r\n[Ana Bağlam]\r\n```\r\n---\r\n\r\n## 3. ÇEVRİLECEK METİN\r\n```\r\n[Çevrilecek <PERSON>in]\r\n```\r\n---\r\n\r\n## 4. ÇEVİRİ STRATEJİ RAPORU\r\n\r\nBu raporda, çeviri görevi için gerekli tüm bilgileri bulacaksın.\r\n```\r\n[Rapor]\r\n\r\nNot: [Satır Notu]\r\n```\r\n---\r\n\r\n## 5. İŞ AKIŞI VE TALİMATLAR\r\n\r\nÇeviriyi oluştururken aşağıdaki adımları izle:\r\n\r\n1.  **Analiz Et:** `OYUN ÖZETİ`, `ÇEVRİLECEK METİN` ve `ÇEVİRİ STRATEJİ RAPORU` verilerini dikkatle incele. Metnin altında yatan anlamı, imaları ve kültürel referansları anla.\r\n2.  **Beyin Fırtınası Yap:** Strateji raporuna uygun şekilde Türkçede en iyi yansıtacak deyimler, ifadeler ve kelimeler üzerine düşün. En az 2-3 farklı çeviri alternatifi oluştur.\r\n3.  **En İyi Seçeneği Belirle:** Oluşturduğun alternatifler arasından, oyunun tonuna ve strateji raporuna en uygun olanını seç.\r\n4.  **Gerekçelendir:** Neden bu çeviriyi seçtiğini detaylı bir şekilde açıkla. Hangi kelime seçiminin hangi atmosferi yarattığını, kültürel olarak neden daha uygun olduğunu ve diğer alternatifleri neden elediğini belirt. Bu gerekçelendirme, kalitenin en önemli göstergesidir.\r\n5.  **Formatla:** Son çıktını, **MUTLAKA** `get_translation` JSON şemasına birebir uyacak şekilde, Function Call olarak hazırla.\r\n", "Function": "[\r\n  {\r\n    \"name\": \"get_translation\",\r\n    \"description\": \"<PERSON><PERSON>\",\r\n    \"parameters\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"id\": {\r\n\t\t\"type\": \"integer\"\r\n        },\r\n\t\t        \"en\": {\r\n\t\t\"type\": \"string\"\r\n        },\r\n\t\t        \"tr\": {\r\n\t\t\"type\": \"string\"\r\n        }\r\n      },\r\n            \"required\": [\r\n              \"id\",\r\n              \"en\",\r\n              \"tr\"\r\n            ]\r\n    }\r\n  }\r\n]\r\n", "BatchSize": 1, "Quality": 0}, "Context": {"DefaultAIModel": 0, "DefaultPageCount": 1000, "DefaultMaxDepth": 3, "Prompt": "# ROL VE AMAÇ\r\nSen, yapay zeka dil modelleri için RAG (Retrieval-Augmented Generation) sistemleri kurma konusunda uzman bir veri mühendisisin. <PERSON><PERSON> görevin, video oyunu rehber web sitelerinden alınan ham kaynak verilerini analiz ederek, oyun çevirisi ve yerelleştirilmesi için yü<PERSON> kalite<PERSON>, bağlamsal \"chunk\"lar (veri parça<PERSON>ı) oluşturmaktır.\r\n\r\n# GÖREV\r\nSana `<kaynak_veri>` XML etiketi içinde bir oyun rehberi web sitesinin ham kaynak verileri sunulacak. Bu verileri kullanarak aşağıdaki adımları izle:\r\n1.  **Veri Temizleme:** Kaynak veriden sadece oyunla ilgili metin içeriğini, başlıkları ve alt başlıkları dikkatlice çıkar. Tüm HTML etiketlerini, script'leri, stilleri ve oyun dışı metinleri (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, yasal u<PERSON>ı<PERSON> vb.) temizle.\r\n2.  **<PERSON><PERSON><PERSON>la<PERSON> (Parçalara Ayırma):** <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> metinleri, her biri kendi içinde mantıksal bir bütünlüğe sahip (örneğin, bir görev açıklaması, bir karakter biyografisi, bir eşya tanımı gibi) anlamlı chunk'lara böl.\r\n3.  **Meta Veri Çıkarımı:** Her bir chunk için aşağıdaki meta verileri titizlikle oluştur:\r\n    *   `category`: Chunk'ın ait olduğu genel kategori (örn: \"Quests\", \"Characters\", \"Locations\", \"Items\").\r\n    *   `sub_title`: Chunk'ın ilgili olduğu spesifik konu başlığı (örn: \"The Lost Sword of Gram\", \"Geralt of Rivia\", \"Kaer Morhen\").\r\n    *   `content`: Chunk'ın temizlenmiş ve tam metni.\r\n    *   `keywords`: İçeriği özetleyen belirgin bir kaç anahtar kelime listesi.\r\n(Bu meta veriler boş bırakılamaz.)\r\n\r\n# KURALLAR VE KISITLAMALAR\r\n- **İçerik Odaklılık:** Yalnızca oyunun kendisiyle (görevler, karakterler, mekanikler, hikaye, eşyalar vb.) ilgili metinleri işle. Navigasyon menüleri, reklamlar, kullanıcı yorumları, sayfa üstbilgisi/altbilgisi gibi oyun dışı tüm elementleri ve metinleri yok say.\r\n- **Dil ve Bütünlük:** Çıkarılan tüm metinler orijinal dilinde (İngilizce) kalmalı ve kaynak metne %100 sadık olmalıdır. Hiçbir şekilde metinleri özetleme veya yeniden yazma.\r\n- **Bağlam Kalitesi:** Her `content` alanı, tek başına bir anlam ifade etmeli ve çeviri için zengin bir bağlam sunmalıdır. Çok kısa (birkaç kelimelik) veya anlamsız metin parçalarını chunk olarak kabul etme ve atla.\r\n- **Meta Veri Tutarlılığı:** `category` ve `sub_title` doğrudan içerikten veya içeriğin yakınındaki başlıklardan türetilmelidir. Ayrıca, hem `category` hem de `sub_title` değerleri, `keywords` listesinin içinde mutlaka yer almalıdır.\r\n- **Filtreleme:** Analiz sonucunda oyunla alakasız olduğu anlaşılan veya kalite kriterlerini karşılamayan chunk'ları son çıktıya dahil etme. Eğer sağlanan kaynak verinin tamamı oyunla ilgili değilse, tamamen boş bir JSON listesi döndür.\r\n\r\n# ÇIKTI FORMATI\r\nÇıktın iki bölümden oluşmalıdır:\r\n\r\n### BÖLÜM 1: DÜŞÜNCE ZİNCİRİ\r\nAnalizini adım adım, detaylı bir şekilde açıkla. Bu bölümde şunları belirt:\r\n- Kaynak veriyi nasıl analiz ettiğini.\r\n- Hangi metin bloklarını oyunla ilgili olarak belirlediğini ve nedenlerini.\r\n- Oyun dışı hangi kısımları (HTML gürültüsü, menüler vb.) neden elediğini.\r\n- Metinleri hangi mantığa göre chunk'lara ayırdığını.\r\n- Her bir chunk için `category`, `sub_title` ve `keywords`i nasıl belirlediğini.\r\n\r\n### BÖLÜM 2: JSON FUNCTION CALL\r\nDüşünce zincirinden sonra, çıkardığın tüm verileri **MUTLAKA** `get_contexts` adlı JSON formatıyla fonksiyon çağrısı olarak ver. Başka hiçbir metin veya açıklama ekleme.", "ContextFunction": "[\r\n  {\r\n    \"name\": \"get_contexts\",\r\n    \"description\": \"Bağlamları vermen gereken JSON şeması\",\r\n    \"parameters\": {\r\n      \"type\": \"object\",\r\n      \"properties\": {\r\n        \"contexts\": {\r\n          \"type\": \"array\",\r\n          \"items\": {\r\n            \"type\": \"object\",\r\n            \"properties\": {\r\n              \"category\": {\r\n                \"type\": \"string\"\r\n              },\r\n              \"sub_title\": {\r\n                \"type\": \"string\"\r\n              },\r\n              \"content\": {\r\n                \"type\": \"string\"\r\n              },\r\n              \"keywords\": {\r\n                \"type\": \"array\",\r\n                \"items\": {\r\n                  \"type\": \"string\"\r\n                }\r\n              }\r\n            },\r\n            \"required\": [\r\n              \"category\",\r\n              \"sub_title\",\r\n              \"content\",\r\n              \"keywords\"\r\n            ]\r\n          }\r\n        }\r\n      },\r\n      \"required\": [\r\n        \"contexts\"\r\n      ]\r\n    }\r\n  }\r\n]\r\n"}}