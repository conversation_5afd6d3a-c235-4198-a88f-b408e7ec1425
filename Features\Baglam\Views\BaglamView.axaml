<UserControl
    x:Class="TranslationAgent.Features.Baglam.Views.BaglamView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:Avalonia.Controls;assembly=Avalonia.Controls.DataGrid"
    xmlns:converters="clr-namespace:TranslationAgent.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="clr-namespace:TranslationAgent.Features.Baglam.Models"
    xmlns:shared="clr-namespace:TranslationAgent.Shared.Views"
    xmlns:vm="clr-namespace:TranslationAgent.Features.Baglam.ViewModels"
    d:DesignHeight="450"
    d:DesignWidth="800"
    x:DataType="vm:BaglamViewModel"
    mc:Ignorable="d">
    <UserControl.Resources>
        <converters:AIModelConverter x:Key="AIModelConverter" />
        <converters:BoolToTextConverter x:Key="BoolToTextConverter" />
        <converters:CrawedToURLConverter x:Key="CrawedToURLConverter" />
        <converters:MainContextHighlightConverter x:Key="MainContextHighlightConverter" />
        <converters:NullToDefaultIntConverter x:Key="NullToDefaultIntConverter" />
    </UserControl.Resources>
    <Border
        Background="Transparent"
        BorderBrush="Gray"
        BorderThickness="1">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="200" MinHeight="150" />
            </Grid.RowDefinitions>
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="3*" MinWidth="400" />
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" MinWidth="250" />
                </Grid.ColumnDefinitions>
                <!--  Sol Panel - AI İçeriği  -->
                <Grid Grid.Column="0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <controls:DataGrid
                        x:Name="ContextDataGrid"
                        Grid.Row="0"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Stretch"
                        AutoGenerateColumns="False"
                        Background="{DynamicResource SystemControlBackgroundAltHighBrush}"
                        CanUserReorderColumns="True"
                        CanUserResizeColumns="True"
                        GridLinesVisibility="All"
                        IsReadOnly="True"
                        ItemsSource="{Binding GoogleSheetsPanelVm.ContextData}"
                        SelectedItem="{Binding SelectedContext}">
                        <controls:DataGrid.Styles>
                            <Style Selector="DataGridRow">
                                <Setter Property="Background">
                                    <Setter.Value>
                                        <MultiBinding Converter="{StaticResource MainContextHighlightConverter}">
                                            <Binding />
                                            <Binding Path="$parent[UserControl].DataContext.GoogleSheetsPanelVm.MainContext" />
                                        </MultiBinding>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </controls:DataGrid.Styles>
                        <controls:DataGrid.ContextMenu>
                            <ContextMenu>
                                <MenuItem Command="{Binding StartAddCommand}" Header="Yeni Bağlam Ekle" />
                                <Separator />
                                <MenuItem
                                    Command="{Binding StartEditCommand}"
                                    CommandParameter="{Binding SelectedContext}"
                                    Header="Düzenle"
                                    IsEnabled="{Binding SelectedContext, Converter={x:Static ObjectConverters.IsNotNull}}" />
                                <MenuItem
                                    Command="{Binding SetAsMainContextCommand}"
                                    Header="Ana Bağlam Olarak Seç"
                                    IsEnabled="{Binding SelectedContext, Converter={x:Static ObjectConverters.IsNotNull}}" />
                                <Separator />
                                <MenuItem
                                    Command="{Binding DeleteSelectedContextCommand}"
                                    Header="Sil"
                                    IsEnabled="{Binding SelectedContext, Converter={x:Static ObjectConverters.IsNotNull}}" />
                                <MenuItem Command="{Binding RefreshContextDataCommand}" Header="Yenile" />
                            </ContextMenu>
                        </controls:DataGrid.ContextMenu>
                        <controls:DataGrid.Columns>
                            <controls:DataGridTextColumn
                                Width="*"
                                Binding="{Binding ID}"
                                Header="#" />
                            <controls:DataGridTextColumn
                                Width="*"
                                Binding="{Binding Kategori}"
                                Header="Kategori" />
                            <controls:DataGridTextColumn
                                Width="*"
                                Binding="{Binding AltBaslik}"
                                Header="Alt Başlık" />
                            <controls:DataGridTextColumn
                                Width="2*"
                                Binding="{Binding Icerik}"
                                Header="İçerik" />
                            <controls:DataGridTextColumn
                                Width="*"
                                Binding="{Binding URL}"
                                Header="URL" />
                        </controls:DataGrid.Columns>
                    </controls:DataGrid>

                    <!--  Düzenleme Paneli  -->
                    <Border
                        Grid.Row="1"
                        Padding="10"
                        Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                        BorderBrush="Gray"
                        BorderThickness="0,1,0,0"
                        IsVisible="{Binding IsEditMode}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="Kategori:" />
                            <TextBox
                                Grid.Row="0"
                                Grid.Column="1"
                                Margin="0,0,10,5"
                                Text="{Binding EditKategori}" />

                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="2"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="Alt Başlık:" />
                            <TextBox
                                Grid.Row="0"
                                Grid.Column="3"
                                Margin="0,0,0,5"
                                Text="{Binding EditAltBaslik}" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="0"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="URL:" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="1"
                                Margin="0,0,10,5"
                                Text="{Binding EditURL}" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="2"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="Keywords:" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="3"
                                Margin="0,0,0,5"
                                Text="{Binding EditKeywords}"
                                Watermark="Virgülle ayırın" />

                            <TextBlock
                                Grid.Row="2"
                                Grid.Column="0"
                                Margin="0,0,10,5"
                                VerticalAlignment="Top"
                                Text="İçerik:" />
                            <ScrollViewer
                                Grid.Row="2"
                                Grid.Column="1"
                                Grid.ColumnSpan="3"
                                Height="80"
                                Margin="0,0,0,5"
                                VerticalScrollBarVisibility="Auto">
                                <TextBox
                                    AcceptsReturn="True"
                                    Text="{Binding EditIcerik}"
                                    TextWrapping="Wrap" />
                            </ScrollViewer>

                            <StackPanel
                                Grid.Row="3"
                                Grid.Column="0"
                                Grid.ColumnSpan="4"
                                Margin="0,10,0,0"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal"
                                Spacing="10">
                                <Button
                                    Command="{Binding SaveEditCommand}"
                                    Content="Kaydet"
                                    IsDefault="True" />
                                <Button
                                    Command="{Binding CancelEditCommand}"
                                    Content="İptal"
                                    IsCancel="True" />
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!--  Yeni Bağlam Ekleme Paneli  -->
                    <Border
                        Grid.Row="2"
                        Padding="10"
                        Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                        BorderThickness="0,1,0,0"
                        IsVisible="{Binding IsAddMode}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="0"
                                Grid.ColumnSpan="4"
                                Margin="0,0,0,10"
                                FontSize="14"
                                FontWeight="Bold"
                                Text="Yeni Bağlam Ekle" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="0"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="Kategori:" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="1"
                                Margin="0,0,10,5"
                                Text="{Binding NewKategori}" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="2"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="Alt Başlık:" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="3"
                                Margin="0,0,0,5"
                                Text="{Binding NewAltBaslik}" />

                            <TextBlock
                                Grid.Row="2"
                                Grid.Column="0"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="URL:" />
                            <TextBox
                                Grid.Row="2"
                                Grid.Column="1"
                                Margin="0,0,10,5"
                                Text="{Binding NewURL}" />

                            <TextBlock
                                Grid.Row="2"
                                Grid.Column="2"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="Keywords:" />
                            <TextBox
                                Grid.Row="2"
                                Grid.Column="3"
                                Margin="0,0,0,5"
                                Text="{Binding NewKeywords}"
                                Watermark="Virgülle ayırın" />

                            <TextBlock
                                Grid.Row="3"
                                Grid.Column="0"
                                Margin="0,0,10,5"
                                VerticalAlignment="Top"
                                Text="İçerik:" />
                            <ScrollViewer
                                Grid.Row="3"
                                Grid.Column="1"
                                Grid.ColumnSpan="3"
                                Height="80"
                                Margin="0,0,0,5"
                                VerticalScrollBarVisibility="Auto">
                                <TextBox
                                    AcceptsReturn="True"
                                    Text="{Binding NewIcerik}"
                                    TextWrapping="Wrap" />
                            </ScrollViewer>

                            <StackPanel
                                Grid.Row="4"
                                Grid.Column="0"
                                Grid.ColumnSpan="4"
                                Margin="0,10,0,0"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal"
                                Spacing="10">
                                <Button
                                    Command="{Binding SaveAddCommand}"
                                    Content="Ekle"
                                    IsDefault="True" />
                                <Button
                                    Command="{Binding CancelAddCommand}"
                                    Content="İptal"
                                    IsCancel="True" />
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>
                <!--  Splitter  -->
                <GridSplitter
                    Grid.Column="1"
                    Width="5"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                    ResizeDirection="Columns" />
                <!--  Sağ Panel - Crawler  -->
                <Grid
                    Grid.Column="2"
                    Margin="10"
                    RowDefinitions="Auto,*">
                    <StackPanel
                        Grid.Row="0"
                        Orientation="Vertical"
                        Spacing="10">
                        <!--  AI Model Seçimi  -->
                        <StackPanel Orientation="Vertical" Spacing="5">
                            <TextBlock Text="AI Model:" />
                            <Grid ColumnDefinitions="*,Auto,Auto">
                                <ComboBox
                                    Grid.Column="0"
                                    HorizontalAlignment="Stretch"
                                    ItemsSource="{Binding AvailableAIModels}"
                                    SelectedItem="{Binding SelectedAIModel}">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding Converter={StaticResource AIModelConverter}}" />
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>
                                <!-- Gönder Butonu - İşleme yapılmıyorken görünür -->
                                <Button
                                    Grid.Column="1"
                                    Margin="5,0,0,0"
                                    Command="{Binding SendModelCommand}"
                                    Content="Gönder"
                                    IsVisible="{Binding !IsProcessing}" />
                                <!-- Durdur Butonu - İşleme yapılırken görünür -->
                                <Button
                                    Grid.Column="1"
                                    Margin="5,0,0,0"
                                    Command="{Binding StopProcessingCommand}"
                                    Content="Durdur"
                                    IsVisible="{Binding IsProcessing}" />
                            </Grid>
                        </StackPanel>
                        <!--  Web Adres Girişi  -->
              
                        <!-- Tarama Başlat Bölümü - Tarama yapılmıyorken görünür -->
                        <Grid IsVisible="{Binding !IsCrawling}" ColumnDefinitions="*,Auto">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <TextBox Text="{Binding WebAddress}"    Grid.Row="0"
                                Grid.Column="0" Watermark="Web adresi giriniz" />
                                   <Button
                                   Margin="5,0,0,0"
                                Grid.Row="0"
                                Grid.Column="1"
                                HorizontalAlignment="Right"
                                Command="{Binding CrawlCommand}"
                                Content="Tarama Başlat"
                                IsEnabled="{Binding !IsCrawling}" />
    
                         </Grid>
                                <Grid ColumnDefinitions="*,*" Margin="0,0,0,5">
                                <StackPanel Grid.Column="0" Margin="0,0,5,0">
                                    <TextBlock Text="Sayfa Sayısı" FontSize="10" Foreground="Gray"/>
                                    <NumericUpDown
                                        FormatString="0"
                                        IsEnabled="{Binding !IsCrawling}"
                                        Maximum="1000"
                                        Minimum="1"
                                        Value="{Binding AppSettings.Context.DefaultPageCount, Converter={StaticResource NullToDefaultIntConverter}}" />
                                </StackPanel>
                                <StackPanel Grid.Column="1" Margin="5,0,0,0">
                                    <TextBlock Text="Maks. Derinlik" FontSize="10" Foreground="Gray"/>
                                    <NumericUpDown
                                        FormatString="0"
                                        IsEnabled="{Binding !IsCrawling}"
                                        Maximum="50"
                                        Minimum="0"
                                        Value="{Binding AppSettings.Context.DefaultMaxDepth, Converter={StaticResource NullToDefaultIntConverter}}" />
                                </StackPanel>
                            </Grid>
                        <!-- Tarama Durumu ve Durdur Butonu - Tarama yapılırken görünür -->
                        <Grid ColumnDefinitions="*,Auto" IsVisible="{Binding IsCrawling}">
                
                                <ProgressBar   Grid.Column="0" Height="20" MinWidth="20" IsIndeterminate="True" />
                    
                            <Button
                                Grid.Column="1"
                                Margin="5,0,0,0"
                                Command="{Binding StopCrawlCommand}"
                                Content="Durdur"
                                IsEnabled="{Binding IsCrawling}" />
                        </Grid>
                    </StackPanel>

                    <ListBox
                        Grid.Row="1"
                        Margin="0,5,0,0"
                        ItemsSource="{Binding CrawledUrls}"
                        SelectedItem="{Binding SelectedPage}">
                        <ListBox.ContextMenu>
                            <ContextMenu>
                                <MenuItem
                                    Command="{Binding RemoveSelectedPageCommand}"
                                    Header="Sil"
                                    IsEnabled="{Binding SelectedPage, Converter={x:Static ObjectConverters.IsNotNull}}" />
                            </ContextMenu>
                        </ListBox.ContextMenu>
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Vertical" Spacing="2">
                                    <TextBlock
                                        FontWeight="Bold"
                                        Text="{Binding Converter={StaticResource CrawedToURLConverter}}"
                                        TextWrapping="Wrap" />
                                </StackPanel>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>

                </Grid>
            </Grid>

            <!--  GridSplitter  -->
            <GridSplitter
                Grid.Row="1"
                Height="5"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Center"
                Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                ResizeDirection="Rows" />

            <!--  Alt Panel: Log ve İstatistik Paneli  -->
            <shared:LogStatisticsPanel Grid.Row="2" DataContext="{Binding LogStatisticsPanelVm}" />
        </Grid>
    </Border>
</UserControl>
