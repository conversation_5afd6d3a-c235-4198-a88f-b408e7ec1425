using System;
using System.Globalization;
using Avalonia.Data;
using Avalonia.Data.Converters;
using Avalonia.Media;

namespace TranslationAgent.Converters
{
    public class BoolToRedBackgroundConverter : IValueConverter
    {
        public static readonly BoolToRedBackgroundConverter Instance = new();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isQuotaZero && isQuotaZero)
            {
                return new SolidColorBrush(Color.Parse("#33FF0000"));
            }

            // Null değer gelebilir, bu durumda şeffaf döndür
            return new SolidColorBrush(Colors.Transparent);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return BindingOperations.DoNothing;
        }
    }
}