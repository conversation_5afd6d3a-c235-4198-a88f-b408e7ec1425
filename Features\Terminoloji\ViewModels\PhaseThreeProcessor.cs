
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Catalyst;
using Mosaik.Core;
using TranslationAgent.Features.GoogleSheets.ViewModels;
using TranslationAgent.Features.Terminoloji.Models;
using TranslationAgent.Features.Ceviri.Models;
using TranslationAgent.Services;
using TranslationAgent.Helpers;

namespace TranslationAgent.Features.Terminoloji.ViewModels
{
    public class PhaseThreeProcessor
    {
        private readonly TerminolojiViewModel _viewModel;
        private string _sistemPrompt;
        private CancellationToken _cancellationToken;

        public PhaseThreeProcessor(
            TerminolojiViewModel viewModel)
        {
            _viewModel = viewModel;
        }

        public async Task ProcessAsync()
        {
            _cancellationToken = _viewModel.CancellationToken;

            var termsToTranslate = PrepareTermsToTranslate();
            if (termsToTranslate == null || !termsToTranslate.Any())
            {
                _viewModel.LogService.Info("Çevrilecek terim bulunamadı.", true, LogService.LogState.Stage3);
                return;
            }

            var batches = CreateBatches(termsToTranslate);
            _viewModel.LogService.Info($"{_viewModel.AppSettings.Terminology.Stage3Prompt}", false, LogService.LogState.Stage3);
            await ProcessBatches(batches);
        }

        private List<TermViewModel> PrepareTermsToTranslate()
        {
            _viewModel.LogService.Info("Terimler için bağlam vektörleri hazırlanıyor...", true, LogService.LogState.Stage3);
            _viewModel.GoogleSheetsPanelVm.contextDataWithEmbeddings = _viewModel.GoogleSheetsPanelVm.ContextData.Where(c => c.ID != _viewModel.GoogleSheetsPanelVm.MainContext?.ID).Select(c => (c, c.Vector!.Value)).ToList();
            _sistemPrompt = _viewModel.AppSettings.Terminology.Stage3Prompt.Replace("[Ana Bağlam]", _viewModel.GoogleSheetsPanelVm.MainContext?.Icerik ?? "(YOK)");

            var termsToTranslate = new List<TermViewModel>();
            foreach (var row in _viewModel.GoogleSheetsPanelVm.TerminologyData)
            {
                _cancellationToken.ThrowIfCancellationRequested();

                if (row.Status == TermStatus.Onaylandı)
                {
                    termsToTranslate.Add(row);
                }
            }
            // İstatistikleri güncelle
            _viewModel.TotalItemsToProcess = termsToTranslate.Count;
            _viewModel.ItemsProcessedCount = 0;
            _viewModel.UpdateStatistics();
            _viewModel.LogStatisticsPanelVm.SetProgressBar(0);
            _viewModel.LogService.Info($"Çevrilecek terim sayısı: {termsToTranslate.Count}", true, LogService.LogState.Stage3);
            return termsToTranslate;
        }

        private List<List<TermViewModel>> CreateBatches(List<TermViewModel> termsToTranslate)
        {
            var batchSize = _viewModel.AppSettings.Terminology.Stage3BatchSize;
            _viewModel.LogService.Info($"Batch boyutu: {batchSize}", true, LogService.LogState.Stage3);
            var batches = new List<List<TermViewModel>>();
            for (int i = 0; i < termsToTranslate.Count; i += batchSize)
            {
                _cancellationToken.ThrowIfCancellationRequested();
                batches.Add(termsToTranslate.Skip(i).Take(batchSize).ToList());
            }
            _viewModel.LogService.Info($"Toplam Batch sayısı: {batches.Count}", true, LogService.LogState.Stage3);
            return batches;
        }

        private async Task ProcessBatches(List<List<TermViewModel>> batches)
        {
            int currentBatch = 0;
            foreach (var batch in batches)
            {
                if (!await ProcessBatch(batch, ++currentBatch, batches.Count))
                    break;
            }
        }

        private async Task<bool> ProcessBatch(List<TermViewModel> batch, int currentBatch, int totalBatches)
        {
            _viewModel.LogService.Info($"Batch {currentBatch}/{totalBatches} işleniyor...", true, LogService.LogState.Stage3);

            // Duraklat kontrolü
            await HandlePauseIfNeeded();
            _cancellationToken.ThrowIfCancellationRequested();

            try
            {
                var success = await ProcessBatchWithRetry(batch);
                if (!success)
                    return false;

                // İstatistikleri güncelle
                _viewModel.ItemsProcessedCount += batch.Count;
                _viewModel.LogService.Info($"Kullanılan Model: {_viewModel.GeminiService.GetCurrentModelInfo()}", false, LogService.LogState.Stage3);
                _viewModel.LogService.Info($"Mevcut Kota Durumu: {_viewModel.GeminiService.GetCurrentQuotaInfo()}", false, LogService.LogState.Stage3);
                _viewModel.LogService.Info($"Batch işlendi: {_viewModel.ItemsProcessedCount}/{_viewModel.TotalItemsToProcess} metin", false, LogService.LogState.Stage3);
                _viewModel.LogService.Info("------------------------------------", true, LogService.LogState.Stage3);




                // Model istatistiklerini güncelle
                _viewModel.UpdateModelQuotaStatistics();
                _viewModel.UpdateStatistics();

                await Task.Delay(100, _cancellationToken);
                return true;
            }
            catch (Exception ex)
            {
                _viewModel.LogService.Error("Batch işleme sürecinde beklenmeyen hata", ex, true, LogService.LogState.Stage3);
                return false;
            }
        }

        private List<dynamic?> GetExampleSentencesForTerm(string term, string lemmatizedTerm, int maxExamples = 3)
        {
            if (string.IsNullOrWhiteSpace(term))
                return new List<dynamic?>();

            // Regex'i önceden derleyerek performans artışı sağla
            var regexOrj = new Regex($@"\b{Regex.Escape(term)}\b", RegexOptions.IgnoreCase | RegexOptions.Compiled);
            var regexLemma = new Regex($@"\b{Regex.Escape(lemmatizedTerm)}\b", RegexOptions.IgnoreCase | RegexOptions.Compiled);

            // Sık kullanılan değerleri cache'le
            var textStatusHeader = _viewModel.AppSettings.General.TextStatusColumnHeader;
            var sourceTextHeader = _viewModel.AppSettings.General.SourceTextColumnHeader;

            var examplesOrj = _viewModel.GoogleSheetsPanelVm.TextData
                .Where(IsValidTextRow)
                .Where(x => HasValidMatch(x, regexOrj, x => x.EN))
                .Where(text => !string.IsNullOrWhiteSpace(text.EN) && !text.EN.Equals(term, StringComparison.OrdinalIgnoreCase))
                .Distinct()
                .Take(maxExamples)
                .ToList();

            if (examplesOrj.Count >= maxExamples)
                return TextProcessingHelper.ProcessExamplesWithLemma(examplesOrj, lemmatizedTerm, _viewModel.Nlp);

            var examplesLemma = _viewModel.GoogleSheetsPanelVm.TextData
                .Where(IsValidTextRow)
                .Where(x => HasValidMatch(x, regexLemma, x => x.Lemma))
                .Where(text => !string.IsNullOrWhiteSpace(text.EN) && !text.EN.Equals(term, StringComparison.OrdinalIgnoreCase) && !examplesOrj.Any(x => x.EN.Equals(text.EN, StringComparison.OrdinalIgnoreCase)))
                .Distinct()
                .Take(maxExamples - examplesOrj.Count)
                .ToList();

            var examples = TextProcessingHelper.ProcessExamplesWithLemma(examplesOrj.Concat(examplesLemma).Distinct().ToList(), lemmatizedTerm, _viewModel.Nlp);

            return examples;

            // Local helper methods for better readability and performance
            bool IsValidTextRow(Text row)
            {
                return row.IsTranslated || row.NeedsTranslation;
            }

            bool HasValidMatch(Text row, Regex compiledRegex, Func<Text, string?> valueSelector)
            {
                var value = valueSelector(row);
                return !string.IsNullOrWhiteSpace(value) && compiledRegex.IsMatch(value);
            }
        }


        private async Task<bool> ProcessBatchWithRetry(List<TermViewModel> batch)
        {
            var termsWithExamples = await Task.WhenAll(batch.Select(async row =>
            {

                var examples = GetExampleSentencesForTerm(row.EN, row.Lemma, 12);

                var contexts = await _viewModel.GoogleSheetsPanelVm.SearchContextsForTerm(row, 10);

                var similarTerms = _viewModel.GoogleSheetsPanelVm.TerminologyData
                    .Where(t => !string.IsNullOrWhiteSpace(t.EN) && !string.IsNullOrWhiteSpace(t.TR) && !t.EN.Equals(row.EN, StringComparison.OrdinalIgnoreCase))
                    .Where(t => IsTermSimilarWithRegex(row.Lemma, t.Lemma))
                    .ToList();

                var contextterms = await _viewModel.GoogleSheetsPanelVm.SearchTermsForTerm(row, similarTerms, 5);
                similarTerms.AddRange(contextterms);
                similarTerms = similarTerms.Distinct().ToList();

                return new { Term = row.EN, ID = row.ID, Examples = examples, SimilarTerms = similarTerms, Contexts = contexts };
            }));

            var promptBuilder = new System.Text.StringBuilder();

            foreach (var item in termsWithExamples)
            {
                promptBuilder.AppendLine("ID: " + item.ID.ToString());
                promptBuilder.AppendLine("Terim: " + item.Term);
                promptBuilder.AppendLine();
                if (item.SimilarTerms.Any())
                {
                    promptBuilder.AppendLine("Benzer Terimler ve Çevirileri:");
                    foreach (var sim in item.SimilarTerms)
                    {
                        promptBuilder.AppendLine($"- {sim.EN} = {sim.TR}");
                    }
                }
                else
                {
                    promptBuilder.AppendLine("Benzer Terimler: (Yok)");
                }
                promptBuilder.AppendLine();
                if (item.Contexts.Any())
                {
                    promptBuilder.AppendLine("Bağlamlar:");
                    foreach (var con in item.Contexts)
                    {
                        promptBuilder.AppendLine(item.Contexts.IndexOf(con) + 1 + ". " + con.Kategori + " - " + con.AltBaslik);
                        promptBuilder.AppendLine(con.Icerik);
                        promptBuilder.AppendLine();
                    }
                }
                else
                {
                    promptBuilder.AppendLine("Bağlamlar: (Yok)");
                }
                promptBuilder.AppendLine();
                if (item.Examples.Any())
                {
                    promptBuilder.AppendLine("Terimin Geçtiği Metinler:");
                    foreach (var text in item.Examples)
                    {
                        if (_viewModel.SelectedTextContextColumns.Any())
                        {
                            foreach (var column in _viewModel.SelectedTextContextColumns)
                            {
                                var contextInfo = _viewModel.GetTextContextInfo(text, column);

                                if (!string.IsNullOrEmpty(contextInfo))
                                {
                                    promptBuilder.AppendLine($"{column}: {contextInfo}");
                                }
                            }
                            promptBuilder.AppendLine($"- {text.EN}");
                            promptBuilder.AppendLine();
                        }
                        else
                        {
                            promptBuilder.AppendLine($"- {text.EN}");
                        }
                    }
                }
            }
            var prompt = promptBuilder.ToString();


            var success = false;
            var retryCount = 0;
            const int maxRetries = 5;

            while (!success && retryCount < maxRetries)
            {
                await HandlePauseIfNeeded();
                _cancellationToken.ThrowIfCancellationRequested();

                try
                {
                    _viewModel.LogService.Info($"AI'ya gönderilen terimler ve örnekler:\n{prompt}", false, LogService.LogState.Stage3);
                    var response = await _viewModel.GeminiService.GenerateContentWithJsonToolsAsync(
                        _sistemPrompt,
                        prompt,
                        _viewModel.AppSettings.Terminology.Stage3Function,
                        _viewModel.AppSettings.Terminology.Stage3AIModel, LogService.LogState.Stage3, _cancellationToken
                    );
                    if (response == null)
                    {
                        throw new Exception($"AI yanıtı alınamadı!");
                    }
                    else
                    {
                        if (response.HasValue)
                        {
                            success = await ProcessGeminiResponse(response.Value);
                        }
                        else
                        {
                            success = false;
                        }
                    }

                }
                catch (OperationCanceledException)
                {
                    // İptal edildi, normal durum - hata değil
                    _viewModel.LogService.Warning("İşlem iptal edildi.", true, LogService.LogState.Stage3);
                    return false;
                }
                catch (Exception ex)
                {
                    if (ex.Message.Contains("ServiceUnavailable") && ex.Message.Contains("model is overloaded"))
                    {
                        _viewModel.LogService.Warning($"Deneme {retryCount}/{maxRetries} başarısız oldu. Model aşırı yüklendi. Tekrar deneniyor...", true, LogService.LogState.Stage1);
                    }
                    else
                    {
                        retryCount++;
                        if (retryCount < maxRetries)
                        {
                            _viewModel.LogService.Warning($"Deneme {retryCount}/{maxRetries} başarısız oldu. Tekrar deneniyor... Hata: {ex.Message}", true, LogService.LogState.Stage1);

                            try
                            {
                                await Task.Delay(1000 * retryCount, _cancellationToken);
                            }
                            catch (OperationCanceledException)
                            {
                                _viewModel.LogService.Warning("İşlem iptal edildi.", true, LogService.LogState.Stage1);
                                return false;
                            }
                        }
                        else
                        {
                            _viewModel.LogService.Error($"Batch işlenirken maksimum deneme sayısına ulaşıldı", ex, true, LogService.LogState.Stage1);
                            return false;
                        }
                    }
                }
            }
            return success;
        }

        private async Task<bool> ProcessGeminiResponse(System.Text.Json.JsonElement response)
        {
            try
            {
                _viewModel.LogService.Info($"AI yanıtı: {response.GetRawText()}", false, LogService.LogState.Stage3);
                if (response.ValueKind == System.Text.Json.JsonValueKind.Object && response.TryGetProperty("terimler", out var terimlerElement2) && terimlerElement2.ValueKind == System.Text.Json.JsonValueKind.Array)
                {

                    foreach (var terimObj in terimlerElement2.EnumerateArray())
                    {
                        int id;
                        if (terimObj.TryGetProperty("id", out var idObj))
                            id = idObj.GetInt32();
                        else
                            id = -1;
                        var en = terimObj.GetProperty("en").GetString();

                        if (!string.IsNullOrWhiteSpace(en))
                        {
                            var tr = terimObj.GetProperty("tr").GetString();
                            if (!string.IsNullOrWhiteSpace(en))
                            {
                                var kategori = terimObj.GetProperty("kategori").GetString();
                                var bilgi = terimObj.GetProperty("bilgi").GetString();
                                await AddTranslationToGoogleSheets(id, en, tr, kategori, bilgi);
                            }
                        }
                    }
                    return true;
                }
                else
                {
                    _viewModel.LogService.Error("AI yanıtı beklenen formatta değil (terimler bulunamadı)", null, true, LogService.LogState.Stage3);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _viewModel.LogService.Error("AI yanıtı parse edilirken hata oluştu", ex, true, LogService.LogState.Stage3);
                return false;
            }
        }

        private async Task AddTranslationToGoogleSheets(int id, string en, string tr, string kategori, string bilgi)
        {
            var panelVm = _viewModel.GoogleSheetsPanelVm;
            var terminologyData = panelVm.TerminologyData;

            try
            {
                if (!panelVm.IsTerminologyDataFetched || string.IsNullOrWhiteSpace(panelVm.SpreadsheetId))
                {
                    _viewModel.LogService.Warning("Google Sheets bağlantısı hazır değil. Çeviri eklenemedi.", true, LogService.LogState.Stage3);
                    return;
                }

                var selectedSheet = panelVm.SelectedTerminologySheet;
                if (selectedSheet == null || string.IsNullOrWhiteSpace(selectedSheet.SheetName))
                {
                    _viewModel.LogService.Warning("Terimce sayfası seçili değil. Çeviri eklenemedi.", true, LogService.LogState.Stage3);
                    return;
                }


                var spreadsheetId = panelVm.SpreadsheetId;
                var sheetName = selectedSheet.SheetName;


                var row = terminologyData.FirstOrDefault(t => t.ID == id);

                // Lokal veriyi güncelle veya ekle
                if (row != null)
                {
                    row.TR = tr ?? string.Empty;
                    row.Kategori = kategori ?? string.Empty;
                    row.Bilgi = bilgi ?? string.Empty;
                    row.Status = TermStatus.Çevrildi;



                    var existingData = await _viewModel.GoogleSheetsService.GetDataAsync(spreadsheetId, sheetName);

                    if (existingData != null)
                    {
                        var rowIndex = existingData
                            .Select((r, idx) => new { r, idx })
                            .Skip(1) // başlık satırını atla
                            .FirstOrDefault(x => x.r.Count > 0 &&
                                                 int.TryParse(x.r[0]?.ToString(), out int rid) &&
                                                 rid == row.ID)?.idx;

                        if (rowIndex.HasValue)
                        {
                            var updatedRowData = new List<object> { row.ID, row.EN, row.TR, row.Kategori, row.Bilgi, row.Status.ToString() };
                            await _viewModel.GoogleSheetsService.UpdateRowAsync(spreadsheetId, sheetName, rowIndex.Value, updatedRowData);
                            _viewModel.LogService.Info($"'{row.EN}' terimi Google Sheets'te güncellendi.", true, LogService.LogState.Stage3);
                            return;
                        }
                    }
                }
                else
                {
                    var newId = terminologyData.Any() ? terminologyData.Max(t => t.ID) + 1 : 1;
                    var newRow = new TermViewModel
                    {
                        ID = newId,
                        EN = en ?? string.Empty,
                        TR = tr ?? string.Empty,
                        Kategori = kategori ?? string.Empty,
                        Bilgi = bilgi ?? string.Empty,
                        Status = TermStatus.Çevrildi
                    };
                    terminologyData.Add(newRow);
                    var rowData = new List<object> { newRow.ID, newRow.EN, newRow.TR, newRow.Kategori, newRow.Bilgi, newRow.Status.ToString() };
                    await _viewModel.GoogleSheetsService.AppendRowAsync(spreadsheetId, sheetName, rowData);
                    _viewModel.LogService.Info($"'{newRow.EN}' terimi ve çevirisi Google Sheets'e eklendi.", true, LogService.LogState.Stage3);
                }

            }
            catch (Exception ex)
            {
                _viewModel.LogService.Error("Çeviri Google Sheets'e eklenirken hata oluştu", ex, true, LogService.LogState.Stage3);
            }
        }

        private async Task HandlePauseIfNeeded()
        {
            while (_viewModel.IsPaused)
            {
                _viewModel.LogService.Info("İşlem duraklatıldı. Devam etmek için 'Devam Et' butonuna tıklayın.", true, LogService.LogState.Stage3);
                _viewModel.IsOperationInProgress = true;
                _viewModel.NotifyRelatedCommands();
                await Task.Delay(1000); // Her saniye kontrol et
                _cancellationToken.ThrowIfCancellationRequested();
            }
            _viewModel.IsOperationInProgress = false;
            _viewModel.NotifyRelatedCommands();
        }

        private bool IsTermSimilarWithRegex(string baseTerm, string candidate)
        {
            if (string.IsNullOrWhiteSpace(baseTerm) || string.IsNullOrWhiteSpace(candidate))
                return false;

            // Basit bir regex benzerlik kontrolü
            // Örneğin, kelimenin kökünü veya belirli bir kısmını içeren terimleri bul
            var pattern = $@"\b{Regex.Escape(baseTerm.ToLowerInvariant())}\w*\b";
            return Regex.IsMatch(candidate.ToLowerInvariant(), pattern);
        }
    }
}