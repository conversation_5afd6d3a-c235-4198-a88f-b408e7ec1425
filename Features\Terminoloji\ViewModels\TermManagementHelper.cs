using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TranslationAgent.Features.GoogleSheets.ViewModels;
using TranslationAgent.Features.Terminoloji.Models;
using TranslationAgent.Services;

namespace TranslationAgent.Features.Terminoloji.ViewModels
{
    /// <summary>
    /// Terminoloji yönetimi için yardımcı sınıf - Google Sheets entegrasyonu ve terim işlemleri
    /// </summary>
    public static class TermManagementHelper
    {
        #region Google Sheets Operations (Google Sheets İşlemleri)

        /// <summary>
        /// Google Sheets'te terimi günceller
        /// </summary>
        public static async Task UpdateTermInGoogleSheets(
            GoogleSheetsService googleSheetsService,
            GoogleSheetsPanelViewModel googleSheetsPanelVm,
            TermViewModel originalTerm,
            TermViewModel updatedTerm,
            LogService logService)
        {
            var selectedSheet = googleSheetsPanelVm.SelectedTerminologySheet;
            var spreadsheetId = googleSheetsPanelVm.SpreadsheetId;

            if (selectedSheet == null || string.IsNullOrWhiteSpace(spreadsheetId))
            {
                logService.Warning("Google Sheets bilgileri eksik, terim güncellenemedi.", true, LogService.LogState.Main);
                return;
            }

            try
            {
                logService.Info($"Terim güncelleniyor: {originalTerm.EN}", true, LogService.LogState.Main);

                // Google Sheets'ten mevcut verileri al
                var existingData = await googleSheetsService.GetDataAsync(spreadsheetId, selectedSheet.SheetName);
                if (existingData != null && existingData.Count > 1)
                {
                    // Güncellenecek satırı bul - eski değerlerle tam eşleşme yap
                    var rowIndex = FindTermRowIndex(existingData, originalTerm);

                    if (rowIndex.HasValue)
                    {
                        // Güncellenmiş veriyi hazırla
                        var updatedRowData = new List<object>
                        {
                            updatedTerm.ID,
                            updatedTerm.EN ?? "",
                            updatedTerm.TR ?? "",
                            updatedTerm.Kategori ?? "",
                            updatedTerm.Bilgi ?? "",
                            updatedTerm.Status.ToString() // TermStatus'u string olarak ekle
                        };

                        // Satırı Google Sheets'te güncelle
                        await googleSheetsService.UpdateRowAsync(spreadsheetId, selectedSheet.SheetName, rowIndex.Value, updatedRowData);
                        logService.Info($"Terim Google Sheets'te başarıyla güncellendi: {originalTerm.EN}", true, LogService.LogState.Main);
                    }
                    else
                    {
                        logService.Warning($"Güncellenecek terim Google Sheets'te bulunamadı: {originalTerm.EN}", true, LogService.LogState.Main);
                    }
                }
                else
                {
                    logService.Warning("Google Sheets'te veri bulunamadı.", true, LogService.LogState.Main);
                }
            }
            catch (Exception ex)
            {
                logService.Error($"Terim Google Sheets'te güncellenirken hata oluştu: {ex.Message}", ex, true, LogService.LogState.Main);
                throw;
            }
        }

        /// <summary>
        /// Google Sheets'ten terimi siler
        /// </summary>
        public static async Task DeleteTermFromGoogleSheets(
            GoogleSheetsService googleSheetsService,
            GoogleSheetsPanelViewModel googleSheetsPanelVm,
            TermViewModel term,
            LogService logService)
        {
            var selectedSheet = googleSheetsPanelVm.SelectedTerminologySheet;
            var spreadsheetId = googleSheetsPanelVm.SpreadsheetId;

            if (selectedSheet == null || string.IsNullOrWhiteSpace(spreadsheetId))
            {
                logService.Warning("Google Sheets bilgileri eksik, terim silinemedi.", true, LogService.LogState.Main);
                return;
            }

            try
            {
                logService.Info($"Terim siliniyor: {term.EN}", true, LogService.LogState.Main);

                var existingData = await googleSheetsService.GetDataAsync(spreadsheetId, selectedSheet.SheetName);
                if (existingData != null)
                {
                    var rowIndex = existingData
                        .Select((row, idx) => new { row, idx })
                        .Skip(1) // Başlık satırını atla
                        .FirstOrDefault(x => x.row.Count > 0 &&
                                           int.TryParse(x.row[0]?.ToString(), out int id) &&
                                           id == term.ID)?.idx;

                    if (rowIndex.HasValue)
                    {
                        await googleSheetsService.DeleteRowAsync(spreadsheetId, selectedSheet.SheetName, rowIndex.Value);
                        logService.Info($"'{term.EN}' terimi Google Sheets'ten başarıyla silindi.", true, LogService.LogState.Main);
                    }
                    else
                    {
                        logService.Warning($"Silinecek terim Google Sheets'te bulunamadı: ID={term.ID}, EN={term.EN}", true, LogService.LogState.Main);
                    }
                }
                else
                {
                    logService.Warning("Google Sheets'te veri bulunamadı.", true, LogService.LogState.Main);
                }
            }
            catch (Exception ex)
            {
                logService.Error($"Terim Google Sheets'ten silinirken hata oluştu: {ex.Message}", ex, true, LogService.LogState.Main);
                throw;
            }
        }

        /// <summary>
        /// Google Sheets'e yeni terim ekler
        /// </summary>
        public static async Task AddTermToGoogleSheets(
            GoogleSheetsService googleSheetsService,
            GoogleSheetsPanelViewModel googleSheetsPanelVm,
            TermViewModel term,
            LogService logService)
        {
            var selectedSheet = googleSheetsPanelVm.SelectedTerminologySheet;
            var spreadsheetId = googleSheetsPanelVm.SpreadsheetId;

            if (selectedSheet == null || string.IsNullOrWhiteSpace(spreadsheetId))
            {
                logService.Warning("Google Sheets bilgileri eksik, terim eklenemedi.", true, LogService.LogState.Main);
                return;
            }

            try
            {
                logService.Info($"Yeni terim ekleniyor: {term.EN}", true, LogService.LogState.Main);

                var newRowData = new List<object> { term.ID, term.EN, term.TR, term.Kategori, term.Bilgi };
                await googleSheetsService.AppendRowAsync(spreadsheetId, selectedSheet.SheetName, newRowData);

                logService.Info($"'{term.EN}' terimi Google Sheets'e başarıyla eklendi.", true, LogService.LogState.Main);
            }
            catch (Exception ex)
            {
                logService.Error($"Terim Google Sheets'e eklenirken hata oluştu: {ex.Message}", ex, true, LogService.LogState.Main);
                throw;
            }
        }

        #endregion

        #region Helper Methods (Yardımcı Metodlar)

        /// <summary>
        /// Google Sheets verilerinde terimin satır indeksini ID ile bulur
        /// </summary>
        private static int? FindTermRowIndex(IList<IList<object>> data, TermViewModel term)
        {
            return data
                .Select((row, idx) => new { row, idx })
                .Skip(1) // Başlık satırını atla
                .FirstOrDefault(x => x.row.Count > 0 &&
                                   int.TryParse(x.row[0]?.ToString(), out int id) &&
                                   id == term.ID)?.idx;
        }

        /// <summary>
        /// Terimin kopyasını oluşturur
        /// </summary>
        public static TermViewModel CloneTerm(TermViewModel term)
        {
            return new TermViewModel
            {
                ID = term.ID,
                EN = term.EN,
                TR = term.TR,
                Kategori = term.Kategori,
                Bilgi = term.Bilgi,
                Lemma = term.Lemma,
                Status = term.Status // TermStatus'u kopyala
            };
        }

        /// <summary>
        /// Terim verilerini temizler (trim işlemi)
        /// </summary>
        public static TermViewModel SanitizeTerm(TermViewModel term)
        {
            return new TermViewModel
            {
                ID = term.ID,
                EN = term.EN?.Trim() ?? "",
                TR = term.TR?.Trim() ?? "",
                Kategori = term.Kategori?.Trim() ?? "",
                Bilgi = term.Bilgi?.Trim() ?? "",
                Lemma = term.Lemma?.Trim() ?? "",
                Status = term.Status // TermStatus'u kopyala
            };
        }

        #endregion

        #region Edit Operations (Düzenleme İşlemleri)

        /// <summary>
        /// Düzenlenmiş terim verilerini alır
        /// </summary>
        public static TermViewModel GetEditedTermData(int id, string editEN, string editTR, string editKategori, string editBilgi, TermStatus editStatus)
        {
            return new TermViewModel
            {
                ID = id,
                EN = editEN,
                TR = editTR,
                Kategori = editKategori,
                Bilgi = editBilgi,
                Status = editStatus
            };
        }

        /// <summary>
        /// Düzenleme değişikliklerini terime uygular
        /// </summary>
        public static void ApplyEditChangesToTerm(TermViewModel term, string editEN, string editTR, string editKategori, string editBilgi, TermStatus editStatus)
        {
            // ID değiştirilmez, sadece diğer alanlar güncellenir
            term.EN = editEN;
            term.TR = editTR;
            term.Kategori = editKategori;
            term.Bilgi = editBilgi;
            term.Status = editStatus;
        }



        #endregion

        #region Add Operations (Yeni Terim Ekleme İşlemleri)

        /// <summary>
        /// Yeni terim verilerini doğrular
        /// </summary>
        public static (bool IsValid, string ErrorMessage) ValidateNewTerm(string newEN)
        {
            if (string.IsNullOrWhiteSpace(newEN))
            {
                return (false, "Terim alanı boş olamaz.");
            }
            return (true, string.Empty);
        }

        /// <summary>
        /// Alanlardan yeni terim oluşturur
        /// </summary>
        public static TermViewModel CreateNewTermFromFields(int id, string newEN, string newTR, string newKategori, string newBilgi, TermStatus newStatus)
        {
            return SanitizeTerm(new TermViewModel
            {
                ID = id,
                EN = newEN,
                TR = newTR,
                Kategori = newKategori,
                Bilgi = newBilgi,
                Status = newStatus
            });
        }

        #endregion


    }
}
