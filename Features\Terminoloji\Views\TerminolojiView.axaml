<UserControl
    x:Class="TranslationAgent.Features.Terminoloji.Views.TerminolojiView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:TranslationAgent.Converters"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:TranslationAgent.Features.Terminoloji.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:shared="clr-namespace:TranslationAgent.Shared.Views"
    xmlns:views="clr-namespace:TranslationAgent.Features.GoogleSheets.Views"
    xmlns:vm="clr-namespace:TranslationAgent.Features.Terminoloji.ViewModels"
    d:DesignHeight="700"
    d:DesignWidth="1000"
    x:DataType="vm:TerminolojiViewModel"
    mc:Ignorable="d">
    <UserControl.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />
        <converters:AIModelConverter x:Key="AIModelConverter" />
    </UserControl.Resources>

    <UserControl.Styles>
        <Style Selector="TextBlock.QuotaWarning">
            <Setter Property="Foreground" Value="Red" />
        </Style>
    </UserControl.Styles>

    <Design.DataContext>
        <!--  Design-time ViewModel Örneği  -->
        <vm:TerminolojiViewModel />
    </Design.DataContext>
    <Border
        Background="Transparent"
        BorderBrush="Gray"
        BorderThickness="1">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="2*" MinHeight="200" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" MinHeight="200" />

            </Grid.RowDefinitions>
            <!--  Üst Panel: Kontrol Paneli  -->
            <Border
                Grid.Row="0"
                Padding="10"
                Background="{DynamicResource SystemControlBackgroundBaseLowBrush}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <StackPanel
                        Grid.Column="0"
                        VerticalAlignment="Center"
                        Orientation="Horizontal"
                        Spacing="10">
                        <TextBlock VerticalAlignment="Center" Text="Aşama:" />
                        <RadioButton
                            Content="Aşama 1: Terim Tespiti"
                            GroupName="PhaseSelection"
                            IsChecked="{Binding IsPhaseOneActive}"
                            IsEnabled="{Binding !IsProcessing}" />
                        <RadioButton
                            Content="Aşama 2: Terim Kontrolü"
                            GroupName="PhaseSelection"
                            IsChecked="{Binding IsPhaseTwoActive}"
                            IsEnabled="{Binding !IsProcessing}" />
                        <RadioButton
                            Content="Aşama 3: Terim Çevirisi"
                            GroupName="PhaseSelection"
                            IsChecked="{Binding IsPhaseThreeActive}"
                            IsEnabled="{Binding !IsProcessing}" />
                    </StackPanel>

                    <StackPanel
                        Grid.Column="2"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Center"
                        Orientation="Horizontal"
                        Spacing="10">
                        <Button
                            Command="{Binding StartAddCommand}"
                            Content="Yeni Terim Ekle"
                            IsEnabled="{Binding !IsProcessing}" />
                        <Button
                            Command="{Binding ProcessStartCommand}"
                            Content="Başlat"
                            IsEnabled="{Binding !IsProcessing}" />
                        <Button
                            Command="{Binding ProcessPauseToggleCommand}"
                            Content="{Binding PauseToggleButtonText}"
                            IsEnabled="{Binding IsProcessing}" />
                        <Button
                            Command="{Binding ProcessStopCommand}"
                            Content="Durdur"
                            IsEnabled="{Binding IsProcessing}" />
                    </StackPanel>
                </Grid>
            </Border>

            <!--  Orta Panel: Ana İçerik ve Workflow  -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" MinWidth="300" />
                    <ColumnDefinition Width="5" />
                    <ColumnDefinition Width="250" MinWidth="200" />
                </Grid.ColumnDefinitions>

                <!--  Sütun 0: Terimler Listesi, Düzenleme ve Yeni Terim Ekleme Panelleri  -->
                <Grid Grid.Column="0">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>


                    <DataGrid
                        x:Name="TerminologyDataGrid"
                        Grid.Row="0"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Stretch"
                        AutoGenerateColumns="False"
                        Background="{DynamicResource SystemControlBackgroundAltHighBrush}"
                        CanUserReorderColumns="True"
                        CanUserResizeColumns="True"
                        GridLinesVisibility="All"
                        IsReadOnly="True"
                        ItemsSource="{Binding GoogleSheetsPanelVm.TerminologyData}"
                        LoadingRow="TerminologyDataGrid_LoadingRow"
                        SelectedItem="{Binding SelectedTerm}">



                        <DataGrid.ContextMenu>
                            <ContextMenu>
                                <MenuItem Command="{Binding StartAddCommand}" Header="Yeni Terim Ekle" />
                                <Separator />
                                <MenuItem
                                    Command="{Binding StartEditCommand}"
                                    CommandParameter="{Binding SelectedTerm}"
                                    Header="Düzenle" />
                                <MenuItem
                                    Command="{Binding DeleteTermCommand}"
                                    CommandParameter="{Binding SelectedTerm}"
                                    Header="Sil" />
                                <Separator />
                                <MenuItem Command="{Binding RefreshTerminologyDataCommand}" Header="Google Sheets'ten Yenile" />
                            </ContextMenu>
                        </DataGrid.ContextMenu>

                        <DataGrid.Columns>
                            <DataGridTextColumn
                                Width="0.5*"
                                Binding="{Binding ID}"
                                Header="ID" />
                            <DataGridTextColumn
                                Width="2*"
                                Binding="{Binding EN}"
                                Header="Terim" />
                            <DataGridTextColumn
                                Width="2*"
                                Binding="{Binding TR}"
                                Header="Çeviri" />
                            <DataGridTextColumn
                                Width="1*"
                                Binding="{Binding Kategori}"
                                Header="Kategori" />
                            <DataGridTemplateColumn
                                Width="1*"
                                Header="Durum"
                                IsReadOnly="True">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock
                                            Padding="5,0"
                                            VerticalAlignment="Center"
                                            Text="{Binding Status, Converter={x:Static converters:TranslationStatusConverter.Instance}}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!--  Düzenleme Paneli  -->
                    <Border
                        Grid.Row="1"
                        Padding="10"
                        Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                        BorderBrush="Gray"
                        BorderThickness="0,1,0,0"
                        IsVisible="{Binding IsEditMode}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="0"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="Terim:" />
                            <TextBox
                                Grid.Row="0"
                                Grid.Column="1"
                                Margin="0,0,10,5"
                                Text="{Binding EditEN}" />

                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="2"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="Çeviri:" />
                            <TextBox
                                Grid.Row="0"
                                Grid.Column="3"
                                Margin="0,0,0,5"
                                Text="{Binding EditTR}" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="0"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="Kategori:" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="1"
                                Margin="0,0,10,5"
                                Text="{Binding EditKategori}" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="2"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="Bilgi:" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="3"
                                Margin="0,0,0,5"
                                Text="{Binding EditBilgi}" />

                            <StackPanel
                                Grid.Row="2"
                                Grid.Column="0"
                                Grid.ColumnSpan="4"
                                Margin="0,10,0,0"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal"
                                Spacing="10">
                                <Button
                                    Command="{Binding SaveEditCommand}"
                                    Content="Kaydet"
                                    IsDefault="True" />
                                <Button
                                    Command="{Binding CancelEditCommand}"
                                    Content="İptal"
                                    IsCancel="True" />
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!--  Yeni Terim Ekleme Paneli  -->
                    <Border
                        Grid.Row="2"
                        Padding="10"
                        Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                        BorderThickness="0,1,0,0"
                        IsVisible="{Binding IsAddMode}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <TextBlock
                                Grid.Row="0"
                                Grid.Column="0"
                                Grid.ColumnSpan="4"
                                Margin="0,0,0,10"
                                FontSize="14"
                                FontWeight="Bold"
                                Text="Yeni Terim Ekle" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="0"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="Terim:" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="1"
                                Margin="0,0,10,5"
                                Text="{Binding NewEN}" />

                            <TextBlock
                                Grid.Row="1"
                                Grid.Column="2"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="Çeviri:" />
                            <TextBox
                                Grid.Row="1"
                                Grid.Column="3"
                                Margin="0,0,0,5"
                                Text="{Binding NewTR}" />

                            <TextBlock
                                Grid.Row="2"
                                Grid.Column="0"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="Kategori:" />
                            <TextBox
                                Grid.Row="2"
                                Grid.Column="1"
                                Margin="0,0,10,5"
                                Text="{Binding NewKategori}" />

                            <TextBlock
                                Grid.Row="2"
                                Grid.Column="2"
                                Margin="0,0,10,5"
                                VerticalAlignment="Center"
                                Text="Bilgi:" />
                            <TextBox
                                Grid.Row="2"
                                Grid.Column="3"
                                Margin="0,0,0,5"
                                Text="{Binding NewBilgi}" />

                            <StackPanel
                                Grid.Row="3"
                                Grid.Column="0"
                                Grid.ColumnSpan="4"
                                Margin="0,10,0,0"
                                HorizontalAlignment="Right"
                                Orientation="Horizontal"
                                Spacing="10">
                                <Button
                                    Command="{Binding SaveAddCommand}"
                                    Content="Ekle"
                                    IsDefault="True" />
                                <Button
                                    Command="{Binding CancelAddCommand}"
                                    Content="İptal"
                                    IsCancel="True" />
                            </StackPanel>
                        </Grid>
                    </Border>
                </Grid>



                <GridSplitter
                    Grid.Column="1"
                    Width="5"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                    ResizeDirection="Columns" />

                <!--  Sütun 2: Ayar Alanı  -->
                <Border
                    Grid.Column="2"
                    Padding="10"
                    Background="{DynamicResource SystemControlMidHighColorBrush}">
                    <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
                        <StackPanel Spacing="20">
                            <StackPanel IsVisible="{Binding IsPhaseOneActive, Converter={StaticResource BooleanToVisibilityConverter}}" Spacing="10">
                                <TextBlock
                                    FontSize="16"
                                    FontWeight="Bold"
                                    Text="Aşama 1 - Terim Tespiti" />
                                <StackPanel Spacing="5">
                                    <TextBlock FontWeight="SemiBold" Text="AI Modeli:" />
                                    <ComboBox
                                        Width="200"
                                        HorizontalAlignment="Left"
                                        ItemsSource="{Binding AppSettings.General.AvailableAIModels}"
                                        SelectedItem="{Binding AppSettings.Terminology.Stage1AIModel}">
                                        <ComboBox.ItemTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding Converter={StaticResource AIModelConverter}}" />
                                            </DataTemplate>
                                        </ComboBox.ItemTemplate>
                                    </ComboBox>
                                </StackPanel>
                                <StackPanel Spacing="5">
                                    <TextBlock FontWeight="SemiBold" Text="Gönderim Adeti:" />
                                    <NumericUpDown
                                        Width="120"
                                        HorizontalAlignment="Left"
                                        FormatString="0"
                                        Maximum="100"
                                        Minimum="1"
                                        Value="{Binding AppSettings.Terminology.Stage1BatchSize}" />
                                </StackPanel>
                                <StackPanel Spacing="5" IsVisible="{Binding IsTextContextColumnVisible}">
                                    <TextBlock FontWeight="SemiBold" Text="Metin Bağlam Sütunları:" />
                                    <Border
                                        Width="200"
                                        Height="80"
                                        HorizontalAlignment="Left"
                                        BorderBrush="Gray"
                                        BorderThickness="1"
                                        CornerRadius="3">
                                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                                            <ItemsControl ItemsSource="{Binding AvailableTextContextColumns}">
                                                <ItemsControl.ItemTemplate>
                                                    <DataTemplate>
                                                        <CheckBox
                                             Margin="5,2"
                                             Content="{Binding}"
                                             IsChecked="{Binding $parent[UserControl].DataContext.SelectedTextContextColumns, Converter={x:Static converters:StringCollectionContainsConverter.Instance}, ConverterParameter={Binding}, Mode=OneWay}"
                                             Command="{Binding $parent[UserControl].DataContext.ToggleTextContextColumnCommand}"
                                             CommandParameter="{Binding}" />
                                                    </DataTemplate>
                                                </ItemsControl.ItemTemplate>
                                            </ItemsControl>
                                        </ScrollViewer>
                                    </Border>
                                </StackPanel>
                                <StackPanel Spacing="5">
                                    <CheckBox
                                        Content="Filtrelenmiş metinleri kullan"
                                        IsChecked="{Binding UseFilteredTexts}"
                                        ToolTip.Tip="Bu seçenek aktifken, Aşama 1 işlemi tüm metinler yerine sadece filtrelenmiş metinleri kullanır." />
                                </StackPanel>
                            </StackPanel>


                            <StackPanel IsVisible="{Binding IsPhaseTwoActive, Converter={StaticResource BooleanToVisibilityConverter}}" Spacing="10">
                                <TextBlock
                                    FontSize="16"
                                    FontWeight="Bold"
                                    Text="Aşama 2 - Terim Çevirisi" />
                                <StackPanel Spacing="5">
                                    <TextBlock FontWeight="SemiBold" Text="AI Modeli:" />
                                    <ComboBox
                                        Width="200"
                                        HorizontalAlignment="Left"
                                        ItemsSource="{Binding AppSettings.General.AvailableAIModels}"
                                        SelectedItem="{Binding AppSettings.Terminology.Stage2AIModel}">
                                        <ComboBox.ItemTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding Converter={StaticResource AIModelConverter}}" />
                                            </DataTemplate>
                                        </ComboBox.ItemTemplate>
                                    </ComboBox>
                                </StackPanel>
                                <StackPanel Spacing="5">
                                    <TextBlock FontWeight="SemiBold" Text="Gönderim Adeti:" />
                                    <NumericUpDown
                                        Width="120"
                                        HorizontalAlignment="Left"
                                        FormatString="0"
                                        Maximum="100"
                                        Minimum="1"
                                        Value="{Binding AppSettings.Terminology.Stage2BatchSize}" />
                                </StackPanel>
                       <StackPanel Spacing="5" IsVisible="{Binding IsTextContextColumnVisible}">
                                    <TextBlock FontWeight="SemiBold" Text="Metin Bağlam Sütunları:" />
                                    <Border
                                        Width="200"
                                        Height="80"
                                        HorizontalAlignment="Left"
                                        BorderBrush="Gray"
                                        BorderThickness="1"
                                        CornerRadius="3">
                                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                                            <ItemsControl ItemsSource="{Binding AvailableTextContextColumns}">
                                                <ItemsControl.ItemTemplate>
                                                    <DataTemplate>
                                                        <CheckBox
                                             Margin="5,2"
                                             Content="{Binding}"
                                             IsChecked="{Binding $parent[UserControl].DataContext.SelectedTextContextColumns, Converter={x:Static converters:StringCollectionContainsConverter.Instance}, ConverterParameter={Binding}, Mode=OneWay}"
                                             Command="{Binding $parent[UserControl].DataContext.ToggleTextContextColumnCommand}"
                                             CommandParameter="{Binding}" />
                                                    </DataTemplate>
                                                </ItemsControl.ItemTemplate>
                                            </ItemsControl>
                                        </ScrollViewer>
                                    </Border>
                                </StackPanel>
                            </StackPanel>

                            <StackPanel IsVisible="{Binding IsPhaseThreeActive, Converter={StaticResource BooleanToVisibilityConverter}}" Spacing="10">
                                <TextBlock
                                    FontSize="16"
                                    FontWeight="Bold"
                                    Text="Aşama 3 - Terim Onayı" />
                                <StackPanel Spacing="5">
                                    <TextBlock FontWeight="SemiBold" Text="AI Modeli:" />
                                    <ComboBox
                                        Width="200"
                                        HorizontalAlignment="Left"
                                        ItemsSource="{Binding AppSettings.General.AvailableAIModels}"
                                        SelectedItem="{Binding AppSettings.Terminology.Stage3AIModel}">
                                        <ComboBox.ItemTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding Converter={StaticResource AIModelConverter}}" />
                                            </DataTemplate>
                                        </ComboBox.ItemTemplate>
                                    </ComboBox>
                                </StackPanel>
                                <StackPanel Spacing="5">
                                    <TextBlock FontWeight="SemiBold" Text="Gönderim Adeti:" />
                                    <NumericUpDown
                                        Width="120"
                                        HorizontalAlignment="Left"
                                        FormatString="0"
                                        Maximum="100"
                                        Minimum="1"
                                        Value="{Binding AppSettings.Terminology.Stage3BatchSize}" />
                                </StackPanel>
               <StackPanel Spacing="5" IsVisible="{Binding IsTextContextColumnVisible}">
                                    <TextBlock FontWeight="SemiBold" Text="Metin Bağlam Sütunları:" />
                                    <Border
                                        Width="200"
                                        Height="80"
                                        HorizontalAlignment="Left"
                                        BorderBrush="Gray"
                                        BorderThickness="1"
                                        CornerRadius="3">
                                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                                            <ItemsControl ItemsSource="{Binding AvailableTextContextColumns}">
                                                <ItemsControl.ItemTemplate>
                                                    <DataTemplate>
                                                        <CheckBox
                                             Margin="5,2"
                                             Content="{Binding}"
                                             IsChecked="{Binding $parent[UserControl].DataContext.SelectedTextContextColumns, Converter={x:Static converters:StringCollectionContainsConverter.Instance}, ConverterParameter={Binding}, Mode=OneWay}"
                                             Command="{Binding $parent[UserControl].DataContext.ToggleTextContextColumnCommand}"
                                             CommandParameter="{Binding}" />
                                                    </DataTemplate>
                                                </ItemsControl.ItemTemplate>
                                            </ItemsControl>
                                        </ScrollViewer>
                                    </Border>
                                </StackPanel>
                            </StackPanel>

                            <!--  Model İstatistikleri  -->
                            <StackPanel Spacing="10">
                                <TextBlock
                                    FontSize="16"
                                    FontWeight="Bold"
                                    Text="Model İstatistikleri" />

                                <Grid RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto" RowSpacing="5">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="2*" />
                                        <ColumnDefinition Width="*" />
                                    </Grid.ColumnDefinitions>

                                    <TextBlock
                                        Grid.Row="0"
                                        Grid.Column="0"
                                        Margin="0,0,10,0"
                                        FontWeight="SemiBold"
                                        Text="Günlük Toplam Kota:" />
                                    <TextBlock
                                        Grid.Row="0"
                                        Grid.Column="1"
                                        HorizontalAlignment="Right"
                                        Text="{Binding CurrentModelDailyQuota}" />

                                    <TextBlock
                                        Grid.Row="1"
                                        Grid.Column="0"
                                        Margin="0,0,10,0"
                                        FontWeight="SemiBold"
                                        Text="Kalan Kota:" />
                                    <TextBlock
                                        Grid.Row="1"
                                        Grid.Column="1"
                                        HorizontalAlignment="Right"
                                        Text="{Binding CurrentModelRemainingQuota}" />

                                    <TextBlock
                                        Grid.Row="2"
                                        Grid.Column="0"
                                        Margin="0,0,10,0"
                                        FontWeight="SemiBold"
                                        Text="İhtiyaç Duyulan Kota:" />
                                    <TextBlock
                                        Grid.Row="2"
                                        Grid.Column="1"
                                        HorizontalAlignment="Right"
                                        Classes.QuotaWarning="{Binding IsQuotaExceeded}"
                                        Text="{Binding RequiredQuota}" />
                                    <TextBlock
                                        Grid.Row="3"
                                        Grid.Column="0"
                                        Margin="0,0,10,0"
                                        FontWeight="SemiBold"
                                        IsVisible="{Binding ShowRemainingTime}"
                                        Text="Kalan Süre:" />
                                    <TextBlock
                                        Grid.Row="3"
                                        Grid.Column="1"
                                        HorizontalAlignment="Right"
                                        IsVisible="{Binding ShowRemainingTime}"
                                        Text="{Binding EstimatedRemainingTime}" />


                                </Grid>
                            </StackPanel>
                        </StackPanel>
                    </ScrollViewer>
                </Border>
            </Grid>

            <GridSplitter
                Grid.Row="2"
                Height="5"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Center"
                Background="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                ResizeDirection="Rows" />

            <!--  Alt Panel: Log ve İstatistik Paneli  -->
            <shared:LogStatisticsPanel Grid.Row="3" DataContext="{Binding LogStatisticsPanelVm}" />
        </Grid>
    </Border>
</UserControl>
