using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.IO;
using System.Text;
using Avalonia.Threading;

namespace TranslationAgent.Services
{
    public class LogService
    {
        private string _projectName = "Main";
        private string _logFilePath;
        private string _hardLogFilePath;
        private string _hardStage1LogFilePath;
        private string _hardStage2LogFilePath;
        private string _hardStage3LogFilePath; // Yeni
        private string _hardTranslateLogFilePath;
        private string _hardTranslateContextFilePath;
        private static readonly object LockObject = new object();
        private readonly StringBuilder _logBuffer = new StringBuilder();
        private readonly ToastService? _toastService;
        private readonly bool _hardLogEnabled;
        private readonly AppSettings _appSettings;

        // Event-based UI log sistemi - Her LogState için ayrı event'ler
        public event Action<string>? UILogAdded; // Genel event (geriye uyumluluk için)
        public event Action<string>? MainLogAdded;
        public event Action<string>? Stage1LogAdded;
        public event Action<string>? Stage2LogAdded;
        public event Action<string>? Stage3LogAdded; // Yeni
        public event Action<string>? ContextLogAdded;
        public event Action<string>? TranslateLogAdded;

        public enum LogState
        {
            Main,
            Stage1,
            Stage2,
            Stage3, // Yeni
            Context,
            Translate
        }

        public LogService(ToastService? toastService = null, AppSettings? appSettings = null)
        {
            _toastService = toastService;
            _appSettings = appSettings ?? AppSettings.Load();
            _hardLogEnabled = _appSettings.General.HardLog;

            // Create project-specific log directory
            var baseLogDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");


            if (!Directory.Exists(baseLogDirectory))
            {
                Directory.CreateDirectory(baseLogDirectory);
            }

            _logFilePath = Path.Combine(baseLogDirectory, "app.log");
            _hardLogFilePath = Path.Combine(baseLogDirectory, "app_hard.log");

        }

        public void UpdateProjectName(string newProjectName)
        {
            var baseLogDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
            var projectLogDirectory = Path.Combine(baseLogDirectory, newProjectName);

            if (!Directory.Exists(projectLogDirectory))
            {
                Directory.CreateDirectory(projectLogDirectory);
            }

            // Flush any remaining logs before changing paths
            FlushLogsToFile();

            // Update paths
            _projectName = newProjectName;


            if (!Directory.Exists(projectLogDirectory))
            {
                Directory.CreateDirectory(projectLogDirectory);
            }
            _hardStage1LogFilePath = Path.Combine(projectLogDirectory, "app_hard_stage1.log");
            _hardStage2LogFilePath = Path.Combine(projectLogDirectory, "app_hard_stage2.log");
            _hardStage3LogFilePath = Path.Combine(projectLogDirectory, "app_hard_stage3.log"); // Yeni
            _hardTranslateLogFilePath = Path.Combine(projectLogDirectory, "app_hard_translate.log");
            _hardTranslateContextFilePath = Path.Combine(projectLogDirectory, "app_hard_context.log");
        }



        private void AddToUILog(string message, LogState logState = LogState.Main)
        {
            // Event'i UI thread'de tetikle
            Action<string>? targetEvent = logState switch
            {
                LogState.Main => MainLogAdded,
                LogState.Stage1 => Stage1LogAdded,
                LogState.Stage2 => Stage2LogAdded,
                LogState.Stage3 => Stage3LogAdded, // Yeni
                LogState.Context => ContextLogAdded,
                LogState.Translate => TranslateLogAdded,
                _ => MainLogAdded
            };

            // Genel event'i de tetikle (geriye uyumluluk için)
            if (UILogAdded != null)
            {
                if (Dispatcher.UIThread.CheckAccess())
                {
                    UILogAdded.Invoke(message);
                }
                else
                {
                    Dispatcher.UIThread.Post(() => UILogAdded.Invoke(message));
                }
            }

            // LogState'e özel event'i tetikle
            if (targetEvent != null)
            {
                if (Dispatcher.UIThread.CheckAccess())
                {
                    targetEvent.Invoke(message);
                }
                else
                {
                    Dispatcher.UIThread.Post(() => targetEvent.Invoke(message));
                }
            }
        }

        public void Info(string message, bool addToUI = false, LogState logState = LogState.Main)
        {
            var logMessage = FormatLogMessage("INFO", message);

#if DEBUG
            Console.WriteLine(logMessage);
#endif

            AddToBuffer(logMessage);
            if (_hardLogEnabled)
            {
                WriteHardLog(logMessage, logState);
            }

            if (addToUI)
            {
                AddToUILog(logMessage, logState);
            }
        }

        public void Success(string message, bool addToUI = false, LogState logState = LogState.Main)
        {
            var logMessage = FormatLogMessage("SUCCESS", message);

#if DEBUG
            Console.WriteLine(logMessage);
#endif

            if (_hardLogEnabled)
            {
                WriteHardLog(logMessage, logState);
            }

            if (addToUI)
            {
                AddToUILog(logMessage, logState);
            }

            _toastService?.ShowToast(message, ToastType.Success);
        }

        public void Warning(string message, bool addToUI = false, LogState logState = LogState.Main)
        {
            var logMessage = FormatLogMessage("WARNING", message);

#if DEBUG
            Console.WriteLine(logMessage);
#endif

            AddToBuffer(logMessage);
            if (_hardLogEnabled)
            {
                WriteHardLog(logMessage, logState);
            }

            if (addToUI)
            {
                AddToUILog(logMessage, logState);
            }

            _toastService?.ShowToast(message, ToastType.Warning);
        }

        public void Error(string message, Exception? exception = null, bool addToUI = false, LogState logState = LogState.Main)
        {
            var logMessage = FormatLogMessage("ERROR", message);
            if (exception != null)
            {
                logMessage += $"\nException: {exception.Message}\nStackTrace: {exception.StackTrace}";
            }

#if DEBUG
            Console.WriteLine(logMessage);
#endif

            AddToBuffer(logMessage);
            if (_hardLogEnabled)
            {
                WriteHardLog(logMessage, logState);
            }

            if (addToUI)
            {
                AddToUILog(logMessage, logState);
            }

            var toastMessage = exception != null ? $"{message}\n{exception.Message}" : message;
            _toastService?.ShowToast(toastMessage, ToastType.Error);
        }

        private void WriteHardLog(string logMessage, LogState logState = LogState.Main)
        {
            lock (LockObject)
            {
                try
                {
                    string targetPath = logState switch
                    {
                        LogState.Stage1 => _hardStage1LogFilePath,
                        LogState.Stage2 => _hardStage2LogFilePath,
                        LogState.Stage3 => _hardStage3LogFilePath, // Yeni
                        LogState.Translate => _hardTranslateLogFilePath,
                        LogState.Context => _hardTranslateContextFilePath,
                        LogState.Main => _hardLogFilePath,
                        _ => _hardLogFilePath,
                    };
                    if (targetPath == null)
                    {

                        File.AppendAllText(_hardLogFilePath, logMessage + Environment.NewLine);

                    }
                    else
                    {
                        File.AppendAllText(targetPath, logMessage + Environment.NewLine);
                    }

                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to write hard logs to file for project {_projectName}: {ex.Message}");
                }
            }
        }

        private string FormatLogMessage(string level, string message)
        {
            return $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [{level}] {message}";
        }

        private void AddToBuffer(string logMessage)
        {
            lock (LockObject)
            {
                _logBuffer.AppendLine(logMessage);
            }
        }

        public void FlushLogsToFile()
        {
            lock (LockObject)
            {
                if (_logBuffer.Length > 0)
                {
                    try
                    {
                        File.AppendAllText(_logFilePath, _logBuffer.ToString());
                        _logBuffer.Clear();
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Failed to write logs to file for project {_projectName}: {ex.Message}");
                    }
                }
            }
        }
    }
}