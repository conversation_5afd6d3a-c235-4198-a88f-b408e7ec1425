using System;
using System.IO; // Added for file operations
using System.Threading.Tasks;
using Avalonia;
using Catalyst;
using Microsoft.Extensions.DependencyInjection;
using Mosaik.Core;
using TranslationAgent;
using TranslationAgent.Services;
using SmartComponents.LocalEmbeddings;
using Microsoft.Extensions.DependencyInjection;
namespace TranslationAgent;

class Program
{
    private static LogService? _logService;
    private static IServiceProvider? _serviceProvider;

    // Initialization code. Don't use any Avalonia, third-party APIs or any
    // SynchronizationContext-reliant code before AppMain is called: things aren't initialized
    // yet and stuff might break.
    [STAThread]
    public static async Task Main(string[] args)
    {
        try
        {
            (await BuildAvaloniaApp())
                .StartWithClassicDesktopLifetime(args);
        }
        catch (Exception e)
        {
            Console.WriteLine($"Kritik Hata: {e.Message}");
            _logService?.Error("Uygulama başlatılırken bir hata oluştu", e);
            _logService?.FlushLogsToFile();
        }
    }

    // Avalonia configuration, don't remove; also used by visual designer.
    public static async Task<AppBuilder> BuildAvaloniaApp()
    {
        var builder = AppBuilder.Configure<App>()
            .UsePlatformDetect()
            .WithInterFont()
            .LogToTrace();

        try
        {

            // Register services
            var services = new ServiceCollection();

            Catalyst.Models.English.Register();
            Storage.Current = new DiskStorage("catalyst-models");
            var nlp = await Pipeline.ForAsync(Language.English);
            services.AddSingleton(nlp);

            // Önce ToastService'i kaydet
            var toastService = new ToastService();
            services.AddSingleton(toastService);

            // AppSettings'i yükle
            var appSettings = AppSettings.Load();
            services.AddSingleton(appSettings);

            // LogService'i ToastService ve AppSettings ile başlat ve kaydet
            _logService = new LogService(toastService, appSettings);
            services.AddSingleton(_logService);

            // LogService'i AppSettings'e aktar
            AppSettings.InitializeLogService(_logService);

            // GeminiService'i en son kaydet çünkü AppSettings'e bağımlı
            services.AddSingleton<GeminiService>();

            // AnthropicService'i en son kaydet çünkü AppSettings'e bağımlı
            services.AddSingleton<AnthropicService>();

            // CrawlerService'i kaydet
            services.AddSingleton<CrawlerService>();

            // LocalEmbedder'i kaydet
            services.AddSingleton<LocalEmbedder>();


            _serviceProvider = services.BuildServiceProvider();

            _logService.Info("Uygulama servisleri başarıyla başlatıldı.");

        }
        catch (Exception ex)
        {
            Console.WriteLine($"Servis kaydı sırasında hata: {ex.Message}");
            throw;
        }

        return builder;
    }

    public static IServiceProvider GetServiceProvider()
    {
        if (_serviceProvider == null)
        {
            throw new InvalidOperationException("Service provider is not initialized");
        }
        return _serviceProvider;
    }
}