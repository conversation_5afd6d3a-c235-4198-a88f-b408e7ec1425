using System;
using System.Globalization;
using Avalonia.Data.Converters;

namespace TranslationAgent.Converters
{
    public class EmptyTooltipConverter : IValueConverter
    {
        public static readonly EmptyTooltipConverter Instance = new();

        public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            if (value is string text)
            {
                return string.IsNullOrWhiteSpace(text) ? null : text;
            }
            return null;
        }

        public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}