<?xml version="1.0" encoding="utf-8" ?>
<UserControl
        x:Class="TranslationAgent.Features.GoogleSheets.Views.GoogleSheetsPanelView"
        xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="clr-namespace:TranslationAgent.Features.GoogleSheets.ViewModels"
        d:DesignHeight="600"
        d:DesignWidth="300"
        x:DataType="vm:GoogleSheetsPanelViewModel"
        mc:Ignorable="d">
        <UserControl.Resources>
                <StreamGeometry x:Key="ArrowRightRegular">M11.5039 3.99609L19 11.5039L11.5039 19L10.4961 17.9961L16.293 12.1992H3V10.7969H16.293L10.4961 5L11.5039 3.99609Z</StreamGeometry>
                <StreamGeometry x:Key="ArrowLeftRegular">M12.4961 19.0039L5 11.4961L12.4961 4L13.5039 5.00391L7.707 10.8008H21V12.2031H7.707L13.5039 18L12.4961 19.0039Z</StreamGeometry>
        </UserControl.Resources>

        <Design.DataContext>
                <vm:GoogleSheetsPanelViewModel />
        </Design.DataContext>

        <Border
                Name="MainPanel"
                HorizontalAlignment="Left"
                Background="{DynamicResource SystemControlPageBackgroundChromeLowBrush}"
                BorderBrush="{DynamicResource SystemControlBackgroundBaseLowBrush}"
                BorderThickness="1,0,1,0">
                <Border.Transitions>
                        <Transitions>
                                <DoubleTransition
                                        Easing="CubicEaseOut"
                                        Property="Width"
                                        Duration="0:0:0.2" />
                                <DoubleTransition
                                        Easing="CubicEaseOut"
                                        Property="Opacity"
                                        Duration="0:0:0.15" />
                        </Transitions>
                </Border.Transitions>

                <DockPanel>
                        <Grid Margin="0,5,0,10" DockPanel.Dock="Top">
                                <Button
                                        Name="ToggleButton"
                                        Width="24"
                                        Height="24"
                                        Margin="2,0,0,0"
                                        HorizontalAlignment="Left"
                                        VerticalAlignment="Center"
                                        Click="OnToggleClick">
                                        <Path
                                                Name="ToggleIcon"
                                                Width="12"
                                                Height="12"
                                                Data="M 12,2 L 4,10 L 12,18"
                                                Fill="{DynamicResource ButtonForeground}"
                                                Stretch="Uniform"
                                                Stroke="{DynamicResource ButtonForeground}"
                                                StrokeThickness="2" />
                                </Button>
                                <TextBlock
                                        Name="HeaderText"
                                        Margin="30,0,0,0"
                                        VerticalAlignment="Center"
                                        FontSize="14"
                                        FontWeight="Bold"
                                        Text="Google Sheets Connector" />
                        </Grid>

                        <ScrollViewer Name="ContentPanel">
                                <StackPanel Margin="0,0,10,10" Spacing="10">
                                        <TextBlock
                                                Margin="10,0,0,0"
                                                IsVisible="{Binding SelectedSpreadsheet, Converter={x:Static ObjectConverters.IsNotNull}}"
                                                Text="{Binding SelectedSpreadsheetName}" />

                                        <Button
                                                Margin="10,0,0,0"
                                                Command="{Binding LoadAccessibleSpreadsheetsCommand}"
                                                Content="E-Tablo Listesini Yükle"
                                                IsEnabled="{Binding CanLoadSpreadsheets, Mode=OneWay}" />

                                        <TextBlock
                                                Margin="10,0,0,0"
                                                IsVisible="{Binding AccessibleSpreadsheets.Count}"
                                                Text="E-Tablo Seç:" />
                                        <ComboBox
                                                Width="280"
                                                Margin="10,0,0,0"
                                                HorizontalAlignment="Left"
                                                DisplayMemberBinding="{Binding Name}"
                                                IsEnabled="{Binding !IsLoadingSpreadsheets}"
                                                IsVisible="{Binding AccessibleSpreadsheets.Count}"
                                                ItemsSource="{Binding AccessibleSpreadsheets}"
                                                SelectedItem="{Binding SelectedSpreadsheet}" />

                                        <StackPanel 
                                                Orientation="Horizontal" 
                                                Spacing="10"
                                                Margin="10,0,0,0"
                                                IsVisible="{Binding SelectedSpreadsheet, Converter={x:Static ObjectConverters.IsNotNull}}">
                                                <Button
                                                        Command="{Binding ListSheetsCommand}"
                                                        Content="Sayfaları Listele" />
                                                <Button
                                                        Command="{Binding AutoFetchCommand}"
                                                        Content="Otomatik Çek"
                                                        Background="{DynamicResource SystemAccentColor}"
                                                        Foreground="White" />
                                        </StackPanel>

                                        <Rectangle
                                                Height="1"
                                                Margin="10,3,10,0"
                                                Fill="{DynamicResource SystemControlForegroundBaseMediumBrush}"
                                                IsVisible="{Binding SelectedSpreadsheet, Converter={x:Static ObjectConverters.IsNotNull}}" />

                                        <!--  Accordion Sections  -->
                                        <Expander
                                Name="TerminologyExpander"
                                Margin="10,5,0,0"
                                HorizontalAlignment="Stretch"
                                IsExpanded="{Binding IsTerminologyExpanderExpanded}"
                                IsVisible="{Binding SelectedSpreadsheet, Converter={x:Static ObjectConverters.IsNotNull}}">
                                                <Expander.Header>
                                                        <StackPanel Orientation="Horizontal" Spacing="8">
                                                                <TextBlock VerticalAlignment="Center" Text="Terimce" />
                                                                <TextBlock
                                                                        VerticalAlignment="Center"
                                                                        FontWeight="Bold"
                                                                        Foreground="Green"
                                                                        IsVisible="{Binding IsTerminologyDataFetched}"
                                                                        Text="✓" />
                                                        </StackPanel>
                                                </Expander.Header>
                                                <StackPanel Spacing="10">
                                                        <StackPanel Orientation="Vertical" Spacing="10">
                                                                <ComboBox
                                                                        HorizontalAlignment="Stretch"
                                                                        DisplayMemberBinding="{Binding SheetName}"
                                                                        IsEnabled="{Binding !IsLoadingSheets}"
                                                                        ItemsSource="{Binding AvailableSheets}"
                                                                        SelectedItem="{Binding SelectedTerminologySheet}" />
                                                                <Button
                                                                        Command="{Binding CreateTerminologySheetCommand}"
                                                                        Content="Terimce Sayfası Oluştur"
                                                                        IsVisible="{Binding CanCreateTerminologySheet}" />
                                                        </StackPanel>
                                                        <StackPanel
                                                                IsVisible="{Binding CanFilterTerminologyData}"
                                                                Orientation="Vertical"
                                                                Spacing="5">
                                                                <ItemsControl ItemsSource="{Binding TerminologyFilterEntries}">
                                                                        <ItemsControl.ItemTemplate>
                                                                                <DataTemplate>
                                                                                        <Border
                                                                                                Margin="0,5"
                                                                                                Padding="5"
                                                                                                BorderBrush="Gray"
                                                                                                BorderThickness="1"
                                                                                                CornerRadius="3">
                                                                                                <Grid RowDefinitions="Auto,5,Auto">
                                                                                                        <Grid Grid.Row="0" ColumnDefinitions="100,5,*,5,Auto">
                                                                                                                <ComboBox
                                                                                                                        Grid.Column="0"
                                                                                                                        Height="32"
                                                                                                                        HorizontalAlignment="Stretch"
                                                                                                                        VerticalAlignment="Center"
                                                                                                                        ItemsSource="{Binding $parent[ItemsControl].DataContext.TerminologyColumnHeaders}"
                                                                                                                        SelectedItem="{Binding ColumnName}" />

                                                                                                                <ComboBox
                                                                                                                        Grid.Column="2"
                                                                                                                        Height="32"
                                                                                                                        HorizontalAlignment="Stretch"
                                                                                                                        VerticalAlignment="Center"
                                                                                                                        ItemsSource="{Binding AvailableTypes}"
                                                                                                                        SelectedItem="{Binding SelectedType}" />

                                                                                                                <Button
                                                                                                                        Grid.Column="4"
                                                                                                                        VerticalAlignment="Center"
                                                                                                                        Command="{Binding $parent[ItemsControl].DataContext.RemoveTerminologyFilterCommand}"
                                                                                                                        CommandParameter="{Binding}"
                                                                                                                        Content="X" />
                                                                                                        </Grid>

                                                                                                        <Grid Grid.Row="2" ColumnDefinitions="Auto,5,*">
                                                                                                                <ComboBox
                                                                                                                        Grid.Column="0"
                                                                                                                        Height="32"
                                                                                                                        HorizontalAlignment="Left"
                                                                                                                        VerticalAlignment="Center"
                                                                                                                        ItemsSource="{Binding AvailableOperators}"
                                                                                                                        SelectedItem="{Binding SelectedOperator}" />

                                                                                                                <TextBox
                                                                                                                        Grid.Column="2"
                                                                                                                        Height="32"
                                                                                                                        VerticalAlignment="Center"
                                                                                                                        Text="{Binding FilterValue}"
                                                                                                                        Watermark="Filtre değeri..." />
                                                                                                        </Grid>
                                                                                                </Grid>
                                                                                        </Border>
                                                                                </DataTemplate>
                                                                        </ItemsControl.ItemTemplate>
                                                                </ItemsControl>

                                                                <Button
                                                                        HorizontalAlignment="Left"
                                                                        Command="{Binding AddTerminologyFilterCommand}"
                                                                        Content="+ Filtre Ekle" />
                                                        </StackPanel>

                                                        <Button
                                                                Command="{Binding FetchTerminologyDataCommand}"
                                                                Content="Terimleri Getir"
                                                                IsVisible="{Binding CanFetchTerminologyData}" />

                                                        <TextBlock
                                                                FontWeight="Bold"
                                                                IsVisible="{Binding IsTerminologyDataFetched}"
                                                                Text="{Binding TerminologyData.Count, StringFormat='Terimler Getirildi. Toplam: {0}'}" />
                                                </StackPanel>
                                        </Expander>

                                        <Expander
                                Name="ContextExpander"
                                Margin="10,5,0,0"
                                HorizontalAlignment="Stretch"
                                IsExpanded="{Binding IsContextExpanderExpanded}"
                                IsVisible="{Binding SelectedSpreadsheet, Converter={x:Static ObjectConverters.IsNotNull}}">
                                                <Expander.Header>
                                                        <StackPanel Orientation="Horizontal" Spacing="8">
                                                                <TextBlock VerticalAlignment="Center" Text="Bağlamlar" />
                                                                <TextBlock
                                                                        VerticalAlignment="Center"
                                                                        FontWeight="Bold"
                                                                        Foreground="Green"
                                                                        IsVisible="{Binding IsContextDataFetched}"
                                                                        Text="✓" />
                                                        </StackPanel>
                                                </Expander.Header>
                                                <StackPanel Spacing="10">
                                                        <StackPanel Orientation="Vertical" Spacing="10">
                                                                <ComboBox
                                                                        HorizontalAlignment="Stretch"
                                                                        DisplayMemberBinding="{Binding SheetName}"
                                                                        IsEnabled="{Binding !IsLoadingSheets}"
                                                                        ItemsSource="{Binding AvailableSheets}"
                                                                        SelectedItem="{Binding SelectedContextSheet}" />
                                                                <Button
                                                                        Command="{Binding CreateContextSheetCommand}"
                                                                        Content="Bağlamlar Sayfası Oluştur"
                                                                        IsVisible="{Binding CanCreateContextSheet}" />
                                                        </StackPanel>

                                                        <StackPanel
                                                                IsVisible="{Binding CanFilterContextData}"
                                                                Orientation="Vertical"
                                                                Spacing="5">
                                                                <ItemsControl ItemsSource="{Binding ContextFilterEntries}">
                                                                        <ItemsControl.ItemTemplate>
                                                                                <DataTemplate>
                                                                                        <Border
                                                                                                Margin="0,5"
                                                                                                Padding="5"
                                                                                                BorderBrush="Gray"
                                                                                                BorderThickness="1"
                                                                                                CornerRadius="3">
                                                                                                <Grid RowDefinitions="Auto,5,Auto">
                                                                                                        <Grid Grid.Row="0" ColumnDefinitions="100,5,*,5,Auto">
                                                                                                                <ComboBox
                                                                                                                        Grid.Column="0"
                                                                                                                        Height="32"
                                                                                                                        HorizontalAlignment="Stretch"
                                                                                                                        VerticalAlignment="Center"
                                                                                                                        ItemsSource="{Binding $parent[ItemsControl].DataContext.ContextColumnHeaders}"
                                                                                                                        SelectedItem="{Binding ColumnName}" />

                                                                                                                <ComboBox
                                                                                                                        Grid.Column="2"
                                                                                                                        Height="32"
                                                                                                                        HorizontalAlignment="Stretch"
                                                                                                                        VerticalAlignment="Center"
                                                                                                                        ItemsSource="{Binding AvailableTypes}"
                                                                                                                        SelectedItem="{Binding SelectedType}" />

                                                                                                                <Button
                                                                                                                        Grid.Column="4"
                                                                                                                        VerticalAlignment="Center"
                                                                                                                        Command="{Binding $parent[ItemsControl].DataContext.RemoveContextFilterCommand}"
                                                                                                                        CommandParameter="{Binding}"
                                                                                                                        Content="X" />
                                                                                                        </Grid>

                                                                                                        <Grid Grid.Row="2" ColumnDefinitions="Auto,5,*">
                                                                                                                <ComboBox
                                                                                                                        Grid.Column="0"
                                                                                                                        Height="32"
                                                                                                                        HorizontalAlignment="Left"
                                                                                                                        VerticalAlignment="Center"
                                                                                                                        ItemsSource="{Binding AvailableOperators}"
                                                                                                                        SelectedItem="{Binding SelectedOperator}" />

                                                                                                                <TextBox
                                                                                                                        Grid.Column="2"
                                                                                                                        Height="32"
                                                                                                                        VerticalAlignment="Center"
                                                                                                                        Text="{Binding FilterValue}"
                                                                                                                        Watermark="Filtre değeri..." />
                                                                                                        </Grid>
                                                                                                </Grid>
                                                                                        </Border>
                                                                                </DataTemplate>
                                                                        </ItemsControl.ItemTemplate>
                                                                </ItemsControl>

                                                                <Button
                                                                        HorizontalAlignment="Left"
                                                                        Command="{Binding AddContextFilterCommand}"
                                                                        Content="+ Filtre Ekle" />
                                                        </StackPanel>

                                                        <Button
                                                                Command="{Binding FetchContextDataCommand}"
                                                                Content="Bağlamları Getir"
                                                                IsVisible="{Binding CanFetchContextData}" />

                                                        <TextBlock
                                                                FontWeight="Bold"
                                                                IsVisible="{Binding IsContextDataFetched}"
                                                                Text="{Binding ContextData.Count, StringFormat='Bağlamlar Getirildi. Toplam: {0}'}" />
                                                </StackPanel>
                                        </Expander>

                                        <Expander
                                Name="TextsExpander"
                                Margin="10,5,0,0"
                                HorizontalAlignment="Stretch"
                                IsExpanded="{Binding IsTextExpanderExpanded}"
                                IsVisible="{Binding SelectedSpreadsheet, Converter={x:Static ObjectConverters.IsNotNull}}">
                                                <Expander.Header>
                                                        <StackPanel Orientation="Horizontal" Spacing="8">
                                                                <TextBlock VerticalAlignment="Center" Text="Metinler" />
                                                                <TextBlock
                                                                        VerticalAlignment="Center"
                                                                        FontWeight="Bold"
                                                                        Foreground="Green"
                                                                        IsVisible="{Binding IsTextDataFetched}"
                                                                        Text="✓" />
                                                        </StackPanel>
                                                </Expander.Header>
                                                <StackPanel Spacing="10">
                                                        <StackPanel Orientation="Vertical" Spacing="10">
                                                                <ComboBox
                                                                        HorizontalAlignment="Stretch"
                                                                        DisplayMemberBinding="{Binding SheetName}"
                                                                        IsEnabled="{Binding !IsLoadingSheets}"
                                                                        ItemsSource="{Binding AvailableSheets}"
                                                                        SelectedItem="{Binding SelectedTextSheet}" />
                                                        </StackPanel>

                                                        <StackPanel
                                                                IsVisible="{Binding CanFilterTextData}"
                                                                Orientation="Vertical"
                                                                Spacing="5">
                                                                <ItemsControl ItemsSource="{Binding TextFilterEntries}">
                                                                        <ItemsControl.ItemTemplate>
                                                                                <DataTemplate>
                                                                                        <Border
                                                                                                Margin="0,5"
                                                                                                Padding="5"
                                                                                                BorderBrush="Gray"
                                                                                                BorderThickness="1"
                                                                                                CornerRadius="3">
                                                                                                <Grid RowDefinitions="Auto,5,Auto">
                                                                                                        <Grid Grid.Row="0" ColumnDefinitions="100,5,*,5,Auto">
                                                                                                                <ComboBox
                                                                                                                        Grid.Column="0"
                                                                                                                        Height="32"
                                                                                                                        HorizontalAlignment="Stretch"
                                                                                                                        VerticalAlignment="Center"
                                                                                                                        ItemsSource="{Binding $parent[ItemsControl].DataContext.TextColumnHeaders}"
                                                                                                                        SelectedItem="{Binding ColumnName}" />

                                                                                                                <ComboBox
                                                                                                                        Grid.Column="2"
                                                                                                                        Height="32"
                                                                                                                        HorizontalAlignment="Stretch"
                                                                                                                        VerticalAlignment="Center"
                                                                                                                        ItemsSource="{Binding AvailableTypes}"
                                                                                                                        SelectedItem="{Binding SelectedType}" />

                                                                                                                <Button
                                                                                                                        Grid.Column="4"
                                                                                                                        VerticalAlignment="Center"
                                                                                                                        Command="{Binding $parent[ItemsControl].DataContext.RemoveTextFilterCommand}"
                                                                                                                        CommandParameter="{Binding}"
                                                                                                                        Content="X" />
                                                                                                        </Grid>

                                                                                                        <Grid Grid.Row="2" ColumnDefinitions="Auto,5,*">
                                                                                                                <ComboBox
                                                                                                                        Grid.Column="0"
                                                                                                                        Height="32"
                                                                                                                        HorizontalAlignment="Left"
                                                                                                                        VerticalAlignment="Center"
                                                                                                                        ItemsSource="{Binding AvailableOperators}"
                                                                                                                        SelectedItem="{Binding SelectedOperator}" />

                                                                                                                <TextBox
                                                                                                                        Grid.Column="2"
                                                                                                                        Height="32"
                                                                                                                        VerticalAlignment="Center"
                                                                                                                        Text="{Binding FilterValue}"
                                                                                                                        Watermark="Filtre değeri..." />
                                                                                                        </Grid>
                                                                                                </Grid>
                                                                                        </Border>
                                                                                </DataTemplate>
                                                                        </ItemsControl.ItemTemplate>
                                                                </ItemsControl>

                                                                <Button
                                                                        HorizontalAlignment="Left"
                                                                        Command="{Binding AddTextFilterCommand}"
                                                                        Content="+ Filtre Ekle" />
                                                        </StackPanel>

                                                        <Button
                                                                Command="{Binding FetchTextDataCommand}"
                                                                Content="Metinleri Getir"
                                                                IsVisible="{Binding CanFetchTextData}" />

                                                        <TextBlock
                                                                FontWeight="Bold"
                                                                IsVisible="{Binding IsTextDataFetched}"
                                                                Text="{Binding TextData.Count, StringFormat='Metinler Getirildi. Toplam: {0}'}" />
                                                </StackPanel>
                                        </Expander>

                                        <ProgressBar
                                                Height="10"
                                                Margin="10,5,0,0"
                                                IsIndeterminate="True"
                                                IsVisible="{Binding IsLoading}" />
                                </StackPanel>
                        </ScrollViewer>
                </DockPanel>
        </Border>
</UserControl>
