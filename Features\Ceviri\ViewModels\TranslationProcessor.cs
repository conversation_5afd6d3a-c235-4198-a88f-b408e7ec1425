using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using TranslationAgent.Features.Ceviri.ViewModels;
using TranslationAgent.Features.Ceviri.Models;
using TranslationAgent.Features.Terminoloji.Models;
using TranslationAgent.Features.Baglam.Models;
using TranslationAgent.Services;
using TranslationAgent.Helpers;
using SmartComponents.LocalEmbeddings;
using TextStatistics;
using System.Text.Json;
using Mosaik.Core;

namespace TranslationAgent.Features.Ceviri.ViewModels
{
    public class TranslationProcessor
    {
        private readonly CeviriViewModel _viewModel;
        private CancellationToken _cancellationToken;

        public TranslationProcessor(CeviriViewModel viewModel)
        {
            _viewModel = viewModel;
        }

        public async Task ProcessAsync()
        {
            _cancellationToken = _viewModel.CancellationToken;

            _viewModel._logService.Info($"Çeviri işlemi başlatılıyor...", false, LogService.LogState.Translate);
            await ProcessTexts();
        }

        private async Task ProcessTexts()
        {
            var textsToProcess = PrepareTextsToProcess();
            if (textsToProcess == null || !textsToProcess.Any())
                return;

            await ProcessIndividualTexts(textsToProcess);
        }

        private List<Text>? PrepareTextsToProcess()
        {
            _viewModel.GoogleSheetsPanelVm.contextDataWithEmbeddings = _viewModel.GoogleSheetsPanelVm.ContextData.Where(c => c.ID != _viewModel.GoogleSheetsPanelVm.MainContext?.ID).Select(c => (c, c.Vector!.Value)).ToList();
            var textsToProcess = _viewModel.GoogleSheetsPanelVm.TextData.Where(t => t.NeedsTranslation).ToList();

            if (!textsToProcess.Any())
            {
                _viewModel._logService.Info("Çevrilecek metin bulunamadı.", true, LogService.LogState.Translate);
                return null;
            }

            _viewModel._logService.Info($"Çevrilecek metin sayısı: {textsToProcess.Count}", true, LogService.LogState.Translate);
            return textsToProcess;
        }

        private async Task ProcessIndividualTexts(List<Text> textsToProcess)
        {
            int currentText = 0;
            int totalTexts = textsToProcess.Count;

            _viewModel._logService.Info($"Toplam {totalTexts} metin tek tek işlenecek", true, LogService.LogState.Translate);

            foreach (var text in textsToProcess)
            {
                currentText++;

                if (!await ProcessSingleText(text, currentText, totalTexts))
                {
                    // Bir metin başarısız olursa diğerlerine devam et
                    _viewModel._logService.Warning($"Metin {currentText}/{totalTexts} başarısız oldu, diğer metinlere devam ediliyor", true, LogService.LogState.Translate);
                    continue;
                }

                // İstatistikleri güncelle
                _viewModel.UpdateStatistics();

                // Progress güncelle
                var progressPercentage = (double)currentText / totalTexts * 100;
                _viewModel.LogStatisticsPanelVm.SetProgressBar(progressPercentage);

                await Task.Delay(100, _cancellationToken);
            }
        }

        private async Task<bool> ProcessSingleText(Text text, int currentText, int totalTexts)
        {
            var textPreview = TextProcessingHelper.TruncateText(text.EN, 50);
            _viewModel._logService.Info($"Metin {currentText}/{totalTexts} işleniyor: {textPreview}", true, LogService.LogState.Translate);

            // Duraklat kontrolü
            await HandlePauseIfNeeded();
            _cancellationToken.ThrowIfCancellationRequested();

            try
            {
                var success = await ProcessSingleTextWithRetry(text);
                if (success)
                {
                    _viewModel._logService.Success($"Metin {currentText}/{totalTexts} başarıyla çevrildi", true, LogService.LogState.Translate);
                }
                return success;
            }
            catch (OperationCanceledException)
            {
                _viewModel._logService.Warning("Metin işlemi iptal edildi.", true, LogService.LogState.Translate);
                return false;
            }
            catch (Exception ex)
            {
                _viewModel._logService.Error($"Metin {currentText}/{totalTexts} işleme sürecinde beklenmeyen hata", ex, true, LogService.LogState.Translate);
                return false;
            }
        }

        private async Task<bool> ProcessSingleTextWithRetry(Text text)
        {
            var success = false;
            var retryCount = 0;
            const int maxRetries = 5;

            while (!success && retryCount < maxRetries)
            {
                await HandlePauseIfNeeded();
                _cancellationToken.ThrowIfCancellationRequested();

                try
                {
                    _viewModel._logService.Info($"Metin çevriliyor: {text.EN}", false, LogService.LogState.Translate);




                    // Aşama 1: Terminoloji, bağlam ve benzer çevirileri çıkar
                    var extractionResult = await ExtractTermsContextsAndSimilarTranslations(text);
                    if (extractionResult == null)
                    {
                        _viewModel._logService.Error("Aşama 1: Terim ve bağlam çıkarma işlemi başarısız", null, true, LogService.LogState.Translate);
                        throw new Exception("Terim ve bağlam çıkarma işlemi başarısız");
                    }

                    // Aşama 2: Çeviri stratejisi oluştur
                    var strategyResult = await GenerateTranslationStrategy(text, extractionResult);
                    if (strategyResult == null)
                    {
                        _viewModel._logService.Error("Aşama 2: Çeviri stratejisi oluşturma işlemi başarısız", null, true, LogService.LogState.Translate);
                        throw new Exception("Çeviri stratejisi oluşturma işlemi başarısız");
                    }

                    // Aşama 3: Çeviri zorluğunu hesapla
                    var difficultyResult = await CalculateTranslationDifficulty(text);
                    if (difficultyResult == null)
                    {
                        _viewModel._logService.Error("Aşama 3: Çeviri zorluğu hesaplama işlemi başarısız", null, true, LogService.LogState.Translate);
                        throw new Exception("Çeviri zorluğu hesaplama işlemi başarısız");
                    }

                    // Aşama 4: Nihai çeviriyi gerçekleştir
                    var translationResult = await PerformFinalTranslation(text, strategyResult, difficultyResult);
                    if (translationResult == null)
                    {
                        _viewModel._logService.Error("Aşama 4: Nihai çeviri işlemi başarısız", null, true, LogService.LogState.Translate);
                        throw new Exception("Nihai çeviri işlemi başarısız");
                    }



                    success = true;
                    _viewModel._logService.Success($"Metin başarıyla çevrildi: {text.EN}", true, LogService.LogState.Translate);
                }
                catch (OperationCanceledException)
                {
                    // İptal edildi, normal durum - hata değil
                    _viewModel._logService.Warning("Çeviri işlemi iptal edildi.", true, LogService.LogState.Translate);
                    return false;
                }
                catch (Exception ex)
                {
                    if (ex.Message.Contains("ServiceUnavailable") && ex.Message.Contains("model is overloaded"))
                    {
                        _viewModel._logService.Warning($"Deneme {retryCount}/{maxRetries} başarısız oldu. Model aşırı yüklendi. Tekrar deneniyor...", true, LogService.LogState.Stage1);
                    }
                    else
                    {

                        retryCount++;
                        if (retryCount < maxRetries)
                        {
                            _viewModel._logService.Warning($"Deneme {retryCount}/{maxRetries} başarısız oldu. Tekrar deneniyor... Hata: {ex.Message}", true, LogService.LogState.Stage1);

                            try
                            {
                                await Task.Delay(1000 * retryCount, _cancellationToken);
                            }
                            catch (OperationCanceledException)
                            {
                                _viewModel._logService.Warning("İşlem iptal edildi.", true, LogService.LogState.Stage1);
                                return false;
                            }
                        }
                        else
                        {
                            _viewModel._logService.Error($"Batch işlenirken maksimum deneme sayısına ulaşıldı", ex, true, LogService.LogState.Stage1);
                            return false;
                        }
                    }
                }
            }

            return success;
        }

        #region 4 Aşamalı Çeviri İşlem Metodları

        /// <summary>
        /// Aşama 1 sonuç veri yapısı
        /// </summary>
        public class ExtractionResult
        {
            public List<TermViewModel> FoundTerms { get; set; } = new();
            public List<Text> SimilarTranslations { get; set; } = new();
            public List<Context> RelevantContexts { get; set; } = new();
            public List<Text> RelevantTranslations_Pre { get; set; } = new();
            public List<Text> RelevantTranslations_Post { get; set; } = new();

        }

        /// <summary>
        /// Aşama 1: Tek metnin terminoloji, bağlam ve benzer çevirilerini çıkarma işlemi
        /// </summary>
        private async Task<ExtractionResult?> ExtractTermsContextsAndSimilarTranslations(Text text)
        {
            await HandlePauseIfNeeded();
            _cancellationToken.ThrowIfCancellationRequested();

            var textPreview = TextProcessingHelper.TruncateText(text.EN, 50);

            _viewModel._logService.Info($"Aşama 1: '{textPreview}' metni için terim, bağlam ve benzer çeviriler çıkarılıyor...", true, LogService.LogState.Translate);

            try
            {
                var result = new ExtractionResult();

                // 1. Terim çıkarma işlemi
                await ExtractTermsFromText(text, result);

                if (text.Vector == null)
                    text.Vector = await Task.Run(() => _viewModel.GoogleSheetsPanelVm._localEmbedder.Embed(text.EN));

                // 2. Benzer çeviriler bulma işlemi
                await FindSimilarTranslations(text, result);

                // 3. Bağlam bilgileri toplama işlemi
                await CollectRelevantContexts(text, result);

                // 4. Alt-Üst Bağlam bilgileri toplama işlemi
                await CollectRelevantTranslations(text, result);

                _viewModel._logService.Success($"Aşama 1 tamamlandı: {result.FoundTerms.Count} terim, {result.SimilarTranslations.Count} benzer çeviri, {result.RelevantContexts.Count} bağlam bulundu", true, LogService.LogState.Translate);

                return result;
            }
            catch (Exception ex)
            {
                _viewModel._logService.Error("Aşama 1'de hata oluştu", ex, true, LogService.LogState.Translate);
                return null;
            }
        }

        /// <summary>
        /// Tek metinde bulunan terimleri tespit eder
        /// </summary>
        private async Task ExtractTermsFromText(Text text, ExtractionResult result)
        {
            await HandlePauseIfNeeded();
            _cancellationToken.ThrowIfCancellationRequested();

            _viewModel._logService.Info("Terminoloji verileri analiz ediliyor...", false, LogService.LogState.Translate);

            if (!string.IsNullOrWhiteSpace(text.EN))
            {
                var foundTerms = FindMatchingTerms(text);
                result.FoundTerms.AddRange(foundTerms);
                result.FoundTerms = result.FoundTerms.Distinct().ToList();
            }

            _viewModel._logService.Info($"{result.FoundTerms.Count} terim tespit edildi", false, LogService.LogState.Translate);
        }

        /// <summary>
        /// Tek metin için benzer çevirileri bulur
        /// </summary>
        private async Task FindSimilarTranslations(Text text, ExtractionResult result)
        {
            await HandlePauseIfNeeded();
            _cancellationToken.ThrowIfCancellationRequested();

            _viewModel._logService.Info("Benzer çeviriler aranıyor...", false, LogService.LogState.Translate);

            // Çevrilmiş metinleri al
            var translatedTexts = _viewModel.GoogleSheetsPanelVm.TextData
                .Where(t => t.IsTranslated)
                .ToList();

            if (!translatedTexts.Any())
            {
                _viewModel._logService.Info("Çevrilmiş metin bulunamadı, benzer çeviri araması atlanıyor", false, LogService.LogState.Translate);
                return;
            }

            if (!string.IsNullOrWhiteSpace(text.EN))
            {
                // Basit string benzerlik kontrolü ile benzer çevirileri bul
                var similarTexts = translatedTexts
                    .Where(t => !t.EN.Equals(text.EN, StringComparison.OrdinalIgnoreCase))
                    .Where(t => !string.IsNullOrWhiteSpace(text.Lemma) && !string.IsNullOrWhiteSpace(t.Lemma) &&
                               IsTextSimilarWithRegex(text.Lemma, t.Lemma))
                    .Distinct()
                    .Take(12)
                    .ToList();

                if (similarTexts.Count < 12)
                {
                    similarTexts.AddRange(await _viewModel.GoogleSheetsPanelVm.SearchTextsForText(text, 12 - similarTexts.Count));
                }

                result.SimilarTranslations.AddRange(similarTexts);
                result.SimilarTranslations = result.SimilarTranslations.Distinct().ToList();
            }

            _viewModel._logService.Info($"{result.SimilarTranslations.Count} benzer çeviri bulundu", false, LogService.LogState.Translate);
        }


        /// <summary>
        /// Tek metin için ilgili bağlam bilgilerini toplar
        /// </summary>
        private async Task CollectRelevantContexts(Text text, ExtractionResult result)
        {
            await HandlePauseIfNeeded();
            _cancellationToken.ThrowIfCancellationRequested();

            _viewModel._logService.Info("İlgili bağlam bilgileri toplanıyor...", false, LogService.LogState.Translate);

            if (_viewModel.GoogleSheetsPanelVm.ContextData?.Any() != true ||
                _viewModel.GoogleSheetsPanelVm.contextDataWithEmbeddings?.Any() != true)
            {
                _viewModel._logService.Info("Bağlam verileri bulunamadı, bağlam araması atlanıyor", false, LogService.LogState.Translate);
                return;
            }

            if (!string.IsNullOrWhiteSpace(text.EN))
            {
                try
                {
                    var relevantContexts = await _viewModel.GoogleSheetsPanelVm.SearchContextsForText(text, 5);

                    foreach (var context in relevantContexts)
                    {
                        if (!result.RelevantContexts.Any(c => c.ID == context.ID))
                        {
                            result.RelevantContexts.Add(context);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _viewModel._logService.Warning($"'{text.ID}' ID'li metni için bağlam aramasında hata: {ex.Message}", false, LogService.LogState.Translate);
                }
            }

            _viewModel._logService.Info($"{result.RelevantContexts.Count} ilgili bağlam bulundu", false, LogService.LogState.Translate);
        }

        /// <summary>
        /// Hedef metnin öncesi ve sonrasındaki metinlerden benzer olanları toplar (Internal kullanım için)
        /// </summary>
        private async Task CollectRelevantTranslations(Text targetText, ExtractionResult result)
        {
            await HandlePauseIfNeeded();
            _cancellationToken.ThrowIfCancellationRequested();

            _viewModel._logService.Info("Önceki ve sonraki benzer çeviriler toplanıyor...", false, LogService.LogState.Translate);

            if (targetText == null)
            {
                _viewModel._logService.Warning("Hedef metin null", false, LogService.LogState.Translate);
                return;
            }

            if (_viewModel.GoogleSheetsPanelVm.TextData == null || !_viewModel.GoogleSheetsPanelVm.TextData.Any())
            {
                _viewModel._logService.Warning("İşlenebilir metin bulunamadı", false, LogService.LogState.Translate);
                return;
            }

            // Hedef metnin listede bulunduğu pozisyonu tespit et
            var targetIndex = FindTargetTextIndex(targetText);
            if (targetIndex == -1)
            {
                _viewModel._logService.Warning($"Hedef metin listede bulunamadı: {targetText.EN}", false, LogService.LogState.Translate);
                return;
            }

            // Önceki 5 metni al ve benzerlik kontrolü yap
            CollectSimilarTextsFromRange(targetText, targetIndex - 5, targetIndex - 1, result.RelevantTranslations_Pre);

            // Sonraki 5 metni al ve benzerlik kontrolü yap
            CollectSimilarTextsFromRange(targetText, targetIndex + 1, targetIndex + 5, result.RelevantTranslations_Post);
            _viewModel._logService.Info($"{result.RelevantTranslations_Pre.Count} önceki, {result.RelevantTranslations_Post.Count} sonraki benzer çeviri bulundu", false, LogService.LogState.Translate);
        }



        /// <summary>
        /// Aşama 2: AI ile çeviri stratejisi oluşturma işlemi
        /// </summary>
        private async Task<string?> GenerateTranslationStrategy(Text text, ExtractionResult extractionData)
        {
            await HandlePauseIfNeeded();
            _cancellationToken.ThrowIfCancellationRequested();

            _viewModel._logService.Info("Aşama 2: Çeviri stratejisi oluşturuluyor...", true, LogService.LogState.Translate);

            try
            {
                // 1. Sistem promptunu çeviri ayarlarından al
                var SystemPrompt = _viewModel.AppSettings.Translation.Prompt;
                SystemPrompt = SystemPrompt.Replace("[Ana Bağlam]", _viewModel.GoogleSheetsPanelVm.MainContext?.Icerik ?? "(YOK)");
                SystemPrompt = SystemPrompt.Replace("Not: [Opsiyonel Prompt]", "Not: " + _viewModel.OptionalPrompt ?? "");

                // 2. Terminoloji ve bağlam verileriyle user prompt oluştur
                var UserPrompt = BuildUserPrompt(text, extractionData);

                var Strategy = await _viewModel.GeminiService.GenerateContentWithStringAsync(
                    SystemPrompt,
                   UserPrompt,
                    _viewModel.AppSettings.Translation.AIModel,
                    LogService.LogState.Translate,
                    _cancellationToken
                );

                _viewModel._logService.Success("Aşama 2 tamamlandı: Çeviri stratejisi oluşturuldu", true, LogService.LogState.Translate);

                return Strategy;
            }
            catch (Exception ex)
            {
                _viewModel._logService.Error("Aşama 2'de hata oluştu", ex, true, LogService.LogState.Translate);
                return null;
            }
        }

        /// <summary>
        /// Tek metin için terminoloji ve bağlam verileriyle user prompt oluşturur
        /// </summary>
        private string BuildUserPrompt(Text text, ExtractionResult extractionData)
        {
            var promptBuilder = new System.Text.StringBuilder();

            // Çevrilecek metni ekle
            promptBuilder.AppendLine("=== ÇEVRİLECEK METİN ===");
            if (!string.IsNullOrWhiteSpace(text.EN))
            {
                promptBuilder.AppendLine($"ID: {text.ID.ToString()}");

                if (_viewModel.SelectedTextContextColumns.Any())
                {
                    foreach (var column in _viewModel.SelectedTextContextColumns)
                    {
                        var contextInfo = _viewModel.GetTextContextInfo(text, column);

                        if (!string.IsNullOrEmpty(contextInfo))
                        {
                            promptBuilder.AppendLine($"{column}: {contextInfo}");
                        }
                    }
                }
                promptBuilder.AppendLine($"Çevrilecek Metin:");
                promptBuilder.AppendLine(text.EN);
            }

            // Bulunan terimleri ekle
            if (extractionData.FoundTerms.Any())
            {
                promptBuilder.AppendLine("\n=== İLGİLİ TERİMLER ===");
                foreach (var term in extractionData.FoundTerms)
                {
                    promptBuilder.AppendLine($"**{term.EN} → {term.TR} ({term.Kategori})**\n - {term.Bilgi}\n");
                }
                promptBuilder.AppendLine();
            }

            // Benzer çevirileri ekle
            if (extractionData.SimilarTranslations.Any())
            {
                promptBuilder.AppendLine("\n=== BENZERİ ÇEVRİLMİŞ METİNLER ===");
                foreach (var similar in extractionData.SimilarTranslations)
                {
                    promptBuilder.AppendLine($"- {similar.EN} → {similar.TR}");
                }
                promptBuilder.AppendLine();
            }

            // İlgili bağlamları ekle
            if (extractionData.RelevantContexts.Any())
            {
                promptBuilder.AppendLine("\n=== İLGİLİ BAĞLAMLAR ===");
                foreach (var context in extractionData.RelevantContexts)
                {
                    promptBuilder.AppendLine($"- {context.Kategori}: {context.AltBaslik}");
                    if (!string.IsNullOrWhiteSpace(context.Icerik))
                    {
                        promptBuilder.AppendLine($"  {context.Icerik}");
                    }
                }
                promptBuilder.AppendLine();
            }


            // Metnin alt-üst bağlamı
            if (extractionData.RelevantTranslations_Pre.Any() || extractionData.RelevantTranslations_Post.Any())
            {
                promptBuilder.AppendLine("\n=== METNİN ALT-ÜST BAĞLAMI ===");
                if (extractionData.RelevantTranslations_Pre.Any())
                {
                    promptBuilder.AppendLine($"Önceki Metinler:");
                    foreach (var pre in extractionData.RelevantTranslations_Pre)
                    {
                        promptBuilder.AppendLine(pre.IsTranslated ? $"- {pre.EN} → {pre.TR}" : $"- {pre.EN}");
                    }
                    promptBuilder.AppendLine();
                }
                if (extractionData.RelevantTranslations_Post.Any())
                {
                    promptBuilder.AppendLine($"Sonraki Metinler:");
                    foreach (var post in extractionData.RelevantTranslations_Post)
                    {
                        promptBuilder.AppendLine(post.IsTranslated != null ? $"- {post.EN} → {post.TR}" : $"- {post.EN}");
                    }
                    promptBuilder.AppendLine();
                }

            }

            return promptBuilder.ToString();
        }


        private async Task<int?> CalculateTranslationDifficulty(Text text)
        {
            await HandlePauseIfNeeded();
            _cancellationToken.ThrowIfCancellationRequested();

            _viewModel._logService.Info("Aşama 3: Çeviri zorluğu hesaplanıyor...", true, LogService.LogState.Translate);

            try
            {


                // Analiz edilecek metni al
                var textToAnalyze = text.EN;

                if (string.IsNullOrWhiteSpace(textToAnalyze))
                {
                    _viewModel._logService.Warning("Analiz edilecek metin boş", true, LogService.LogState.Translate);
                    return 0;
                }

                // TextStatistics kütüphanesi ile analiz yap
                var textStats = TextStatistics.TextStatistics.Parse(textToAnalyze);


                // Zorluk seviyesini hesapla (1-5 arası)
                var DifficultyLevel = CalculateDifficultyLevel(textStats.FleschKincaidReadingEase());


                _viewModel._logService.Success($"Aşama 3 tamamlandı: Zorluk seviyesi {DifficultyLevel}/5", true, LogService.LogState.Translate);


                return DifficultyLevel;
            }
            catch (Exception ex)
            {
                _viewModel._logService.Error("Aşama 3'te hata oluştu", ex, true, LogService.LogState.Translate);
                return null;
            }
        }

        /// <summary>
        /// Flesch Reading Ease skoruna göre zorluk seviyesini hesaplar (1-5 arası)
        /// </summary>
        private int CalculateDifficultyLevel(double fleschScore)
        {
            return fleschScore switch
            {
                >= 90 => 1, // Çok kolay
                >= 80 => 2, // Kolay
                >= 70 => 3, // Orta
                >= 60 => 4, // Zor
                _ => 5      // Çok zor
            };
        }

        /// <summary>
        /// Aşama 4 sonuç veri yapısı
        /// </summary>
        public class TranslationResult
        {
            public string OriginalText { get; set; } = string.Empty;
            public string TranslatedText { get; set; } = string.Empty;
            public string SelectedModel { get; set; } = string.Empty;
            public int DifficultyLevel { get; set; }
            public bool IsSuccessful { get; set; }
            public string ErrorMessage { get; set; } = string.Empty;
            public Dictionary<string, object> Metadata { get; set; } = new();
        }

        /// <summary>
        /// Aşama 4: Hesaplanan zorluk seviyesine göre uygun AI model seçimi yaparak hazırlanan strateji ile nihai çevirileri alma işlemi
        /// </summary>
        private async Task<bool?> PerformFinalTranslation(Text text, string? strategyData, int? difficultyData)
        {
            await HandlePauseIfNeeded();
            _cancellationToken.ThrowIfCancellationRequested();

            _viewModel._logService.Info("Aşama 4: Nihai çeviri gerçekleştiriliyor...", true, LogService.LogState.Translate);

            try
            {

                // 1. Zorluk seviyesine göre AI model seçimi
                var selectedModel = SelectAIModelBasedOnDifficulty(difficultyData ?? 3);


                _viewModel._logService.Info($"Seçilen AI Model: {selectedModel} (Zorluk: {difficultyData ?? 3}/5)", true, LogService.LogState.Translate);

                // 2. Çeviri prompt'unu hazırla
                var finalPrompt = BuildFinalTranslationPrompt(text, strategyData);

                // 3. AI servisini çağır ve çeviri al
                var result = await CallAIServiceAndParseForTranslation(finalPrompt, selectedModel, text);
                if (!result)
                {
                    _viewModel._logService.Error("AI yanıtı parse edilirken hata oluştu", null, true, LogService.LogState.Translate);
                    return false;
                }


                // 4. Google Sheets'e güncelle
                await UpdateGoogleSheetsWithTranslation(text);



                _viewModel._logService.Success($"Aşama 4 tamamlandı.", true, LogService.LogState.Translate);

                return true;
            }
            catch (Exception ex)
            {
                _viewModel._logService.Error("Aşama 4'te hata oluştu", ex, true, LogService.LogState.Translate);
                return false;
            }
        }

        /// <summary>
        /// Zorluk seviyesine göre uygun AI model seçimi yapar
        /// </summary>
        private AIModel SelectAIModelBasedOnDifficulty(int difficultyLevel)
        {
            return (_viewModel.AppSettings.Translation.Quality, difficultyLevel) switch
            {
                // Free Quality
                (TranslationQuality.Free, 1 or 2) => AIModel.GeminiFlash2_0, // Kolay metinler için hızlı model
                (TranslationQuality.Free, 3) => AIModel.GeminiFlash2_0, // Orta zorluk için varsayılan model
                (TranslationQuality.Free, 4 or 5) => AIModel.GeminiFlash2_5, // Zor metinler için güçlü model
                (TranslationQuality.Free, _) => AIModel.GeminiFlash2_5,

                // Eco Quality
                (TranslationQuality.Eco, 1 or 2) => AIModel.GeminiFlash2_5, // Kolay metinler için hızlı model
                (TranslationQuality.Eco, 3) => AIModel.GeminiFlash2_5, // Orta zorluk için varsayılan model
                (TranslationQuality.Eco, 4 or 5) => AIModel.Claude4Sonnet, // Zor metinler için güçlü model
                (TranslationQuality.Eco, _) => AIModel.Claude4Sonnet,

                // Full Quality
                (TranslationQuality.Full, _) => AIModel.Claude4Sonnet,

                _ => AIModel.Claude4Sonnet
            };
        }

        /// <summary>
        /// Nihai çeviri için prompt hazırlar
        /// </summary>
        private string BuildFinalTranslationPrompt(Text text, string? strategyData)
        {
            var promptFinal = _viewModel.AppSettings.Translation.Prompt2;
            promptFinal = promptFinal.Replace("[Ana Bağlam]", _viewModel.GoogleSheetsPanelVm.MainContext?.Icerik ?? "(YOK)");

            if (text.HasNote)
            {
                promptFinal = promptFinal.Replace("Not: [Satır Notu]", "Not: " + text.Note);
            }

            var texttoTranslate = new System.Text.StringBuilder();

            // Çevrilecek metni ekle
            texttoTranslate.AppendLine($"ID: {text.ID.ToString()}");
            if (_viewModel.SelectedTextContextColumns.Any())
            {
                foreach (var column in _viewModel.SelectedTextContextColumns)
                {
                    var contextInfo = _viewModel.GetTextContextInfo(text, column);

                    if (!string.IsNullOrEmpty(contextInfo))
                    {
                        texttoTranslate.AppendLine($"{column}: {contextInfo}");
                    }
                }
            }
            texttoTranslate.AppendLine($"Çevrilecek Metin:");
            texttoTranslate.AppendLine(text.EN);

            promptFinal = promptFinal.Replace("[Çevrilecek Metin]", texttoTranslate.ToString());
            promptFinal = promptFinal.Replace("[Rapor]", strategyData ?? "");

            return promptFinal;
        }

        /// <summary>
        /// AI servisini çağırarak çeviri alır
        /// </summary>
        private async Task<bool> CallAIServiceAndParseForTranslation(string prompt, AIModel model, Text text)
        {
            try
            {


                _viewModel._logService.Info($"AI ile çeviri yapılıyor...", false, LogService.LogState.Translate);

                JsonElement? response;


                if (model == AIModel.Claude4Sonnet || model == AIModel.Claude37Sonnet || model == AIModel.Claude35Sonnet)
                {
                    response = await _viewModel.AnthropicService.GenerateContentWithJsonToolsAsync(
                        _viewModel.AppSettings.Translation.Prompt2,
                        prompt,
                        _viewModel.AppSettings.Translation.Function,
                        model,
                        LogService.LogState.Translate,
                        _cancellationToken
                    );
                }
                else
                {

                    response = await _viewModel.GeminiService.GenerateContentWithJsonToolsAsync(
                        _viewModel.AppSettings.Translation.Prompt2,
                        prompt,
                        _viewModel.AppSettings.Translation.Function,
                        model,
                        LogService.LogState.Translate,
                        _cancellationToken
                    );
                }
                var resVal = response.Value;
                var id = resVal.GetProperty("id");
                if (id.ValueKind == JsonValueKind.Number)
                {
                    if (id.GetInt32() != text.ID)
                    {
                        _viewModel._logService.Error("AI yanıtı beklenen formatta değil (ID yanlış)", null, true, LogService.LogState.Translate);
                        return false;
                    }
                }
                else
                {
                    _viewModel._logService.Error("AI yanıtı beklenen formatta değil (ID bulunamadı)", null, true, LogService.LogState.Translate);
                    return false;
                }

                var TR = resVal.GetProperty("tr");
                if (TR.ValueKind == JsonValueKind.String)
                {
                    text.TR = TR.GetString();
                    text.Status = TranslationStatus.TR;

                }
                else
                {
                    _viewModel._logService.Error("AI yanıtı beklenen formatta değil (TR bulunamadı)", null, true, LogService.LogState.Translate);
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                _viewModel._logService.Error($"AI servis çağrısında hata: {ex.Message}", ex, false, LogService.LogState.Translate);
                return false;
            }
        }

        /// <summary>
        /// Google Sheets'e çeviri sonucunu günceller
        /// </summary>
        private async Task UpdateGoogleSheetsWithTranslation(Text text)
        {
            try
            {
                var selectedSheet = _viewModel.GoogleSheetsPanelVm.SelectedTextSheet;
                var spreadsheetId = _viewModel.GoogleSheetsPanelVm.SpreadsheetId;

                if (selectedSheet == null || string.IsNullOrWhiteSpace(spreadsheetId))
                {
                    _viewModel._logService.Warning("Google Sheets bilgileri eksik, çeviri güncellenemedi.", false, LogService.LogState.Translate);
                    return;
                }

                _viewModel._logService.Info($"Çeviri Google Sheets'e güncelleniyor: {text.Key ?? text.ID.ToString()}", false, LogService.LogState.Translate);

                // Google Sheets'ten mevcut verileri al
                var existingData = await _viewModel.GoogleSheetsService.GetDataAsync(spreadsheetId, selectedSheet.SheetName);

                if (existingData != null && existingData.Count > 1) // Başlık satırı + en az 1 veri satırı
                {
                    // Text nesnesinin row number'ı ile eşleşen satırı bul
                    var rowIndex = existingData
                        .Select((r, idx) => new { r, idx })
                        .Skip(1) // başlık satırını atla
                        .FirstOrDefault(x => x.r.Count > 0 &&
                                             int.TryParse(x.r[0]?.ToString(), out int rid) &&
                                             rid == text.ID)?.idx;

                    if (rowIndex.HasValue)
                    {
                        // Çeviri tamamlandığında status'u TR olarak güncelle
                        text.Status = TranslationStatus.TR;

                        // Google Sheets'teki sütun sırasına göre veriyi hazırla
                        // Sıra: #, EN, TR, EN-TR, NAMESPACE (opsiyonel), KEY (opsiyonel)
                        var updatedRowData = new List<object>
                        {
                            text.ID,     // # sütunu
                        };
                        // Namespace sütunu varsa ekle
                        if (text.HasNamespace)
                        {
                            updatedRowData.Add(text.Namespace ?? "");
                        }
                        // Key sütunu varsa ekle
                        if (text.HasKey)
                        {
                            updatedRowData.Add(text.Key ?? "");
                        }
                        updatedRowData.Add(text.EN ?? "");      // EN sütunu
                        updatedRowData.Add(text.TR ?? "");      // TR sütunu (çevrilmiş metin)
                        updatedRowData.Add(text.Status.ToString()); // EN-TR sütunu (status)
                        updatedRowData.Add(text.Note ?? ""); // NOT sütunu (not)


                        // Satırı Google Sheets'te güncelle
                        await _viewModel.GoogleSheetsService.UpdateRowAsync(spreadsheetId, selectedSheet.SheetName, rowIndex.Value, updatedRowData);
                        _viewModel._logService.Success($"Çeviri Google Sheets'te başarıyla güncellendi: {text.Key ?? text.ID.ToString()}", true, LogService.LogState.Translate);
                    }
                    else
                    {
                        _viewModel._logService.Warning($"Güncellenecek metin Google Sheets'te bulunamadı (Row: {text.ID}): {text.EN}", false, LogService.LogState.Translate);
                    }
                }
                else
                {
                    _viewModel._logService.Warning("Google Sheets'te veri bulunamadı.", false, LogService.LogState.Translate);
                }
            }
            catch (Exception ex)
            {
                _viewModel._logService.Warning($"Google Sheets güncellemesinde hata: {ex.Message}", false, LogService.LogState.Translate);
                // Güncelleme hatası çeviri işlemini durdurmaz
            }
        }

        #endregion

        private async Task HandlePauseIfNeeded()
        {
            if (_viewModel.IsPaused && _viewModel.IsProcessing)
            {
                _viewModel.IsOperationInProgress = false;
                _viewModel.NotifyRelatedCommands();
                _viewModel._logService.Warning("Çeviri işlemi duraklatıldı - Devam etmek için 'Devam Et' butonuna basın.", true, LogService.LogState.Translate);

                try
                {
                    while (_viewModel.IsPaused && _viewModel.IsProcessing)
                    {
                        await Task.Delay(500, _cancellationToken);
                    }

                    if (!_viewModel.IsPaused && _viewModel.IsProcessing)
                    {
                        _viewModel._logService.Success("Çeviri işlemi devam ediyor.", true, LogService.LogState.Translate);
                    }
                }
                catch (OperationCanceledException)
                {
                    // İptal edildi, normal durum
                    _viewModel._logService.Warning("Çeviri işlemi iptal edildi.", true, LogService.LogState.Translate);
                    throw;
                }
            }
        }

        /// <summary>
        /// Verilen metin için eşleşen terimleri bulur
        /// </summary>
        /// <param name="text">Analiz edilecek metin</param>
        /// <returns>Eşleşen terimler listesi</returns>
        private List<TermViewModel> FindMatchingTerms(Text text)
        {
            if (string.IsNullOrWhiteSpace(text.Lemma))
                return [];

            return _viewModel.GoogleSheetsPanelVm.TerminologyData
                .Where(IsValidTerm)
                .Where(term => IsTermMatching(term, text))
                .Distinct()
                .ToList();
        }

        /// <summary>
        /// Terimin geçerli olup olmadığını kontrol eder
        /// </summary>
        /// <param name="term">Kontrol edilecek terim</param>
        /// <returns>Terim geçerliyse true</returns>
        private static bool IsValidTerm(TermViewModel term)
        {
            return term.Status == TermStatus.Çevrildi;
        }

        /// <summary>
        /// Terimin metinle eşleşip eşleşmediğini kontrol eder
        /// </summary>
        /// <param name="term">Kontrol edilecek terim</param>
        /// <param name="text">Karşılaştırılacak metin</param>
        /// <returns>Eşleşiyorsa true</returns>
        private static bool IsTermMatching(TermViewModel term, Text text)
        {
            // Aynı metinleri hariç tut
            if (term.EN.Equals(text.EN, StringComparison.OrdinalIgnoreCase))
                return false;

            // Null kontrolü
            if (string.IsNullOrWhiteSpace(term.Lemma) || string.IsNullOrWhiteSpace(text.Lemma))
                return false;

            // Lemma eşleşmesini kontrol et (kelime sınırları ile)
            var pattern = $@"\b{Regex.Escape(term.Lemma)}\b";
            return new Regex(pattern, RegexOptions.IgnoreCase).IsMatch(text.Lemma);
        }


        private bool IsTextSimilarWithRegex(string baseTerm, string candidate)
        {
            if (string.IsNullOrWhiteSpace(baseTerm) || string.IsNullOrWhiteSpace(candidate))
                return false;
            if (IsRegexMatchWithInflections(baseTerm, candidate))
                return true;
            if (IsRegexMatchWithInflections(candidate, baseTerm))
                return true;
            if (baseTerm.Equals(candidate, StringComparison.OrdinalIgnoreCase))
                return true;
            return false;

            bool IsRegexMatchWithInflections(string term, string text)
            {
                string pattern = $@"\b{Regex.Escape(term)}\b";
                return Regex.IsMatch(text, pattern, RegexOptions.IgnoreCase);
            }
        }

        #region CollectRelevantTranslations Yardımcı Fonksiyonları

        /// <summary>
        /// Hedef metnin tüm metinler listesindeki pozisyonunu bulur
        /// </summary>
        /// <param name="targetText">Hedef metin</param>
        /// <param name="allTexts">Tüm metinler listesi</param>
        /// <returns>Pozisyon indeksi, bulunamazsa -1</returns>
        private int FindTargetTextIndex(Text targetText)
        {
            var indexByKey = _viewModel.GoogleSheetsPanelVm.TextData.ToList().FindIndex(t => t.ID == targetText.ID);
            if (indexByKey != -1)
                return indexByKey;

            // ID yoksa EN metni ile eşleştir
            return _viewModel.GoogleSheetsPanelVm.TextData.ToList().FindIndex(t => t.EN.Equals(targetText.EN, StringComparison.OrdinalIgnoreCase));

        }

        /// <summary>
        /// Belirtilen aralıktaki metinlerden benzer olanları toplar
        /// </summary>
        /// <param name="targetText">Hedef metin</param>
        /// <param name="allTexts">Tüm metinler listesi</param>
        /// <param name="startIndex">Başlangıç indeksi</param>
        /// <param name="endIndex">Bitiş indeksi</param>
        /// <param name="resultList">Sonuçların ekleneceği liste</param>
        private void CollectSimilarTextsFromRange(Text targetText, int startIndex, int endIndex, List<Text> resultList)
        {
            // Geçerli aralığı hesapla
            var validStartIndex = Math.Max(0, startIndex);
            var validEndIndex = Math.Min(_viewModel.GoogleSheetsPanelVm.TextData.Count - 1, endIndex);

            if (validStartIndex > validEndIndex)
                return;

            // Benzerlik eşik değeri (%70)
            const double similarityThreshold = 0.70;

            for (int i = validStartIndex; i <= validEndIndex; i++)
            {
                var candidateText = _viewModel.GoogleSheetsPanelVm.TextData[i];


                // Key benzerliği hesapla
                var similarity = CalculateKeySimilarity(targetText.Key, candidateText.Key);

                // Benzerlik eşiğini geçiyorsa listeye ekle
                if (similarity >= similarityThreshold)
                {
                    // candidateText EN metni 500 karakterden uzunsa kısalt
                    var textToAdd = candidateText;
                    if (candidateText.EN?.Length > 500)
                    {
                        textToAdd = new Text
                        {
                            ID = candidateText.ID,
                            EN = candidateText.EN.Substring(0, 497) + "...",
                            TR = candidateText.TR,
                            Status = candidateText.Status,
                            Namespace = candidateText.Namespace,
                            Key = candidateText.Key,
                            Lemma = candidateText.Lemma,
                            Vector = candidateText.Vector
                        };
                    }
                    resultList.Add(textToAdd);
                }
            }
        }

        /// <summary>
        /// İki key değeri arasındaki benzerlik oranını hesaplar (Levenshtein distance kullanarak)
        /// </summary>
        /// <param name="key1">İlk key değeri</param>
        /// <param name="key2">İkinci key değeri</param>
        /// <returns>Benzerlik oranı (0.0 - 1.0 arası)</returns>
        private double CalculateKeySimilarity(string? key1, string? key2)
        {
            // Null kontrolü
            if (string.IsNullOrWhiteSpace(key1) || string.IsNullOrWhiteSpace(key2))
                return 0.0;

            // Aynı string'ler
            if (key1.Equals(key2, StringComparison.OrdinalIgnoreCase))
                return 1.0;

            // Levenshtein distance hesapla
            var distance = CalculateLevenshteinDistance(key1.ToLowerInvariant(), key2.ToLowerInvariant());
            var maxLength = Math.Max(key1.Length, key2.Length);

            // Benzerlik oranını hesapla (1 - (distance / maxLength))
            return maxLength == 0 ? 0.0 : 1.0 - ((double)distance / maxLength);
        }

        /// <summary>
        /// İki string arasındaki Levenshtein distance'ı hesaplar
        /// </summary>
        /// <param name="source">Kaynak string</param>
        /// <param name="target">Hedef string</param>
        /// <returns>Levenshtein distance değeri</returns>
        private int CalculateLevenshteinDistance(string source, string target)
        {
            if (string.IsNullOrEmpty(source))
                return string.IsNullOrEmpty(target) ? 0 : target.Length;

            if (string.IsNullOrEmpty(target))
                return source.Length;

            var sourceLength = source.Length;
            var targetLength = target.Length;
            var matrix = new int[sourceLength + 1, targetLength + 1];

            // İlk satır ve sütunu başlat
            for (int i = 0; i <= sourceLength; i++)
                matrix[i, 0] = i;

            for (int j = 0; j <= targetLength; j++)
                matrix[0, j] = j;

            // Matrix'i doldur
            for (int i = 1; i <= sourceLength; i++)
            {
                for (int j = 1; j <= targetLength; j++)
                {
                    var cost = source[i - 1] == target[j - 1] ? 0 : 1;

                    matrix[i, j] = Math.Min(
                        Math.Min(matrix[i - 1, j] + 1, matrix[i, j - 1] + 1),
                        matrix[i - 1, j - 1] + cost);
                }
            }

            return matrix[sourceLength, targetLength];
        }

        #endregion

    }
}

