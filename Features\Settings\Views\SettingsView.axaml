<UserControl
  x:Class="TranslationAgent.Features.Settings.Views.SettingsView"
  xmlns="https://github.com/avaloniaui"
  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
  xmlns:converters="clr-namespace:TranslationAgent.Converters"
  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
  xmlns:vm="clr-namespace:TranslationAgent.Features.Settings.ViewModels"
  Name="SettingsViewControl"
  d:DesignHeight="450"
  d:DesignWidth="800"
  x:DataType="vm:SettingsViewModel"
  mc:Ignorable="d">

  <UserControl.Resources>
    <converters:AIModelConverter x:Key="AIModelConverter" />
  </UserControl.Resources>

  <Border
    Background="Transparent"
    BorderBrush="Gray"
    BorderThickness="1">
    <Grid>
      <TabControl VerticalAlignment="Stretch">
        <!--  <PERSON><PERSON>  -->
        <TabItem Header="Genel Ayarlar">
          <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="0,10,20,0" Spacing="20">
              <!--  Google Service Account Section  -->
              <Border
                Padding="10"
                BorderBrush="{DynamicResource SystemControlHighlightBaseMediumBrush}"
                BorderThickness="1"
                CornerRadius="4">
                <StackPanel Spacing="10">
                  <TextBlock
                    FontSize="16"
                    FontWeight="Bold"
                    Text="Google Service Account Credentials" />
                  <TextBlock Text="Paste the content of your service account JSON file below:" TextWrapping="Wrap" />
                  <TextBox
                    Height="200"
                    MaxHeight="300"
                    HorizontalContentAlignment="Stretch"
                    VerticalContentAlignment="Stretch"
                    AcceptsReturn="True"
                    Text="{Binding ServiceAccountJson, Mode=TwoWay}"
                    TextWrapping="Wrap"
                    Watermark="Service Account JSON" />
                </StackPanel>
              </Border>



              <!--  Sütun Başlıkları Section  -->
              <Border
                Padding="10"
                BorderBrush="{DynamicResource SystemControlHighlightBaseMediumBrush}"
                BorderThickness="1"
                CornerRadius="4">
                <StackPanel Spacing="5">
                  <TextBlock
                    FontSize="16"
                    FontWeight="Bold"
                    Text="Sütun Başlıkları" />
                  <TextBlock Text="Excel veya Google Sheets'te kaynak metin ve metin durumu sütunlarının başlıklarını belirtin:" TextWrapping="Wrap" />
                  <Grid
                    Margin="0,5"
                    ColumnDefinitions="200,20,200"
                    RowDefinitions="Auto,Auto">
                    <StackPanel
                      Grid.Row="0"
                      Grid.Column="0"
                      Spacing="5">
                      <TextBlock FontWeight="SemiBold" Text="Kaynak Metin Sütunu:" />
                      <TextBox
                        HorizontalAlignment="Stretch"
                        Text="{Binding SourceTextColumnHeader, Mode=TwoWay}"
                        Watermark="Örn: EN" />
                    </StackPanel>
                    <StackPanel
                      Grid.Row="0"
                      Grid.Column="2"
                      Spacing="5">
                      <TextBlock FontWeight="SemiBold" Text="Metin Durumu Sütunu:" />
                      <TextBox
                        HorizontalAlignment="Stretch"
                        Text="{Binding TextStatusColumnHeader, Mode=TwoWay}"
                        Watermark="Örn: EN-TR" />
                    </StackPanel>
                  </Grid>
                </StackPanel>
              </Border>

              <!--  Hard Log Section  -->
              <Border
                Padding="10"
                BorderBrush="{DynamicResource SystemControlHighlightBaseMediumBrush}"
                BorderThickness="1"
                CornerRadius="4">
                <StackPanel Spacing="5">
                  <TextBlock
                    FontSize="16"
                    FontWeight="Bold"
                    Text="Hard Log" />
                  <TextBlock Text="Detaylı log kayıtlarını etkinleştir veya devre dışı bırak:" TextWrapping="Wrap" />
                  <ToggleSwitch
                    Margin="0,5"
                    IsChecked="{Binding HardLog, Mode=TwoWay}"
                    OffContent="Kapalı"
                    OnContent="Açık" />
                </StackPanel>
              </Border>

              <!--  Claude API Key Section  -->
              <Border
                Padding="10"
                BorderBrush="{DynamicResource SystemControlHighlightBaseMediumBrush}"
                BorderThickness="1"
                CornerRadius="4">
                <StackPanel Spacing="10">
                  <TextBlock
                    FontSize="16"
                    FontWeight="Bold"
                    Text="Anthropic API Key" />
                  <TextBlock Text="Claude AI servisini kullanmak için API anahtarınızı girin:" TextWrapping="Wrap" />
                  <TextBox
                    HorizontalContentAlignment="Stretch"
                    Text="{Binding ClaudeApiKey, Mode=TwoWay}"
                    Watermark="Claude API Key" />
                </StackPanel>
              </Border>
              <!--  Gemini API Keys Section  -->
              <StackPanel Spacing="5">
                <TextBlock
                  FontSize="16"
                  FontWeight="Bold"
                  Text="Gemini API Keys" />

                <!--  Add New Key Section  -->
                <Border
                  Padding="10"
                  BorderBrush="{DynamicResource SystemControlHighlightBaseMediumBrush}"
                  BorderThickness="1"
                  CornerRadius="4">
                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="Bold" Text="Yeni API Anahtarı Ekle" />
                    <Grid
                      Margin="0,5"
                      ColumnDefinitions="3*,1*,1*"
                      RowDefinitions="Auto,Auto">
                      <TextBox
                        Grid.Row="0"
                        Grid.RowSpan="2"
                        Grid.Column="0"
                        MinWidth="250"
                        Margin="0,0,5,0"
                        VerticalAlignment="Stretch"
                        Text="{Binding NewGeminiKey, Mode=TwoWay}"
                        Watermark="API Anahtarını buraya girin" />
                      <StackPanel
                        Grid.Row="0"
                        Grid.Column="1"
                        Margin="10,0,0,0"
                        Spacing="5">
                        <TextBlock FontWeight="SemiBold" Text="Flash 2.0 Günlük Kota:" />
                        <NumericUpDown
                          Width="120"
                          HorizontalAlignment="Left"
                          FormatString="0"
                          Minimum="1"
                          Value="{Binding NewKeyQuotaFlash2_0, Mode=TwoWay}" />
                      </StackPanel>
                      <StackPanel
                        Grid.Row="0"
                        Grid.Column="2"
                        Spacing="5">
                        <TextBlock FontWeight="SemiBold" Text="Flash 2.5 Günlük Kota:" />
                        <NumericUpDown
                          Width="120"
                          HorizontalAlignment="Left"
                          FormatString="0"
                          Minimum="1"
                          Value="{Binding NewKeyQuotaFlash2_5, Mode=TwoWay}" />
                      </StackPanel>
                    </Grid>
                    <Button
                      Width="180"
                      Margin="0,5,0,0"
                      HorizontalAlignment="Left"
                      Command="{Binding AddGeminiKeyCommand}"
                      Content="API Anahtarı Ekle" />
                  </StackPanel>
                </Border>

                <!--  API Keys List  -->
                <TextBlock
                  Margin="0,10,0,5"
                  FontWeight="Bold"
                  Text="Mevcut API Anahtarları" />
                <DataGrid
                  Name="GeminiKeysGrid"
                  MaxHeight="400"
                  HorizontalAlignment="Stretch"
                  VerticalAlignment="Stretch"
                  AutoGenerateColumns="False"
                  Background="{DynamicResource SystemControlBackgroundAltHighBrush}"
                  BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}"
                  BorderThickness="1"
                  CanUserReorderColumns="True"
                  CanUserResizeColumns="True"
                  GridLinesVisibility="All"
                  HorizontalGridLinesBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}"
                  IsReadOnly="True"
                  ItemsSource="{Binding GeminiKeys}"
                  SelectedItem="{Binding SelectedApiKey}"
                  SelectionMode="Single"
                  VerticalGridLinesBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}">
                  <DataGrid.Columns>
                    <DataGridTextColumn
                      Width="2*"
                      Binding="{Binding Key}"
                      Header="API Anahtarı" />
                    <DataGridTextColumn
                      Width="*"
                      Binding="{Binding QuotaStatusFlash2_0}"
                      Header="Flash 2.0 Kota Durumu" />
                    <DataGridTextColumn
                      Width="*"
                      Binding="{Binding QuotaStatusFlash2_5}"
                      Header="Flash 2.5 Kota Durumu" />
                    <DataGridTextColumn
                      Width="*"
                      Binding="{Binding LastResetDate, StringFormat={}{0:dd/MM/yyyy}}"
                      Header="Son Sıfırlama" />
                    <DataGridTemplateColumn Width="auto" Header="İşlemler">
                      <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                          <Button
                            Margin="5"
                            Command="{Binding #SettingsViewControl.DataContext.RemoveGeminiKeyCommand}"
                            CommandParameter="{Binding}"
                            Content="Sil" />
                        </DataTemplate>
                      </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                  </DataGrid.Columns>
                </DataGrid>
                <!--  Toplam Satırı  -->
                <Grid
                  Name="TotalRow"
                  Margin="0,5,0,0"
                  Background="{DynamicResource SystemControlBackgroundAltHighBrush}"
                  ColumnDefinitions="2*,*,*,*,auto">
                  <Border
                    Grid.ColumnSpan="5"
                    BorderBrush="{DynamicResource SystemControlForegroundBaseMediumBrush}"
                    BorderThickness="1" />
                  <TextBlock
                    Grid.Column="0"
                    Margin="10,5"
                    HorizontalAlignment="Left"
                    VerticalAlignment="Center"
                    FontWeight="Bold"
                    Text="TOPLAM" />
                  <TextBlock
                    Grid.Column="1"
                    Margin="5"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontWeight="Bold"
                    Text="{Binding TotalQuotaStatusFlash2_0}" />
                  <TextBlock
                    Grid.Column="2"
                    Margin="5"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontWeight="Bold"
                    Text="{Binding TotalQuotaStatusFlash2_5}" />
                  <TextBlock
                    Grid.Column="3"
                    Margin="5"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontWeight="Bold"
                    Text="-" />
                  <TextBlock
                    Grid.Column="4"
                    Width="75"
                    Margin="5"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontWeight="Bold"
                    Text="" />
                </Grid>
              </StackPanel>
            </StackPanel>
          </ScrollViewer>
        </TabItem>
        <!--  Çeviri Ayarları  -->
        <TabItem Header="Çeviri Ayarları">
          <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="0,10,20,0" Spacing="20">
              <!--  Çeviri Ayarları  -->
              <Border
                Padding="10"
                BorderBrush="{DynamicResource SystemControlHighlightBaseMediumBrush}"
                BorderThickness="1"
                CornerRadius="4">
                <StackPanel Spacing="10">
                  <TextBlock
                    FontSize="16"
                    FontWeight="Bold"
                    Text="Çeviri Ayarları" />
                  <TextBlock Text="Bu ayarlar metin çevirisi işlemleri için kullanılır." TextWrapping="Wrap" />

                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Strateji AI Modeli:" />
                    <ComboBox
                      Width="200"
                      HorizontalAlignment="Left"
                      ItemsSource="{Binding TranslationSettings.AvailableAIModels}"
                      SelectedItem="{Binding TranslationSettings.AIModel}">
                      <ComboBox.ItemTemplate>
                        <DataTemplate>
                          <TextBlock Text="{Binding Converter={StaticResource AIModelConverter}}" />
                        </DataTemplate>
                      </ComboBox.ItemTemplate>
                    </ComboBox>
                  </StackPanel>

                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Çeviri Kalitesi:" />
                    <ComboBox
                      Width="200"
                      HorizontalAlignment="Left"
                      ItemsSource="{Binding TranslationSettings.AvailableQualities}"
                      SelectedItem="{Binding TranslationSettings.Quality}" />
                  </StackPanel>

                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Gönderim Adeti:" />
                    <NumericUpDown
                      Width="120"
                      HorizontalAlignment="Left"
                      FormatString="0"
                      Maximum="100"
                      Minimum="1"
                      Value="{Binding TranslationSettings.BatchSize}" />
                  </StackPanel>

                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Prompt:" />
                    <TextBox
                      MinHeight="80"
                      MaxHeight="300"
                      HorizontalContentAlignment="Stretch"
                      VerticalContentAlignment="Stretch"
                      AcceptsReturn="True"
                      Classes="ResizableTextBox"
                      IsEnabled="True"
                      Text="{Binding TranslationSettings.Prompt, Mode=TwoWay}"
                      TextWrapping="Wrap"
                      Watermark="Örn: Lütfen aşağıdaki metni Türkçe'ye çevirin..." />
                  </StackPanel>

                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Prompt 2:" />
                    <TextBox
                      MinHeight="80"
                      MaxHeight="300"
                      HorizontalContentAlignment="Stretch"
                      VerticalContentAlignment="Stretch"
                      AcceptsReturn="True"
                      Classes="ResizableTextBox"
                      IsEnabled="True"
                      Text="{Binding TranslationSettings.Prompt2, Mode=TwoWay}"
                      TextWrapping="Wrap"
                      Watermark="Örn: Lütfen çeviriyi gözden geçirin ve iyileştirin..." />
                  </StackPanel>

                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Function:" />
                    <TextBox
                      MinHeight="80"
                      MaxHeight="300"
                      HorizontalContentAlignment="Stretch"
                      VerticalContentAlignment="Stretch"
                      AcceptsReturn="True"
                      Classes="ResizableTextBox"
                      IsEnabled="True"
                      Text="{Binding TranslationSettings.Function, Mode=TwoWay}"
                      TextWrapping="Wrap"
                      Watermark="Örn: translate_text" />
                  </StackPanel>
                </StackPanel>
              </Border>
            </StackPanel>
          </ScrollViewer>
        </TabItem>

        <!--  Terminoloji Ayarları  -->
        <TabItem Header="Terim Ayarları">
          <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="0,10,20,0" Spacing="20">
              <!--  Aşama 1  -->
              <Border
                Padding="10"
                BorderBrush="{DynamicResource SystemControlHighlightBaseMediumBrush}"
                BorderThickness="1"
                CornerRadius="4">
                <StackPanel Spacing="10">
                  <TextBlock
                    FontSize="16"
                    FontWeight="Bold"
                    Text="Aşama 1 - Terim Tespiti" />
                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="AI Modeli:" />
                    <ComboBox
                      Width="200"
                      HorizontalAlignment="Left"
                      ItemsSource="{Binding TerminologySettings.AvailableAIModels}"
                      SelectedItem="{Binding TerminologySettings.Stage1AIModel}">
                      <ComboBox.ItemTemplate>
                        <DataTemplate>
                          <TextBlock Text="{Binding Converter={StaticResource AIModelConverter}}" />
                        </DataTemplate>
                      </ComboBox.ItemTemplate>
                    </ComboBox>
                  </StackPanel>

                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Gönderim Adeti:" />
                    <NumericUpDown
                      Width="120"
                      HorizontalAlignment="Left"
                      FormatString="0"
                      Maximum="100"
                      Minimum="1"
                      Value="{Binding TerminologySettings.Stage1BatchSize}" />
                  </StackPanel>
                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Prompt:" />
                    <TextBox
                      MinHeight="80"
                      MaxHeight="300"
                      HorizontalContentAlignment="Stretch"
                      VerticalContentAlignment="Stretch"
                      AcceptsReturn="True"
                      Classes="ResizableTextBox"
                      IsEnabled="True"
                      Text="{Binding TerminologySettings.Stage1Prompt, Mode=TwoWay}"
                      TextWrapping="Wrap"
                      Watermark="Örn: Lütfen aşağıdaki metni Türkçe'ye çevirin..." />
                  </StackPanel>

                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Function:" />
                    <TextBox
                      MinHeight="80"
                      MaxHeight="300"
                      HorizontalContentAlignment="Stretch"
                      VerticalContentAlignment="Stretch"
                      AcceptsReturn="True"
                      Classes="ResizableTextBox"
                      IsEnabled="True"
                      Text="{Binding TerminologySettings.Stage1Function, Mode=TwoWay}"
                      TextWrapping="Wrap"
                      Watermark="Örn: translate_to_turkish" />
                  </StackPanel>



                </StackPanel>
              </Border>

              <!--  Aşama 2  -->
              <Border
                Padding="10"
                BorderBrush="{DynamicResource SystemControlHighlightBaseMediumBrush}"
                BorderThickness="1"
                CornerRadius="4">
                <StackPanel Spacing="10">
                  <TextBlock
                    FontSize="16"
                    FontWeight="Bold"
                    Text="Aşama 2 - Terim Kontrolü" />
                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="AI Modeli:" />
                    <ComboBox
                      Width="200"
                      HorizontalAlignment="Left"
                      ItemsSource="{Binding TerminologySettings.AvailableAIModels}"
                      SelectedItem="{Binding TerminologySettings.Stage2AIModel}">
                      <ComboBox.ItemTemplate>
                        <DataTemplate>
                          <TextBlock Text="{Binding Converter={StaticResource AIModelConverter}}" />
                        </DataTemplate>
                      </ComboBox.ItemTemplate>
                    </ComboBox>
                  </StackPanel>

                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Gönderim Adeti:" />
                    <NumericUpDown
                      Width="120"
                      HorizontalAlignment="Left"
                      FormatString="0"
                      Maximum="100"
                      Minimum="1"
                      Value="{Binding TerminologySettings.Stage2BatchSize}" />
                  </StackPanel>
                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Prompt:" />
                    <TextBox
                      MinHeight="80"
                      MaxHeight="300"
                      HorizontalContentAlignment="Stretch"
                      VerticalContentAlignment="Stretch"
                      AcceptsReturn="True"
                      Classes="ResizableTextBox"
                      IsEnabled="True"
                      Text="{Binding TerminologySettings.Stage2Prompt, Mode=TwoWay}"
                      TextWrapping="Wrap" />
                  </StackPanel>

                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Function:" />
                    <TextBox
                      MinHeight="80"
                      MaxHeight="300"
                      HorizontalContentAlignment="Stretch"
                      VerticalContentAlignment="Stretch"
                      AcceptsReturn="True"
                      Classes="ResizableTextBox"
                      IsEnabled="True"
                      Text="{Binding TerminologySettings.Stage2Function, Mode=TwoWay}"
                      TextWrapping="Wrap" />
                  </StackPanel>
                </StackPanel>
              </Border>

              <!--  Aşama 3  -->
              <Border
                Padding="10"
                BorderBrush="{DynamicResource SystemControlHighlightBaseMediumBrush}"
                BorderThickness="1"
                CornerRadius="4">
                <StackPanel Spacing="10">
                  <TextBlock
                    FontSize="16"
                    FontWeight="Bold"
                    Text="Aşama 3 - Terim Çevirisi" />
                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="AI Modeli:" />
                    <ComboBox
                      Width="200"
                      HorizontalAlignment="Left"
                      ItemsSource="{Binding TerminologySettings.AvailableAIModels}"
                      SelectedItem="{Binding TerminologySettings.Stage3AIModel}">
                      <ComboBox.ItemTemplate>
                        <DataTemplate>
                          <TextBlock Text="{Binding Converter={StaticResource AIModelConverter}}" />
                        </DataTemplate>
                      </ComboBox.ItemTemplate>
                    </ComboBox>
                  </StackPanel>

                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Gönderim Adeti:" />
                    <NumericUpDown
                      Width="120"
                      HorizontalAlignment="Left"
                      FormatString="0"
                      Maximum="100"
                      Minimum="1"
                      Value="{Binding TerminologySettings.Stage3BatchSize}" />
                  </StackPanel>
                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Prompt:" />
                    <TextBox
                      MinHeight="80"
                      MaxHeight="300"
                      HorizontalContentAlignment="Stretch"
                      VerticalContentAlignment="Stretch"
                      AcceptsReturn="True"
                      Classes="ResizableTextBox"
                      IsEnabled="True"
                      Text="{Binding TerminologySettings.Stage3Prompt, Mode=TwoWay}"
                      TextWrapping="Wrap" />
                  </StackPanel>

                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Function:" />
                    <TextBox
                      MinHeight="80"
                      MaxHeight="300"
                      HorizontalContentAlignment="Stretch"
                      VerticalContentAlignment="Stretch"
                      AcceptsReturn="True"
                      Classes="ResizableTextBox"
                      IsEnabled="True"
                      Text="{Binding TerminologySettings.Stage3Function, Mode=TwoWay}"
                      TextWrapping="Wrap" />
                  </StackPanel>
                </StackPanel>
              </Border>
            </StackPanel>
          </ScrollViewer>
        </TabItem>

        <!--  Bağlam Ayarları  -->
        <TabItem Header="Bağlam Ayarları">
          <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="0,10,20,0" Spacing="20">
              <TextBlock FontSize="24" Text="Bağlam Ayarları" />
              <TextBlock Text="Bu ayarlar Bağlam sayfasında varsayılan değerler olarak kullanılır." TextWrapping="Wrap" />

              <!--  Varsayılan AI Model  -->
              <Border
                Padding="10"
                BorderBrush="{DynamicResource SystemControlHighlightBaseMediumBrush}"
                BorderThickness="1"
                CornerRadius="4">
                <StackPanel Spacing="10">
                  <TextBlock
                    FontSize="16"
                    FontWeight="Bold"
                    Text="Varsayılan AI Model" />
                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="AI Modeli:" />
                    <ComboBox
                      Width="200"
                      HorizontalAlignment="Left"
                      ItemsSource="{Binding ContextSettings.AvailableAIModels}"
                      SelectedItem="{Binding ContextSettings.DefaultAIModel}">
                      <ComboBox.ItemTemplate>
                        <DataTemplate>
                          <TextBlock Text="{Binding Converter={StaticResource AIModelConverter}}" />
                        </DataTemplate>
                      </ComboBox.ItemTemplate>
                    </ComboBox>
                  </StackPanel>
                </StackPanel>
              </Border>

              <!--  Varsayılan Sayfa Sayısı  -->
              <Border
                Padding="10"
                BorderBrush="{DynamicResource SystemControlHighlightBaseMediumBrush}"
                BorderThickness="1"
                CornerRadius="4">
                <StackPanel Spacing="10">
                  <TextBlock
                    FontSize="16"
                    FontWeight="Bold"
                    Text="Varsayılan Sayfa Sayısı" />
                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Web Sayfası Tarama Adeti:" />
                    <NumericUpDown
                      Width="120"
                      HorizontalAlignment="Left"
                      FormatString="0"
                      Maximum="100"
                      Minimum="1"
                      Value="{Binding ContextSettings.DefaultPageCount}" />
                  </StackPanel>
                </StackPanel>
              </Border>

              <!--  Varsayılan Maksimum Derinlik  -->
              <Border
                Padding="10"
                BorderBrush="{DynamicResource SystemControlHighlightBaseMediumBrush}"
                BorderThickness="1"
                CornerRadius="4">
                <StackPanel Spacing="10">
                  <TextBlock
                    FontSize="16"
                    FontWeight="Bold"
                    Text="Varsayılan Maksimum Derinlik" />
                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Web Sayfası Tarama Derinliği:" />
                    <NumericUpDown
                      Width="120"
                      HorizontalAlignment="Left"
                      FormatString="0"
                      Maximum="10"
                      Minimum="1"
                      Value="{Binding ContextSettings.DefaultMaxDepth}" />
                  </StackPanel>
                </StackPanel>
              </Border>

              <!--  Prompt Ayarı  -->
              <Border
                Padding="10"
                BorderBrush="{DynamicResource SystemControlHighlightBaseMediumBrush}"
                BorderThickness="1"
                CornerRadius="4">
                <StackPanel Spacing="10">
                  <TextBlock
                    FontSize="16"
                    FontWeight="Bold"
                    Text="Prompt Ayarı" />
                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Prompt Metni:" />
                    <TextBox
                      MinHeight="80"
                      MaxHeight="300"
                      HorizontalContentAlignment="Stretch"
                      VerticalContentAlignment="Stretch"
                      AcceptsReturn="True"
                      Classes="ResizableTextBox"
                      IsEnabled="True"
                      Text="{Binding ContextSettings.Prompt, Mode=TwoWay}"
                      TextWrapping="Wrap"
                      Watermark="Örn: Lütfen aşağıdaki metni Türkçe'ye çevirin..." />
                  </StackPanel>
                </StackPanel>
              </Border>

              <!--  Context Function Ayarı  -->
              <Border
                Padding="10"
                BorderBrush="{DynamicResource SystemControlHighlightBaseMediumBrush}"
                BorderThickness="1"
                CornerRadius="4">
                <StackPanel Spacing="10">
                  <TextBlock
                    FontSize="16"
                    FontWeight="Bold"
                    Text="Context Function Ayarı" />
                  <StackPanel Spacing="5">
                    <TextBlock FontWeight="SemiBold" Text="Function Metni:" />
                    <TextBox
                      MinHeight="80"
                      MaxHeight="300"
                      HorizontalContentAlignment="Stretch"
                      VerticalContentAlignment="Stretch"
                      AcceptsReturn="True"
                      Classes="ResizableTextBox"
                      IsEnabled="True"
                      Text="{Binding ContextSettings.ContextFunction, Mode=TwoWay}"
                      TextWrapping="Wrap"
                      Watermark="Örn: translate_context" />
                  </StackPanel>
                </StackPanel>
              </Border>
            </StackPanel>
          </ScrollViewer>
        </TabItem>
      </TabControl>

      <!--  Ortak Kaydet Butonu  -->
      <Button
        Margin="0,10,15,0"
        HorizontalAlignment="Right"
        VerticalAlignment="Top"
        Command="{Binding SaveSettingsAsyncCommand}"
        Content="Tüm Ayarları Kaydet" />
    </Grid>
  </Border>

  <UserControl.Styles>
    <Style Selector="TextBox.ResizableTextBox">
      <Setter Property="CornerRadius" Value="3" />
      <Setter Property="BorderThickness" Value="1" />
      <Setter Property="Padding" Value="4" />
      <Setter Property="MinWidth" Value="200" />
      <Setter Property="MinHeight" Value="80" />
    </Style>
  </UserControl.Styles>
</UserControl>
