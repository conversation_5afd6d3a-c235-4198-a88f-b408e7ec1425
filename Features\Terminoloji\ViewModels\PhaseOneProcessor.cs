using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Catalyst;
using Mosaik.Core;
using TextStatistics;
using TranslationAgent.Features.GoogleSheets.ViewModels;
using TranslationAgent.Features.Terminoloji.Models;
using TranslationAgent.Features.Ceviri.Models;
using TranslationAgent.Services;
using TranslationAgent.Helpers;

namespace TranslationAgent.Features.Terminoloji.ViewModels
{
    public class PhaseOneProcessor
    {
        private readonly TerminolojiViewModel _viewModel;
        private CancellationToken _cancellationToken;
        private string _sistemPrompt = string.Empty;

        public PhaseOneProcessor(
            TerminolojiViewModel viewModel)
        {
            _viewModel = viewModel;
        }

        public async Task ProcessAsync()
        {
            _cancellationToken = _viewModel.CancellationToken;

            _viewModel.LogService.Info($"{_viewModel.AppSettings.Terminology.Stage1Prompt}", false, LogService.LogState.Stage1);
            await ProcessTexts();
        }

        private async Task ProcessTexts()
        {
            var textsToProcess = PrepareTextsToProcess();
            if (textsToProcess == null || !textsToProcess.Any())
                return;

            var batches = CreateBatches(textsToProcess);
            await ProcessBatches(batches);
        }

        private List<Text>? PrepareTextsToProcess()
        {
            var textsToProcess = new List<Text>();
            _sistemPrompt = _viewModel.AppSettings.Terminology.Stage1Prompt.Replace("[Ana Bağlam]", _viewModel.GoogleSheetsPanelVm.MainContext?.Icerik ?? "(YOK)");

            // UseFilteredTexts seçeneğine göre TextData veya FilteredTextData kullan
            var dataSource = _viewModel.UseFilteredTexts
                ? _viewModel.CeviriViewModel.FilteredTextData
                : _viewModel.GoogleSheetsPanelVm.TextData;

            foreach (var textData in dataSource)
            {
                _cancellationToken.ThrowIfCancellationRequested();

                // Çeviri durumunu kontrol et - EN veya TR durumunda olan metinleri işle
                if (textData.IsTranslated == false && textData.NeedsTranslation == false)
                {
                    continue;
                }

                // İngilizce metni al
                if (!string.IsNullOrWhiteSpace(textData.EN))
                {
                    textsToProcess.Add(textData);
                }
            }

            // textsToProcess = textsToProcess.Take(30).ToList();
            _viewModel.TotalItemsToProcess = textsToProcess.Count;
            _viewModel.ItemsProcessedCount = 0;
            _viewModel.UpdateStatistics();
            _viewModel.LogStatisticsPanelVm.SetProgressBar(0);
            _viewModel.LogService.Info($"Toplam Metin sayısı: {textsToProcess.Count}", true, LogService.LogState.Stage1);
            return textsToProcess;
        }

        private List<List<Text>> CreateBatches(List<Text> textsToProcess)
        {
            const int maxWordsPerBatch = 500;
            var batches = new List<List<Text>>();
            var currentBatch = new List<Text>();
            int currentBatchWordCount = 0;

            foreach (var text in textsToProcess)
            {
                _cancellationToken.ThrowIfCancellationRequested();

                var wordCount = TextStatistics.TextStatistics.Parse(text.EN).WordCount;

                if (currentBatch.Count > 0 && (currentBatchWordCount + wordCount > maxWordsPerBatch || currentBatch.Count == _viewModel.AppSettings.Terminology.Stage1BatchSize))
                {
                    batches.Add(currentBatch);
                    //_viewModel.LogService.Info($"Batch oluşturuldu. Kelime sayısı: {currentBatchWordCount}, Metin sayısı: {currentBatch.Count}", true, LogService.LogState.Stage1);
                    currentBatch = new List<Text>();
                    currentBatchWordCount = 0;
                }

                currentBatch.Add(text);
                currentBatchWordCount += wordCount;
            }

            if (currentBatch.Count > 0)
            {
                batches.Add(currentBatch);
                //_viewModel.LogService.Info($"Son batch oluşturuldu. Kelime sayısı: {currentBatchWordCount}, Metin sayısı: {currentBatch.Count}", true, LogService.LogState.Stage1);
            }

            _viewModel.LogService.Info($"Toplam Batch sayısı: {batches.Count}", true, LogService.LogState.Stage1);
            return batches;
        }

        private async Task ProcessBatches(List<List<Text>> batches)
        {
            int currentBatch = 0;
            foreach (var batch in batches)
            {
                if (!await ProcessBatch(batch, ++currentBatch, batches.Count))
                    break;
            }
        }

        private async Task<bool> ProcessBatch(List<Text> batch, int currentBatch, int totalBatches)
        {
            _viewModel.LogService.Info($"Batch {currentBatch}/{totalBatches} işleniyor...", true, LogService.LogState.Stage1);

            // Duraklat kontrolü
            await HandlePauseIfNeeded();
            _cancellationToken.ThrowIfCancellationRequested();

            try
            {
                var success = await ProcessBatchWithRetry(batch);
                if (!success)
                    return false;

                _viewModel.ItemsProcessedCount += batch.Count;
                _viewModel.LogService.Info($"Kullanılan Model: {_viewModel.GeminiService.GetCurrentModelInfo()}", false, LogService.LogState.Stage1);
                _viewModel.LogService.Info($"Mevcut Kota Durumu: {_viewModel.GeminiService.GetCurrentQuotaInfo()}", false, LogService.LogState.Stage1);
                _viewModel.LogService.Info($"Batch işlendi: {_viewModel.ItemsProcessedCount}/{_viewModel.TotalItemsToProcess} metin", false, LogService.LogState.Stage1);
                _viewModel.LogService.Info("------------------------------------", true, LogService.LogState.Stage1);

                // Model istatistiklerini güncelle
                _viewModel.UpdateModelQuotaStatistics();
                _viewModel.UpdateStatistics();

                await Task.Delay(100, _cancellationToken);
                return true;
            }
            catch (Exception ex)
            {
                _viewModel.LogService.Error("Batch işleme sürecinde beklenmeyen hata", ex, true, LogService.LogState.Stage1);
                return false;
            }
        }

        private async Task<bool> ProcessBatchWithRetry(List<Text> batch)
        {
            var combinedText = "Analiz etmen gereken metinler:\n";
            Text lastText = null;
            foreach (var text in batch)
            {
                _cancellationToken.ThrowIfCancellationRequested();

                if (_viewModel.SelectedTextContextColumns.Any())
                {
                    foreach (var column in _viewModel.SelectedTextContextColumns)
                    {
                        var contextInfo = _viewModel.GetTextContextInfo(text, column);

                        if (!string.IsNullOrEmpty(contextInfo))
                        {
                            combinedText += $"{column}: {contextInfo}\n";
                        }
                    }
                }
                combinedText += $"Metin: {text.EN}\n\n";
                lastText = text;
            }
            var success = false;
            var retryCount = 0;
            const int maxRetries = 5;

            while (!success && retryCount < maxRetries)
            {
                await HandlePauseIfNeeded();
                _cancellationToken.ThrowIfCancellationRequested();

                try
                {
                    _viewModel.LogService.Info($"{combinedText}", false, LogService.LogState.Stage1);
                    var response = await _viewModel.GeminiService.GenerateContentWithJsonToolsAsync(
                        _sistemPrompt,
                        combinedText,
                        _viewModel.AppSettings.Terminology.Stage1Function,
                        _viewModel.AppSettings.Terminology.Stage1AIModel, LogService.LogState.Stage1, _cancellationToken
                    );

                    if (response == null)
                    {
                        throw new Exception($"AI yanıtı alınamadı!");
                    }
                    else if (response.HasValue)
                    {
                        success = await ProcessGeminiResponse(response.Value);

                    }
                    else
                    {
                        success = false;
                    }
                }
                catch (OperationCanceledException)
                {
                    // İptal edildi, normal durum - hata değil
                    _viewModel.LogService.Warning("İşlem iptal edildi.", true, LogService.LogState.Stage1);
                    return false;
                }
                catch (Exception ex)
                {
                    if (ex.Message.Contains("ServiceUnavailable") && ex.Message.Contains("model is overloaded"))
                    {
                        _viewModel.LogService.Warning($"Deneme {retryCount}/{maxRetries} başarısız oldu. Model aşırı yüklendi. Tekrar deneniyor...", true, LogService.LogState.Stage1);
                    }
                    else
                    {
                        retryCount++;
                        if (retryCount < maxRetries)
                        {
                            _viewModel.LogService.Warning($"Deneme {retryCount}/{maxRetries} başarısız oldu. Tekrar deneniyor... Hata: {ex.Message}", true, LogService.LogState.Stage1);

                            try
                            {
                                await Task.Delay(1000 * retryCount, _cancellationToken);
                            }
                            catch (OperationCanceledException)
                            {

                                _viewModel.LogService.Info($"Son terim ID'si: {lastText?.ID}", true, LogService.LogState.Stage1);
                                _viewModel.LogService.Warning("İşlem iptal edildi.", true, LogService.LogState.Stage1);
                                return false;
                            }
                        }
                        else
                        {
                            _viewModel.LogService.Info($"Son terim ID'si: {lastText?.ID}", true, LogService.LogState.Stage1);
                            _viewModel.LogService.Error($"Batch işlenirken maksimum deneme sayısına ulaşıldı", ex, true, LogService.LogState.Stage1);
                            return false;
                        }
                    }
                }
            }

            return success;
        }

        private async Task<bool> ProcessGeminiResponse(JsonElement response)
        {
            var terimlerElement = response.GetProperty("terimler");
            if (terimlerElement.ValueKind != JsonValueKind.Array)
            {
                return false;
            }

            var validatedTerms = new List<TermViewModel>();
            foreach (var terim in terimlerElement.EnumerateArray())
            {
                _cancellationToken.ThrowIfCancellationRequested();

                if (terim.ValueKind == JsonValueKind.String)
                {
                    var terimStr = terim.GetString();
                    _viewModel.LogService.Info($"{terimStr}", false, LogService.LogState.Stage1);
                    if (!string.IsNullOrWhiteSpace(terimStr))
                    {
                        // Terimi analiz et
                        var termViewModel = AnalyzeTermInTexts(terimStr);
                        if (termViewModel != null)
                        {
                            validatedTerms.Add(termViewModel);
                        }
                    }
                }
            }

            // Tüm geçerli terimleri toplu olarak Google Sheets'e ekle
            if (validatedTerms.Count > 0)
            {
                await AddTermsToGoogleSheetsBatchAsync(validatedTerms);
            }

            return true;
        }

        private TermViewModel? AnalyzeTermInTexts(string term)
        {
            try
            {
                var cleanedTerm = TextProcessingHelper.TermCleanStopWords(term);
                var lemmatizedTerm = TextProcessingHelper.ProcessTextToLemma(cleanedTerm, _viewModel.Nlp);

                // Zaten mevcut terimleri kontrol et
                if (_viewModel.GoogleSheetsPanelVm.TerminologyData.Any(t =>
                    t.Lemma != null && t.Lemma.Equals(lemmatizedTerm, StringComparison.OrdinalIgnoreCase)))
                {
                    _viewModel.LogService.Info($"'{cleanedTerm}' terimi zaten mevcut, atlanıyor.", true, LogService.LogState.Stage1);
                    return null;
                }

                string pattern = $@"\b{Regex.Escape(lemmatizedTerm)}\b";

                var matchedTexts = _viewModel.GoogleSheetsPanelVm.TextData
                .Where(text => text.NeedsTranslation || text.IsTranslated)
                .Where(text => !string.IsNullOrWhiteSpace(text.Lemma) &&
                    Regex.IsMatch(text.Lemma, pattern, RegexOptions.IgnoreCase)).Count();

                if (matchedTexts >= 2)
                {
                    _viewModel.LogService.Info($"'{cleanedTerm}' terimi {matchedTexts} metinde var - Kabul edildi.", true, LogService.LogState.Stage1);

                    return new TermViewModel
                    {
                        ID = 0, // ID toplu ekleme sırasında atanacak
                        EN = cleanedTerm,
                        TR = string.Empty,
                        Kategori = string.Empty,
                        Bilgi = string.Empty,
                        Lemma = lemmatizedTerm,
                        Status = TermStatus.İncelemede,
                    };
                }

                _viewModel.LogService.Info($"'{cleanedTerm}' terimi yeterli sayıda bulunamadı - Reddedildi.", true, LogService.LogState.Stage1);
                return null;
            }
            catch (Exception ex)
            {
                _viewModel.LogService.Error("Terim analizi sırasında hata", ex, true, LogService.LogState.Stage1);
                return null;
            }
        }

        private async Task AddTermsToGoogleSheetsBatchAsync(List<TermViewModel> terms)
        {
            if (terms == null || terms.Count == 0)
                return;

            try
            {
                if (!_viewModel.GoogleSheetsPanelVm.IsTerminologyDataFetched || string.IsNullOrWhiteSpace(_viewModel.GoogleSheetsPanelVm.SpreadsheetId))
                {
                    _viewModel.LogService.Warning("Google Sheets bağlantısı hazır değil. Terimler eklenemedi.", true, LogService.LogState.Stage1);
                    return;
                }

                var selectedSheet = _viewModel.GoogleSheetsPanelVm.SelectedTerminologySheet;
                if (selectedSheet == null || string.IsNullOrWhiteSpace(selectedSheet.SheetName))
                {
                    _viewModel.LogService.Warning("Terminoloji sayfası seçili değil. Terimler eklenemedi.", true, LogService.LogState.Stage1);
                    return;
                }


                if (terms.Count == 0)
                {
                    _viewModel.LogService.Info("Toplu ekleme için hazırlanan terim listesi boş, yeni terim eklenmedi.", true, LogService.LogState.Stage1);
                    return;
                }

                // Toplu ekleme için veri hazırla - ID'leri oluştur
                var allRowsData = new List<IList<object>>();
                var nextId = _viewModel.GoogleSheetsPanelVm.TerminologyData.Any() ?
                    _viewModel.GoogleSheetsPanelVm.TerminologyData.Max(t => t.ID) + 1 : 1;

                foreach (var term in terms)
                {
                    term.ID = nextId++; // Her terime benzersiz ID ata
                    var rowData = new List<object>
                    {
                        term.ID,
                        term.EN ?? string.Empty,
                        term.TR ?? string.Empty,
                        term.Kategori ?? string.Empty,
                        term.Bilgi ?? string.Empty,
                        term.Status.ToString()
                    };
                    allRowsData.Add(rowData);
                }

                // Toplu olarak Google Sheets'e ekle
                await _viewModel.GoogleSheetsService.AppendSheetDataAsync(_viewModel.GoogleSheetsPanelVm.SpreadsheetId, selectedSheet.SheetName, allRowsData);
                _viewModel.LogService.Info($"Toplam {terms.Count} terim '{selectedSheet.SheetName}' sayfasına toplu olarak eklendi.", true, LogService.LogState.Stage1);

                // Local veriyi güncelle
                foreach (var term in terms)
                {
                    _viewModel.GoogleSheetsPanelVm.TerminologyData.Add(term);
                }
            }
            catch (Exception ex)
            {
                _viewModel.LogService.Error("Terimler Google Sheets'e toplu eklenirken hata oluştu", ex, true, LogService.LogState.Stage1);
            }
        }

        private async Task HandlePauseIfNeeded()
        {
            if (_viewModel.IsPaused && _viewModel.IsProcessing)
            {
                _viewModel.IsOperationInProgress = false;
                _viewModel.NotifyRelatedCommands();
                _viewModel.LogService.Warning("İşlem duraklatıldı - Devam etmek için 'Devam Et' butonuna basın.", true, LogService.LogState.Stage1);

                try
                {
                    while (_viewModel.IsPaused && _viewModel.IsProcessing)
                    {
                        await Task.Delay(500, _cancellationToken);
                    }

                    if (!_viewModel.IsPaused && _viewModel.IsProcessing)
                    {
                        _viewModel.LogService.Success("İşlem devam ediyor.", true, LogService.LogState.Stage1);
                    }
                }
                catch (OperationCanceledException)
                {
                    // İptal edildi, normal durum
                    _viewModel.LogService.Warning("İşlem iptal edildi.", true, LogService.LogState.Stage1);
                    throw;
                }
            }
        }
    }
}
