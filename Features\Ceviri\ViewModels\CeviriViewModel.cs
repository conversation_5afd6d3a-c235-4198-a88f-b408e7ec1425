using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using TranslationAgent.Features.GoogleSheets.ViewModels;
using TranslationAgent.Services;
using TranslationAgent.Shared.ViewModels;
using TranslationAgent.Features.Ceviri.Models;
using System.ComponentModel;

namespace TranslationAgent.Features.Ceviri.ViewModels
{
    public partial class CeviriViewModel : ObservableObject
    {
        private readonly GoogleSheetsService _googleSheetsService;
        public readonly GeminiService GeminiService;
        public readonly AnthropicService AnthropicService;
        public readonly LogService _logService;
        private readonly AppSettings _appSettings;
        public GoogleSheetsPanelViewModel GoogleSheetsPanelVm { get; }
        public AppSettings AppSettings => _appSettings;
        public GoogleSheetsService GoogleSheetsService => _googleSheetsService;

        [ObservableProperty]
        private LogStatisticsPanelViewModel _logStatisticsPanelVm;

        [ObservableProperty]
        private ObservableCollection<string> _translationsStatus = new();

        [ObservableProperty]
        private double _progress;

        [ObservableProperty]
        private bool _isProcessing;

        // CancellationToken için yeni özellikler
        private CancellationTokenSource? _cancellationTokenSource;
        public CancellationToken CancellationToken => _cancellationTokenSource?.Token ?? CancellationToken.None;

        [ObservableProperty]
        private bool _isOperationInProgress = false; // Butonları deaktif etmek için

        [ObservableProperty]
        private bool _isPaused = false;

        [ObservableProperty]
        private string _pauseToggleButtonText = "Duraklat";

        [ObservableProperty]
        private int _totalTexts;

        [ObservableProperty]
        private int _translatedTexts;

        [ObservableProperty]
        private int _pendingTexts;

        [ObservableProperty]
        private int _duplicateTexts;

        // Filtreleme özellikleri
        [ObservableProperty]
        private ObservableCollection<Text> _filteredTextData = new();

        [ObservableProperty]
        private Text? _selectedText;

        [ObservableProperty]
        private string _quickSearchText = string.Empty;

        [ObservableProperty]
        private ObservableCollection<string> _statusFilterOptions = new();

        [ObservableProperty]
        private string _selectedStatusFilter = "Tümü";

        // Çeviri modülü için özel filtreler
        [ObservableProperty]
        private ObservableCollection<CeviriFilterEntry> _ceviriFilterEntries = new();

        [ObservableProperty]
        private ObservableCollection<string> _ceviriColumnHeaders = new();

        // Filtreleme kontrolü için flag
        private bool _isApplyingFilters = false;

        // Düzenleme paneli özellikleri
        [ObservableProperty]
        private bool _isEditMode = false;

        [ObservableProperty]
        private string _editEN = string.Empty;

        [ObservableProperty]
        private string _editTR = string.Empty;

        [ObservableProperty]
        private TranslationQuality _selectedQuality = TranslationQuality.Eco;

        public Array AvailableQualities => Enum.GetValues(typeof(TranslationQuality));

        [ObservableProperty]
        private TranslationStatus _editStatus = TranslationStatus.EN;

        [ObservableProperty]
        private string _editNamespace = string.Empty;

        [ObservableProperty]
        private string _editKey = string.Empty;

        [ObservableProperty]
        private string _editNote = string.Empty;

        // Opsiyonel Prompt özelliği
        [ObservableProperty]
        private string _optionalPrompt = string.Empty;

        // Not ekleme özellikleri
        [ObservableProperty]
        private string _noteText = string.Empty;

        [ObservableProperty]
        private string _bulkNoteText = string.Empty;

        [ObservableProperty]
        private bool _isBulkNoteProcessing = false;

        [ObservableProperty]
        private double _bulkNoteProgress = 0;

        [ObservableProperty]
        private string _bulkNoteProgressText = string.Empty;

        // Text Context Column özellikleri
        [ObservableProperty]
        private ObservableCollection<string> _selectedTextContextColumns = new();

        [ObservableProperty]
        private ObservableCollection<string> _availableTextContextColumns = new();

        /// <summary>
        /// Text Context Column ayarının görünür olup olmadığını kontrol eder
        /// </summary>
        public bool IsTextContextColumnVisible => GoogleSheetsPanelVm?.TextData?.Any(t => t.HasKey || t.HasNamespace) ?? false;

        /// <summary>
        /// Namespace sütununun görünür olup olmadığını kontrol eder
        /// </summary>
        public bool HasNamespace => GoogleSheetsPanelVm.TextData.Any(t => t.HasNamespace);

        /// <summary>
        /// Key sütununun görünür olup olmadığını kontrol eder
        /// </summary>
        public bool HasKey => GoogleSheetsPanelVm.TextData.Any(t => t.HasKey);

        /// <summary>
        /// Note sütununun görünür olup olmadığını kontrol eder
        /// </summary>
        public bool HasNote => GoogleSheetsPanelVm.TextData.Any(t => t.HasNote);

        public CeviriViewModel(GoogleSheetsService googleSheetsService, GoogleSheetsPanelViewModel googleSheetsPanelVm, GeminiService geminiService, AnthropicService anthropicService, LogService logService, AppSettings appSettings)
        {
            _googleSheetsService = googleSheetsService;
            GeminiService = geminiService;
            AnthropicService = anthropicService;
            _logService = logService;
            _appSettings = appSettings;
            GoogleSheetsPanelVm = googleSheetsPanelVm;

            // Çeviri kalitesini AppSettings'ten yükle
            SelectedQuality = _appSettings.Translation.Quality;

            // Log ve İstatistik Panel ViewModel'ini başlat ve özel etiketler ayarla - Çeviri için Translate loglarını dinle
            _logStatisticsPanelVm = new LogStatisticsPanelViewModel(logService, LogService.LogState.Translate);
            _logStatisticsPanelVm.SetCustomLabels("Çeviri İstatistikleri", "Çeviri İlerlemesi:");

            // Çeviri sayfası için farklı istatistik yapısı
            _logStatisticsPanelVm.SetStatisticItems(
                ("Toplam Metin:", "0"),
                ("Çevrilen Metin:", "0"),
                ("Bekleyen Metin:", "0")
            );

            // Durum filtresi seçeneklerini başlat
            StatusFilterOptions.Add("Tümü");
            StatusFilterOptions.Add("EN");
            StatusFilterOptions.Add("TR");
            StatusFilterOptions.Add("NULL");
            StatusFilterOptions.Add("DUPE");

            // Çeviri sütun başlıklarını başlat
            CeviriColumnHeaders.Add("#");
            CeviriColumnHeaders.Add("EN");
            CeviriColumnHeaders.Add("TR");
            CeviriColumnHeaders.Add("Durum");
            CeviriColumnHeaders.Add("Namespace");
            CeviriColumnHeaders.Add("Key");
            CeviriColumnHeaders.Add("Not");

            InitializeCommands();

            // Text Context Column seçeneklerini başlat
            UpdateTextContextColumns();

            // Text değişikliklerini dinle
            GoogleSheetsPanelVm.PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(GoogleSheetsPanelVm.IsTextDataFetched))
                {
                    UpdateTextDataAndStatistics();
                    UpdateTextContextColumns();
                    OnPropertyChanged(nameof(IsTextContextColumnVisible));
                }
            };

            // Hızlı arama değişikliklerini dinle
            PropertyChanged += (s, e) =>
            {
                if (!_isApplyingFilters && (e.PropertyName == nameof(QuickSearchText) || e.PropertyName == nameof(SelectedStatusFilter)))
                {
                    ApplyFilters();
                }
            };

            // GoogleSheetsPanelVm'deki IsTextDataFetched değişikliklerini dinle - sadece veri yükleme tamamlandığında tetikle
            GoogleSheetsPanelVm.PropertyChanged += (s, e) =>
            {
                if (!_isApplyingFilters && e.PropertyName == nameof(GoogleSheetsPanelVm.IsTextDataFetched) && GoogleSheetsPanelVm.IsTextDataFetched)
                {
                    ApplyFilters();
                }
            };
        }

        private void InitializeCommands()
        {
            // İşlem kontrol komutları
            ProcessStartCommand = new RelayCommand(StartTranslation, () => !IsProcessing && !IsOperationInProgress);
            ProcessPauseToggleCommand = new RelayCommand(PauseOrResumeProcessing, () => IsProcessing && !IsOperationInProgress);
            ProcessStopCommand = new RelayCommand(StopProcessing, () => IsProcessing);

            // Diğer komutlar
            RefreshStatusCommand = new RelayCommand(RefreshStatus);
            ApplyFiltersCommand = new RelayCommand(ApplyFilters);
            ClearFiltersCommand = new RelayCommand(ClearFilters);
            AddCeviriFilterCommand = new RelayCommand(AddCeviriFilter);
            RemoveCeviriFilterCommand = new RelayCommand<CeviriFilterEntry>(RemoveCeviriFilter);

            // Düzenleme komutları
            EditTextCommand = new RelayCommand<Text>(StartEdit);
            SaveEditCommand = new RelayCommand(SaveEdit, () => IsEditMode);
            CancelEditCommand = new RelayCommand(CancelEdit, () => IsEditMode);

            // Not ekleme komutları
            AddNoteCommand = new RelayCommand(AddNote, () => SelectedText != null && NoteText != SelectedText.Note);
            AddBulkNoteCommand = new RelayCommand(AddBulkNote, () => FilteredTextData.Any() && !string.IsNullOrWhiteSpace(BulkNoteText) && !IsBulkNoteProcessing);
            CancelBulkNoteCommand = new RelayCommand(CancelBulkNote, () => IsBulkNoteProcessing);
        }

        // İşlem kontrol komutları
        public IRelayCommand ProcessStartCommand { get; private set; }
        public IRelayCommand ProcessPauseToggleCommand { get; private set; }
        public IRelayCommand ProcessStopCommand { get; private set; }

        // Diğer komutlar
        public IRelayCommand RefreshStatusCommand { get; private set; }
        public IRelayCommand ApplyFiltersCommand { get; private set; }
        public IRelayCommand ClearFiltersCommand { get; private set; }
        public IRelayCommand AddCeviriFilterCommand { get; private set; }
        public IRelayCommand<CeviriFilterEntry> RemoveCeviriFilterCommand { get; private set; }

        // Düzenleme komutları
        public IRelayCommand<Text> EditTextCommand { get; private set; }
        public IRelayCommand SaveEditCommand { get; private set; }
        public IRelayCommand CancelEditCommand { get; private set; }

        // Not ekleme komutları
        public IRelayCommand AddNoteCommand { get; private set; }
        public IRelayCommand AddBulkNoteCommand { get; private set; }
        public IRelayCommand CancelBulkNoteCommand { get; private set; }

        // Eski komut - geriye dönük uyumluluk için
        public IRelayCommand StartTranslationCommand => ProcessStartCommand;


        private async void StartTranslation()
        {
            if (GoogleSheetsPanelVm.SelectedTextSheet == null)
            {
                _logService.Warning("Lütfen önce bir çeviri sayfası seçin", true, LogService.LogState.Translate);
                return;
            }

            if (!GoogleSheetsPanelVm.IsTextDataFetched || !GoogleSheetsPanelVm.TextData.Any())
            {
                _logService.Warning("Çeviri verileri yüklenmemiş. Lütfen önce verileri çekin.", true, LogService.LogState.Translate);
                return;
            }

            var pendingTexts = GoogleSheetsPanelVm.TextData.Where(t => t.NeedsTranslation).ToList();
            if (!pendingTexts.Any())
            {
                _logService.Info("Çevrilecek metin bulunamadı", true, LogService.LogState.Translate);
                return;
            }

            // Yeni CancellationTokenSource oluştur
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = new CancellationTokenSource();

            IsProcessing = true;
            IsPaused = false;
            IsOperationInProgress = false;
            PauseToggleButtonText = "Duraklat";
            Progress = 0;
            TranslationsStatus.Clear();
            TranslationsStatus.Add($"Çeviri işlemi başlatıldı - {pendingTexts.Count} metin");
            NotifyRelatedCommands();

            _logService.Info($"Çeviri işlemi başlatıldı - {pendingTexts.Count} metin çevrilecek", true, LogService.LogState.Translate);
            LogStatisticsPanelVm.ClearLogs();

            UpdateStatistics();

            try
            {
                // TranslationProcessor ile çeviri işlemini başlat
                var processor = new TranslationProcessor(this);
                await processor.ProcessAsync();

                // İşlem tamamlandığında durumu sıfırla
                IsProcessing = false;
                IsOperationInProgress = false;
                _logService.Success("Çeviri işlemi tamamlandı", true, LogService.LogState.Translate);
            }
            catch (OperationCanceledException)
            {
                // İptal edildi, normal durum - hata değil
                IsProcessing = false;
                IsOperationInProgress = false;
                IsPaused = false;
                PauseToggleButtonText = "Duraklat";
                _logService.Warning("Çeviri işlemi iptal edildi", true, LogService.LogState.Translate);
            }
            catch (Exception ex)
            {
                IsProcessing = false;
                IsOperationInProgress = false;
                IsPaused = false;
                PauseToggleButtonText = "Duraklat";
                _logService.Error("Çeviri işlemi sırasında hata oluştu", ex, true, LogService.LogState.Translate);
            }
            finally
            {
                NotifyRelatedCommands();
            }
        }

        private void PauseOrResumeProcessing()
        {
            if (IsOperationInProgress)
                return; // Eğer bir işlem devam ediyorsa duraklat/devam et butonunu deaktif et

            IsPaused = !IsPaused;
            PauseToggleButtonText = IsPaused ? "Devam Et" : "Duraklat";

            if (IsPaused)
            {
                IsOperationInProgress = true;
                _logService.Warning("Çeviri işlemi duraklatılıyor... Mevcut işlem tamamlanana kadar bekleyiniz...", true, LogService.LogState.Translate);
                NotifyRelatedCommands();
            }
            else
            {
                _logService.Info("Çeviri işlemi devam edecek.", true, LogService.LogState.Translate);
                // IsOperationInProgress = false; // Bu HandlePauseIfNeeded metodunda yapılacak
            }
        }

        private void StopProcessing()
        {
            _logService.Warning("Çeviri işlemi durduruluyor...", true, LogService.LogState.Translate);

            // CancellationToken'ı iptal et
            _cancellationTokenSource?.Cancel();

            // Butonları deaktif et - işlem tamamen durana kadar
            IsOperationInProgress = true;
            NotifyRelatedCommands();

            // Not: Gerçek durum sıfırlama OperationCanceledException catch bloğunda yapılacak
        }



        private void UpdateTextDataAndStatistics()
        {
            if (GoogleSheetsPanelVm.IsTextDataFetched)
            {
                UpdateStatistics();

                // Sütun görünürlüğü property'lerini güncelle
                OnPropertyChanged(nameof(HasNamespace));
                OnPropertyChanged(nameof(HasKey));
                OnPropertyChanged(nameof(HasNote));
            }
        }



        public void UpdateStatistics()
        {
            var allTexts = GoogleSheetsPanelVm.TextData;

            TotalTexts = allTexts.Count;
            TranslatedTexts = allTexts.Count(t => t.IsTranslated);
            PendingTexts = allTexts.Count(t => t.NeedsTranslation);

            // İstatistik panelini güncelle
            LogStatisticsPanelVm.SetStatisticItems(
                ("Toplam Metin:", TotalTexts.ToString()),
                ("Çevrilen Metin:", TranslatedTexts.ToString()),
                ("Bekleyen Metin:", PendingTexts.ToString()),
                ("Tamamlanma:", TotalTexts > 0 ? $"%{(TranslatedTexts * 100 / TotalTexts):F1}" : "%0")
            );

            // Progress bar güncelleme
            var progressPercentage = TotalTexts > 0 ? (double)TranslatedTexts / TotalTexts * 100 : 0;
            LogStatisticsPanelVm.SetProgressBar(progressPercentage);
        }

        private void ApplyFilters()
        {
            if (_isApplyingFilters) return; // Döngüsel çağrıları engelle

            _isApplyingFilters = true;

            try
            {
                var filteredData = GoogleSheetsPanelVm.TextData.AsEnumerable();

                // Hızlı arama filtresi
                if (!string.IsNullOrWhiteSpace(QuickSearchText))
                {
                    var searchText = QuickSearchText.ToLowerInvariant();
                    filteredData = filteredData.Where(t =>
                        t.EN.ToLowerInvariant().Contains(searchText) ||
                        t.TR.ToLowerInvariant().Contains(searchText) ||
                        (t.Namespace?.ToLowerInvariant().Contains(searchText) ?? false) ||
                        (t.Key?.ToLowerInvariant().Contains(searchText) ?? false) ||
                        (t.Note?.ToLowerInvariant().Contains(searchText) ?? false) ||
                        t.StatusText.ToLowerInvariant().Contains(searchText) ||
                        t.ID.ToString().Contains(searchText)
                    );
                }

                // Durum filtresi
                if (SelectedStatusFilter != "Tümü")
                {
                    filteredData = filteredData.Where(t => t.StatusText == SelectedStatusFilter);
                }

                // Çeviri modülünün gelişmiş filtreleri
                foreach (var filter in CeviriFilterEntries.Where(f => !string.IsNullOrWhiteSpace(f.FilterValue)))
                {
                    var filterValue = filter.FilterValue;
                    var columnName = filter.ColumnName;
                    var operatorType = filter.SelectedOperator;
                    var filterType = filter.SelectedType;

                    filteredData = filteredData.Where(t =>
                    {
                        string stringValue = columnName switch
                        {
                            "#" => t.ID.ToString(),
                            "EN" => t.EN,
                            "TR" => t.TR,
                            "Durum" => t.StatusText,
                            "Namespace" => t.Namespace ?? string.Empty,
                            "Key" => t.Key ?? string.Empty,
                            "Not" => t.Note ?? string.Empty,
                            _ => string.Empty
                        };

                        return filterType switch
                        {
                            "Number" => ApplyNumberFilter(stringValue, filterValue, operatorType),
                            "Boolean" => ApplyBooleanFilter(stringValue, filterValue, operatorType),
                            _ => ApplyStringFilter(stringValue, filterValue, operatorType)
                        };
                    });
                }

                // Sonuçları güncelle
                FilteredTextData.Clear();
                foreach (var item in filteredData)
                {
                    FilteredTextData.Add(item);
                }

                _logService.Info($"Filtre uygulandı: {FilteredTextData.Count}/{GoogleSheetsPanelVm.TextData.Count} kayıt gösteriliyor", false, LogService.LogState.Translate);
            }
            finally
            {
                _isApplyingFilters = false;
            }
        }

        private bool ApplyStringFilter(string value, string filterValue, string operatorType)
        {
            return operatorType switch
            {
                "=" => value.Equals(filterValue, StringComparison.OrdinalIgnoreCase),
                "!=" => !value.Equals(filterValue, StringComparison.OrdinalIgnoreCase),
                "Contains" => value.Contains(filterValue, StringComparison.OrdinalIgnoreCase),
                "Not Contains" => !value.Contains(filterValue, StringComparison.OrdinalIgnoreCase),
                "Regex" => System.Text.RegularExpressions.Regex.IsMatch(value, filterValue, System.Text.RegularExpressions.RegexOptions.IgnoreCase),
                "Not Regex" => !System.Text.RegularExpressions.Regex.IsMatch(value, filterValue, System.Text.RegularExpressions.RegexOptions.IgnoreCase),
                _ => true
            };
        }

        private bool ApplyNumberFilter(string value, string filterValue, string operatorType)
        {
            if (!double.TryParse(value, out double numValue) || !double.TryParse(filterValue, out double filterNumValue))
                return false;

            return operatorType switch
            {
                "=" => Math.Abs(numValue - filterNumValue) < 0.0001,
                "!=" => Math.Abs(numValue - filterNumValue) >= 0.0001,
                ">" => numValue > filterNumValue,
                ">=" => numValue >= filterNumValue,
                "<" => numValue < filterNumValue,
                "<=" => numValue <= filterNumValue,
                _ => true
            };
        }

        private bool ApplyBooleanFilter(string value, string filterValue, string operatorType)
        {
            if (!bool.TryParse(value, out bool boolValue) || !bool.TryParse(filterValue, out bool filterBoolValue))
                return false;

            return operatorType switch
            {
                "=" => boolValue == filterBoolValue,
                "!=" => boolValue != filterBoolValue,
                _ => true
            };
        }

        private void ClearFilters()
        {
            QuickSearchText = string.Empty;
            SelectedStatusFilter = "Tümü";
            CeviriFilterEntries.Clear(); // Çeviri modülünün kendi filtrelerini temizle

            // Tüm verileri göster
            FilteredTextData.Clear();
            foreach (var item in GoogleSheetsPanelVm.TextData)
            {
                FilteredTextData.Add(item);
            }

            _logService.Info("Filtreler temizlendi", false, LogService.LogState.Translate);
        }

        private void AddCeviriFilter()
        {
            if (CeviriColumnHeaders.Count > 0)
            {
                CeviriFilterEntries.Add(new CeviriFilterEntry { ColumnName = CeviriColumnHeaders[0] });
            }
        }

        private void RemoveCeviriFilter(CeviriFilterEntry? filter)
        {
            if (filter != null)
            {
                CeviriFilterEntries.Remove(filter);
                ApplyFilters(); // Filtre kaldırıldığında yeniden uygula
            }
        }

        public void NotifyRelatedCommands()
        {
            ((RelayCommand)ProcessStartCommand).NotifyCanExecuteChanged();
            ((RelayCommand)ProcessPauseToggleCommand).NotifyCanExecuteChanged();
            ((RelayCommand)ProcessStopCommand).NotifyCanExecuteChanged();
            ((RelayCommand)AddNoteCommand).NotifyCanExecuteChanged();
            ((RelayCommand)AddBulkNoteCommand).NotifyCanExecuteChanged();
        }

        partial void OnIsProcessingChanged(bool value)
        {
            NotifyRelatedCommands();
        }

        partial void OnIsOperationInProgressChanged(bool value)
        {
            NotifyRelatedCommands();
        }

        partial void OnIsEditModeChanged(bool value)
        {
            NotifyEditCommands();
        }

        private void RefreshStatus()
        {
            UpdateStatistics();
            ApplyFilters();
            _logService.Info("Durum bilgileri yenilendi", true, LogService.LogState.Translate);
        }

        #region Edit Operations (Panel Düzenleme İşlemleri)

        /// <summary>
        /// Düzenleme modunu başlatır ve seçili metnin bilgilerini düzenleme alanlarına yükler
        /// </summary>
        private void StartEdit(Text? text)
        {
            if (text == null) return;

            SelectedText = text;
            IsEditMode = true;

            // Mevcut değerleri düzenleme alanlarına yükle
            LoadTextToEditFields(text);

            _logService.Info($"'{text.EN}' metni düzenleme moduna alındı.", true, LogService.LogState.Translate);
            NotifyEditCommands();
        }

        /// <summary>
        /// Seçili metnin bilgilerini düzenleme alanlarına yükler
        /// </summary>
        private void LoadTextToEditFields(Text text)
        {
            EditEN = text.EN;
            EditTR = text.TR;
            EditStatus = text.Status;
            EditNamespace = text.Namespace ?? string.Empty;
            EditKey = text.Key ?? string.Empty;
            EditNote = text.Note ?? string.Empty;
        }

        /// <summary>
        /// Düzenleme işlemini kaydeder
        /// </summary>
        private async void SaveEdit()
        {
            if (SelectedText == null) return;

            try
            {
                var originalText = SelectedText;

                // Local veriyi güncelle
                ApplyEditChangesToText(SelectedText, EditEN, EditTR, EditStatus, EditNamespace, EditKey, EditNote);

                // Google Sheets'i güncelle
                await UpdateTextInGoogleSheetsAsync(SelectedText);

                _logService.Info($"'{originalText.Key ?? originalText.ID.ToString()}' metni güncellendi.", true, LogService.LogState.Translate);

                // İstatistikleri güncelle
                UpdateStatistics();

                // Filtreleri yeniden uygula
                ApplyFilters();

                // Düzenleme modunu kapat
                ExitEditMode();
            }
            catch (Exception ex)
            {
                _logService.Error($"Metin güncellenirken hata oluştu: {ex.Message}", ex, true, LogService.LogState.Translate);
            }
        }

        /// <summary>
        /// Düzenleme işlemini iptal eder
        /// </summary>
        private void CancelEdit()
        {
            ExitEditMode();
            _logService.Info("Düzenleme iptal edildi.", true, LogService.LogState.Translate);
        }

        /// <summary>
        /// Düzenleme modundan çıkar
        /// </summary>
        private void ExitEditMode()
        {
            IsEditMode = false;
            SelectedText = null;

            // Düzenleme alanlarını temizle
            EditEN = string.Empty;
            EditTR = string.Empty;
            EditStatus = TranslationStatus.EN;
            EditNamespace = string.Empty;
            EditKey = string.Empty;
            EditNote = string.Empty;

            NotifyEditCommands();
        }

        /// <summary>
        /// Düzenleme değişikliklerini Text nesnesine uygular
        /// </summary>
        private void ApplyEditChangesToText(Text text, string newEN, string newTR, TranslationStatus newStatus, string newNamespace, string newKey, string newNote)
        {
            text.EN = newEN;
            text.TR = newTR;
            text.Status = newStatus;
            text.Namespace = string.IsNullOrWhiteSpace(newNamespace) ? null : newNamespace;
            text.Key = string.IsNullOrWhiteSpace(newKey) ? null : newKey;
            text.Note = string.IsNullOrWhiteSpace(newNote) ? null : newNote;
        }

        /// <summary>
        /// Google Sheets'te metni günceller
        /// </summary>
        private async Task UpdateTextInGoogleSheetsAsync(Text text, IList<IList<object>>? existingData = null)
        {
            if (GoogleSheetsPanelVm.SelectedSpreadsheet == null || GoogleSheetsPanelVm.SelectedTextSheet == null)
            {
                throw new InvalidOperationException("Spreadsheet veya sheet seçilmemiş");
            }
            var selectedSheet = GoogleSheetsPanelVm.SelectedTextSheet;
            var spreadsheetId = GoogleSheetsPanelVm.SpreadsheetId;

            if (existingData == null)
            {
                // Satır indeksini bul (RowNumber - 1, çünkü başlık satırı var)
                existingData = await GoogleSheetsService.GetDataAsync(spreadsheetId, selectedSheet.SheetName);
            }

            if (existingData != null && existingData.Count > 1) // Başlık satırı + en az 1 veri satırı
            {
                // Text nesnesinin row number'ı ile eşleşen satırı bul
                var rowIndex = existingData
                    .Select((r, idx) => new { r, idx })
                    .Skip(1) // başlık satırını atla
                    .FirstOrDefault(x => x.r.Count > 0 &&
                                         int.TryParse(x.r[0]?.ToString(), out int rid) &&
                                         rid == text.ID)?.idx;

                // Güncelleme verilerini hazırla
                var updatedRowData = new List<object>
                {
                    text.ID, // # sütunu
                };

                // Namespace ve Key sütunları varsa ekle
                if (HasNamespace)
                {
                    updatedRowData.Add(text.Namespace ?? "");
                }
                if (HasKey)
                {
                    updatedRowData.Add(text.Key ?? "");
                }
                updatedRowData.Add(text.EN);
                updatedRowData.Add(text.TR);
                updatedRowData.Add(text.Status.ToString());
                updatedRowData.Add(text.Note ?? "");

                // Google Sheets'te güncelle
                await _googleSheetsService.UpdateRowAsync(spreadsheetId, selectedSheet.SheetName, rowIndex.Value, updatedRowData);
                _logService.Success($"Metin Google Sheets'te başarıyla güncellendi: {text.Key ?? text.ID.ToString()}", true, LogService.LogState.Translate);
            }
        }

        /// <summary>
        /// Düzenleme komutlarının durumunu günceller
        /// </summary>
        private void NotifyEditCommands()
        {
            ((RelayCommand)SaveEditCommand).NotifyCanExecuteChanged();
            ((RelayCommand)CancelEditCommand).NotifyCanExecuteChanged();
        }

        #endregion

        #region Text Context Column Methods

        /// <summary>
        /// Text Context Column seçeneklerini günceller
        /// </summary>
        private void UpdateTextContextColumns()
        {
            try
            {
                AvailableTextContextColumns.Clear();

                if (GoogleSheetsPanelVm?.TextData != null && GoogleSheetsPanelVm.TextData.Any())
                {
                    // Key sütunu varsa ekle
                    if (GoogleSheetsPanelVm.TextData.Any(t => t.HasKey))
                    {
                        AvailableTextContextColumns.Add("Key");
                    }

                    // Namespace sütunu varsa ekle
                    if (GoogleSheetsPanelVm.TextData.Any(t => t.HasNamespace))
                    {
                        AvailableTextContextColumns.Add("Namespace");
                    }


                    // Seçili sütunları güncelle - mevcut olmayan sütunları kaldır
                    var itemsToRemove = SelectedTextContextColumns.Where(col => !AvailableTextContextColumns.Contains(col)).ToList();
                    foreach (var item in itemsToRemove)
                    {
                        SelectedTextContextColumns.Remove(item);
                    }


                }
                else
                {
                    SelectedTextContextColumns.Clear();
                }
            }
            catch (Exception ex)
            {
                _logService.Error($"Text Context Column seçenekleri güncellenirken hata oluştu: {ex.Message}", ex, true, LogService.LogState.Translate);
            }
        }

        /// <summary>
        /// Seçili metin bağlam sütunlarına göre bağlam bilgisini döndürür
        /// </summary>
        /// <param name="text">Metin objesi</param>
        /// <returns>Bağlam bilgisi</returns>
        public string GetTextContextInfo(dynamic? text, string column)
        {
            if (!SelectedTextContextColumns.Any() || text == null || string.IsNullOrWhiteSpace(column))
                return string.Empty;

            return column switch
            {
                "Key" => text?.Key ?? string.Empty,
                "Namespace" => text?.Namespace ?? string.Empty,
                _ => string.Empty
            };
        }

        #endregion

        #region Not Ekleme İşlemleri

        /// <summary>
        /// Seçili metne not ekler
        /// </summary>
        private async void AddNote()
        {
            if (SelectedText == null || string.IsNullOrWhiteSpace(NoteText))
                return;
            var oldNote = SelectedText.Note;
            try
            {

                SelectedText.Note = NoteText;

                await UpdateTextInGoogleSheetsAsync(SelectedText);



                _logService.Success($"Not başarıyla eklendi: {SelectedText.EN}", true, LogService.LogState.Translate);
                NoteText = string.Empty;
                NotifyRelatedCommands();

            }
            catch (Exception ex)
            {
                SelectedText.Note = oldNote;
                _logService.Error("Not eklenirken hata oluştu", ex, true, LogService.LogState.Translate);
            }
        }

        /// <summary>
        /// Filtrelenmiş listedeki tüm metinlere toplu not ekler
        /// </summary>
        private async void AddBulkNote()
        {
            if (!FilteredTextData.Any() || string.IsNullOrWhiteSpace(BulkNoteText))
                return;

            var textsToUpdate = FilteredTextData.ToList();
            var confirmMessage = $"Seçili {textsToUpdate.Count} satıra toplu not eklenecek. Bu işlem uzun sürebilir. Devam etmek istiyor musunuz?";

            // Burada gerçek bir onay dialog'u olmalı, şimdilik log ile uyarı veriyoruz
            _logService.Warning(confirmMessage, true, LogService.LogState.Translate);

            // Kullanıcı onayı simülasyonu - gerçek uygulamada dialog kullanılmalı
            await Task.Delay(2000); // 2 saniye bekle

            IsBulkNoteProcessing = true;
            BulkNoteProgress = 0;
            BulkNoteProgressText = "Toplu not ekleme başlatılıyor...";
            NotifyRelatedCommands();

            try
            {
                var cancellationTokenSource = new CancellationTokenSource();
                var cancellationToken = cancellationTokenSource.Token;

                _logService.Info($"Toplu not ekleme başlatıldı - {textsToUpdate.Count} metin", true, LogService.LogState.Translate);
                var selectedSheet = GoogleSheetsPanelVm.SelectedTextSheet;
                var spreadsheetId = GoogleSheetsPanelVm.SpreadsheetId;


                // Satır indeksini bul (RowNumber - 1, çünkü başlık satırı var)
                var existingData = await GoogleSheetsService.GetDataAsync(spreadsheetId, selectedSheet.SheetName);

                for (int i = 0; i < textsToUpdate.Count; i++)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    var text = textsToUpdate[i];
                    var oldNote = text.Note;
                    text.Note = BulkNoteText;

                    BulkNoteProgressText = $"İşleniyor: {i + 1}/{textsToUpdate.Count} - {text.EN.Substring(0, Math.Min(30, text.EN.Length))}...";
                    BulkNoteProgress = (double)(i + 1) / textsToUpdate.Count * 100;

                    try
                    {
                        await UpdateTextInGoogleSheetsAsync(text, existingData);

                    }
                    catch (Exception ex)
                    {
                        text.Note = oldNote; // Hata olursa eski değeri geri yükle
                        _logService.Warning($"Satır {text.ID} için not güncellenirken hata: {ex.Message}", false, LogService.LogState.Translate);
                    }

                    // Kısa bir bekleme ekle
                    await Task.Delay(100, cancellationToken);
                }

                _logService.Success("Toplu not ekleme tamamlandı", true, LogService.LogState.Translate);
                BulkNoteText = string.Empty;
            }
            catch (OperationCanceledException)
            {
                _logService.Warning("Toplu not ekleme iptal edildi", true, LogService.LogState.Translate);
            }
            catch (Exception ex)
            {
                _logService.Error("Toplu not ekleme sırasında hata oluştu", ex, true, LogService.LogState.Translate);
            }
            finally
            {
                IsBulkNoteProcessing = false;
                BulkNoteProgress = 0;
                BulkNoteProgressText = string.Empty;
                NotifyRelatedCommands();
            }
        }

        /// <summary>
        /// Toplu not ekleme işlemini iptal eder
        /// </summary>
        private void CancelBulkNote()
        {
            // Gerçek uygulamada CancellationToken iptal edilmeli
            _logService.Warning("Toplu not ekleme iptal ediliyor...", true, LogService.LogState.Translate);
        }

        /// <summary>
        /// Seçili metin değiştiğinde not alanını günceller
        /// </summary>
        partial void OnSelectedTextChanged(Text? value)
        {
            if (value != null)
            {
                NoteText = value.Note ?? string.Empty;
            }
            else
            {
                NoteText = string.Empty;
            }
            NotifyRelatedCommands();
        }

        /// <summary>
        /// Not metni değiştiğinde komutları günceller
        /// </summary>
        partial void OnNoteTextChanged(string value)
        {
            NotifyRelatedCommands();
        }

        /// <summary>
        /// Toplu not metni değiştiğinde komutları günceller
        /// </summary>
        partial void OnBulkNoteTextChanged(string value)
        {
            NotifyRelatedCommands();
        }
        #endregion

        [RelayCommand]
        private void ToggleTextContextColumn(string columnName)
        {
            if (SelectedTextContextColumns.Contains(columnName))
            {
                SelectedTextContextColumns.Remove(columnName);
            }
            else
            {
                SelectedTextContextColumns.Add(columnName);
            }
        }
    }
}