using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using Catalyst;
using Mosaik.Core;
using TranslationAgent.Features.Ceviri.Models;
using TranslationAgent.Features.Terminoloji.Models;

namespace TranslationAgent.Helpers
{
    /// <summary>
    /// Metin işleme için yardımcı fonksiyonlar
    /// </summary>
    public static class TextProcessingHelper
    {

        /// <summary>
        /// Verilen terimi temizler ve stopword'leri kaldırır
        /// </summary>
        /// <param name="term">Temizlenecek terim</param>
        /// <returns>Temizlenmiş terim</returns>
        /// 
        public static string TermCleanStopWords(string? term)
        {
            if (string.IsNullOrWhiteSpace(term))
                return string.Empty;
            // Stopword'leri temizle (a, an, the)

            string[] stopwords = { "a", "an", "the" };
            foreach (var stopword in stopwords)
            {
                // Kelimenin başında ve ardından bir boşlukla eşleşen stopword'leri temizle
                term = Regex.Replace(term, $"^{stopword}\\s+", "", RegexOptions.IgnoreCase);
            }

            return term;
        }

        /// <summary>
        /// Verilen metni lemmatization işleminden geçirir ve temizler
        /// </summary>
        /// <param name="text">İşlenecek metin</param>
        /// <param name="nlp">Catalyst Pipeline nesnesi</param>
        /// <returns>Lemmatized ve temizlenmiş metin</returns>
        /// 
        public static string ProcessTextToLemma(string? text, Pipeline nlp)
        {
            if (string.IsNullOrWhiteSpace(text))
                return string.Empty;

            try
            {

                // Metni küçük harfe çevir ve İngilizce locale kullan
                var sentenceDoc = new Document(text.ToLower(CultureInfo.GetCultureInfo("en-GB")), Language.English);

                // NLP işlemini uygula
                nlp.ProcessSingle(sentenceDoc);

                // Lemma'ları çıkar ve noktalama işaretlerini filtrele
                var lemmatizedText = string.Join(" ", sentenceDoc.SelectMany(s => s.Select(t => t.Lemma)))
                    .Where(c => !char.IsPunctuation(c))
                    .Aggregate(new StringBuilder(), (sb, c) => sb.Append(c))
                    .ToString();

                // Çoklu boşlukları tek boşluğa dönüştür ve trim et
                return Regex.Replace(lemmatizedText, @"\s+", " ").Trim();
            }
            catch (Exception)
            {
                // Hata durumunda orijinal metni döndür (temizlenmiş haliyle)
                return Regex.Replace(text.Trim(), @"\s+", " ");
            }
        }

        /// <summary>
        /// Verilen metni belirtilen uzunlukta kısaltır ve sonuna "..." ekler.
        /// </summary>
        /// <param name="text">Kısaltılacak metin.</param>
        /// <param name="maxLength">Maksimum uzunluk.</param>
        /// <returns>Kısaltılmış metin.</returns>
        public static string TruncateText(string? text, int maxLength)
        {
            if (string.IsNullOrWhiteSpace(text))
                return string.Empty;

            if (text.Length <= maxLength)
                return text;

            return text.Substring(0, maxLength) + "...";
        }

        /// <summary>
        /// Verilen örnekleri işler ve 500 kelimeden fazla olan metinleri lemmatize terim etrafında kısaltır
        /// </summary>
        /// <param name="examplesLemma">İşlenecek örnekler listesi</param>
        /// <param name="lemmatizeTerim">Aranacak lemmatize terim</param>
        /// <param name="nlp">Catalyst Pipeline nesnesi</param>
        /// <returns>İşlenmiş örnekler listesi</returns>
        public static List<dynamic>? ProcessExamplesWithLemma(List<Text>? texts, string lemmatizeTerim, Pipeline nlp)
        {

            var processedExamples = new List<dynamic>();

            foreach (var en in texts)
            {
                try
                {

                    // EN metninin kelime sayısını kontrol et
                    if (en.EN.Length > 500)
                    {
                        // NLP ile cümleleri ayır ve lemmatize terimi ara
                        var processedText = ProcessTextWithLemmaSearch(en.EN, lemmatizeTerim, nlp);

                        // Yeni nesne oluştur
                        processedExamples.Add(new { EN = processedText, Key = en.Key, Namespace = en.Namespace });
                    }
                    else
                    {
                        // Orijinal nesneyi koru
                        processedExamples.Add(new { EN = en.EN, Key = en.Key, Namespace = en.Namespace });
                    }
                }
                catch (Exception ex)
                {
                    // Hata durumunda orijinal nesneyi koru
                    processedExamples.Add(new { EN = en.EN, Key = en.Key, Namespace = en.Namespace });
                }
            }

            return processedExamples;
        }


        /// <summary>
        /// Metni NLP ile işler ve lemmatize terim etrafında 500 karaktere kısaltır
        /// </summary>
        /// <param name="text">İşlenecek metin</param>
        /// <param name="lemmatizeTerim">Aranacak lemmatize terim</param>
        /// <param name="nlp">Catalyst Pipeline nesnesi</param>
        /// <returns>Kısaltılmış metin</returns>
        private static string ProcessTextWithLemmaSearch(string text, string lemmatizeTerim, Pipeline nlp)
        {
            try
            {
                // Metni cümlelere ayır
                var doc = new Document(text, Language.English);
                nlp.ProcessSingle(doc);

                // Lemmatize terimi içeren cümleyi bul
                var targetSentence = string.Empty;
                var targetSentenceIndex = -1;
                var regexLemma = new Regex($@"\b{Regex.Escape(lemmatizeTerim)}\b", RegexOptions.IgnoreCase | RegexOptions.Compiled);
                for (int i = 0; i < doc.Length; i++)
                {
                    var sentence = doc[i];

                    var sentenceLemmas = string.Join(" ", sentence.Select(t => t.Lemma)).Where(c => !char.IsPunctuation(c))
                  .Aggregate(new StringBuilder(), (sb, c) => sb.Append(c))
                  .ToString();
                    sentenceLemmas = Regex.Replace(sentenceLemmas, @"\s+", " ").Trim();

                    if (regexLemma.IsMatch(sentenceLemmas))
                    {
                        targetSentence = sentence.Value;
                        targetSentenceIndex = i;
                        break;
                    }
                }

                // Eğer terim bulunamazsa, orijinal metni 500 karaktere kısalt
                if (targetSentenceIndex == -1)
                {
                    return TruncateToCharacterLimit(text, 500);
                }

                // Hedef cümle etrafında 500 karakterlik bölge oluştur
                return CreateContextAroundSentence(text, targetSentence, 500);
            }
            catch (Exception)
            {
                // Hata durumunda metni basit şekilde kısalt
                return TruncateToCharacterLimit(text, 500);
            }
        }

        /// <summary>
        /// Verilen cümle etrafında belirtilen karakter sayısında bağlam oluşturur
        /// </summary>
        /// <param name="fullText">Tam metin</param>
        /// <param name="targetSentence">Hedef cümle</param>
        /// <param name="maxLength">Maksimum karakter sayısı</param>
        /// <returns>Bağlam metni</returns>
        private static string CreateContextAroundSentence(string fullText, string targetSentence, int maxLength)
        {
            var sentenceIndex = fullText.IndexOf(targetSentence, StringComparison.OrdinalIgnoreCase);
            if (sentenceIndex == -1)
            {
                return TruncateToCharacterLimit(fullText, maxLength);
            }

            var sentenceLength = targetSentence.Length;
            var remainingChars = maxLength - sentenceLength;

            if (remainingChars <= 0)
            {
                // Cümle zaten çok uzunsa, cümleyi kısalt
                return "..." + targetSentence.Substring(0, maxLength - 6) + "...";
            }

            var beforeChars = remainingChars / 2;
            var afterChars = remainingChars - beforeChars;

            // Kelime sınırlarını bul
            var startIndex = FindNearestWordBoundary(fullText, Math.Max(0, sentenceIndex - beforeChars), false);
            var endIndex = FindNearestWordBoundary(fullText, Math.Min(fullText.Length, sentenceIndex + sentenceLength + afterChars), true);

            var result = fullText.Substring(startIndex, endIndex - startIndex);

            // Başına ve sonuna "..." ekle (eğer kısaltma yapıldıysa)
            if (startIndex > 0)
                result = "..." + result;
            if (endIndex < fullText.Length)
                result = result + "...";

            return result;
        }

        /// <summary>
        /// Metni belirtilen karakter sayısına kısaltır ve ortaya "..." ekler
        /// </summary>
        /// <param name="text">Kısaltılacak metin</param>
        /// <param name="maxLength">Maksimum karakter sayısı</param>
        /// <returns>Kısaltılmış metin</returns>
        private static string TruncateToCharacterLimit(string text, int maxLength)
        {
            if (text.Length <= maxLength)
                return text;

            var halfLength = (maxLength - 3) / 2; // 3 karakter "..." için

            // Kelime sınırlarını bul
            var startIndex = FindNearestWordBoundary(text, halfLength, false);
            var endIndex = FindNearestWordBoundary(text, text.Length - halfLength, true);

            return text.Substring(0, startIndex) + "..." + text.Substring(endIndex);
        }



        /// <summary>
        /// Verilen pozisyona en yakın kelime sınırını bulur
        /// </summary>
        /// <param name="text">Aranacak metin</param>
        /// <param name="position">Başlangıç pozisyonu</param>
        /// <param name="forward">İleriye doğru mu arama yapılacak</param>
        /// <returns>Kelim sınırı pozisyonu</returns>
        private static int FindNearestWordBoundary(string text, int position, bool forward)
        {
            if (position <= 0 || position >= text.Length)
                return position;

            var step = forward ? 1 : -1;

            while (position > 0 && position < text.Length && !char.IsWhiteSpace(text[position]))
            {
                position += step;
            }

            return position;
        }
    }
}
