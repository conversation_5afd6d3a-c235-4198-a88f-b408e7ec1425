using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using Avalonia.Threading;
using Catalyst;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Mosaik.Core;
using TranslationAgent.Features.Terminoloji.Models;
using TranslationAgent.Features.Baglam.Models;
using TranslationAgent.Features.Ceviri.Models;
using TranslationAgent.Services;
using Build5Nines.SharpVector;
using System.Text;
using TranslationAgent.Helpers;
using SmartComponents.LocalEmbeddings;
using DynamicData;
using System.Text.RegularExpressions;


namespace TranslationAgent.Features.GoogleSheets.ViewModels
{
    // GoogleSheetsPanelViewModel: Google Sheets panelinin ana ViewModel'i
    public partial class GoogleSheetsPanelViewModel : INotifyPropertyChanged
    {
        // Servisler
        private readonly GoogleSheetsService _sheetsService;
        private readonly LogService _logService;
        private readonly AppSettings _appSettings;
        private readonly Pipeline _nlp;
        public readonly LocalEmbedder _localEmbedder;
        // PropertyChanged event'i
        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
            => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));

        // Genel durum ve yetki
        public string SpreadsheetId { get; private set; }
        private bool _isLoading;
        public bool IsLoading
        {
            get => _isLoading;
            set { if (_isLoading != value) { _isLoading = value; OnPropertyChanged(); } }
        }
        private bool _isLoadingSpreadsheets;
        public bool IsLoadingSpreadsheets
        {
            get => _isLoadingSpreadsheets;
            set { if (_isLoadingSpreadsheets != value) { _isLoadingSpreadsheets = value; OnPropertyChanged(); } }
        }
        private bool _isLoadingSheets;
        public bool IsLoadingSheets
        {
            get => _isLoadingSheets;
            set { if (_isLoadingSheets != value) { _isLoadingSheets = value; OnPropertyChanged(); } }
        }
        private bool _canFetchData;
        public bool CanFetchData
        {
            get => _canFetchData;
            set { if (_canFetchData != value) { _canFetchData = value; OnPropertyChanged(); } }
        }
        private bool _canFilterData;
        public bool CanFilterData
        {
            get => _canFilterData;
            set { if (_canFilterData != value) { _canFilterData = value; OnPropertyChanged(); } }
        }
        private bool _canLoadSpreadsheets;
        public bool CanLoadSpreadsheets
        {
            get => _canLoadSpreadsheets;
            set { if (_canLoadSpreadsheets != value) { _canLoadSpreadsheets = value; OnPropertyChanged(); } }
        }

        // Erişilebilir e-tablolar ve seçili e-tablo
        public ObservableCollection<SpreadsheetInfo> AccessibleSpreadsheets { get; } = new();
        private SpreadsheetInfo? _selectedSpreadsheet;
        public SpreadsheetInfo? SelectedSpreadsheet
        {
            get => _selectedSpreadsheet;
            set { if (_selectedSpreadsheet != value) { _selectedSpreadsheet = value; OnPropertyChanged(); OnSelectedSpreadsheetChanged(value); } }
        }

        // Seçili e-tablo adını güvenli şekilde döndüren property
        public string SelectedSpreadsheetName => SelectedSpreadsheet?.Name ?? "E-Tablo seçilmedi";

        // Sayfa ve filtre ile ilgili property'ler
        public ObservableCollection<SheetHelper> AvailableSheets { get; } = new();
        private SheetHelper? _selectedSheet;
        public SheetHelper? SelectedSheet
        {
            get => _selectedSheet;
            set { if (_selectedSheet != value) { _selectedSheet = value; OnPropertyChanged(); OnSelectedSheetChanged(value); } }
        }
        public ObservableCollection<string> ColumnHeaders { get; } = new();
        public ObservableCollection<FilterEntry> FilterEntries { get; } = new();

        // Komutlar
        public IAsyncRelayCommand LoadAccessibleSpreadsheetsCommand { get; }
        public IAsyncRelayCommand ListSheetsCommand { get; }
        public IAsyncRelayCommand CreateTerminologySheetCommand { get; private set; }
        public IAsyncRelayCommand CreateContextSheetCommand { get; private set; }
        public IAsyncRelayCommand AutoFetchCommand { get; private set; }

        // Kimlik doğrulama
        private bool _isAuthenticated;
        public bool IsAuthenticated
        {
            get => _isAuthenticated;
            private set { if (_isAuthenticated != value) { _isAuthenticated = value; AuthenticationStatusChanged?.Invoke(this, value); } }
        }
        public event EventHandler<bool>? AuthenticationStatusChanged;

        // Ana bağlam bulunduğunda tetiklenen event
        public event EventHandler<Context>? MainContextFound;

        private Context? _mainContext;
        public Context? MainContext
        {
            get => _mainContext;
            set
            {
                if (_mainContext != value)
                {
                    _mainContext = value;
                    OnPropertyChanged();
                }
            }
        }

        // Terimce, Metin ve Bağlam veri yapıları
        public ObservableCollection<TermViewModel> TerminologyData { get; } = new();
        public ObservableCollection<Text> TextData { get; } = new();
        public ObservableCollection<Context> ContextData { get; } = new();

        public List<(Text c, EmbeddingF32 Value)>? textDataWithEmbeddings { get; set; }
        public List<(Context c, EmbeddingF32 Value)>? contextDataWithEmbeddings { get; set; }
        public List<(TermViewModel c, EmbeddingF32 Value)>? termDataWithEmbeddings { get; set; }
        public ObservableCollection<string> TerminologyColumnHeaders { get; } = new();
        public ObservableCollection<string> TextColumnHeaders { get; } = new();
        public ObservableCollection<string> ContextColumnHeaders { get; } = new();
        public ObservableCollection<FilterEntry> TerminologyFilterEntries { get; } = new();
        public ObservableCollection<FilterEntry> TextFilterEntries { get; } = new();
        public ObservableCollection<FilterEntry> ContextFilterEntries { get; } = new();
        private SheetHelper? _selectedTerminologySheet;
        public SheetHelper? SelectedTerminologySheet
        {
            get => _selectedTerminologySheet;
            set { if (_selectedTerminologySheet != value) { _selectedTerminologySheet = value; OnPropertyChanged(); OnSelectedTerminologySheetChanged(value); } }
        }
        private SheetHelper? _selectedTextSheet;
        public SheetHelper? SelectedTextSheet
        {
            get => _selectedTextSheet;
            set { if (_selectedTextSheet != value) { _selectedTextSheet = value; OnPropertyChanged(); OnSelectedTextSheetChanged(value); } }
        }
        private SheetHelper? _selectedContextSheet;
        public SheetHelper? SelectedContextSheet
        {
            get => _selectedContextSheet;
            set { if (_selectedContextSheet != value) { _selectedContextSheet = value; OnPropertyChanged(); OnSelectedContextSheetChanged(value); } }
        }
        public IAsyncRelayCommand FetchTerminologyDataCommand { get; }
        public IAsyncRelayCommand FetchTextDataCommand { get; }
        public IAsyncRelayCommand FetchContextDataCommand { get; }
        public IRelayCommand AddTerminologyFilterCommand { get; private set; }
        public IRelayCommand AddTextFilterCommand { get; private set; }
        public IRelayCommand AddContextFilterCommand { get; private set; }
        public IRelayCommand<FilterEntry> RemoveTerminologyFilterCommand { get; private set; }
        public IRelayCommand<FilterEntry> RemoveTextFilterCommand { get; private set; }
        public IRelayCommand<FilterEntry> RemoveContextFilterCommand { get; private set; }
        private bool _canCreateTerminologySheet;
        public bool CanCreateTerminologySheet
        {
            get => _canCreateTerminologySheet;
            set { if (_canCreateTerminologySheet != value) { _canCreateTerminologySheet = value; OnPropertyChanged(); } }
        }
        private bool _isTerminologyDataFetched;
        public bool IsTerminologyDataFetched
        {
            get => _isTerminologyDataFetched;
            set { _isTerminologyDataFetched = value; OnPropertyChanged(); }
        }
        private bool _isTextDataFetched;
        public bool IsTextDataFetched
        {
            get => _isTextDataFetched;
            set { _isTextDataFetched = value; OnPropertyChanged(); }
        }
        private bool _canFetchTerminologyData;
        public bool CanFetchTerminologyData
        {
            get => _canFetchTerminologyData;
            set { if (_canFetchTerminologyData != value) { _canFetchTerminologyData = value; OnPropertyChanged(); } }
        }
        private bool _canFetchTextData;
        public bool CanFetchTextData
        {
            get => _canFetchTextData;
            set { if (_canFetchTextData != value) { _canFetchTextData = value; OnPropertyChanged(); } }
        }
        private bool _canFilterTerminologyData;
        public bool CanFilterTerminologyData
        {
            get => _canFilterTerminologyData;
            set { if (_canFilterTerminologyData != value) { _canFilterTerminologyData = value; OnPropertyChanged(); } }
        }
        private bool _canFilterTextData;
        public bool CanFilterTextData
        {
            get => _canFilterTextData;
            set { if (_canFilterTextData != value) { _canFilterTextData = value; OnPropertyChanged(); } }
        }
        private bool _canCreateContextSheet;
        public bool CanCreateContextSheet
        {
            get => _canCreateContextSheet;
            set { if (_canCreateContextSheet != value) { _canCreateContextSheet = value; OnPropertyChanged(); } }
        }
        private bool _isContextDataFetched;
        public bool IsContextDataFetched
        {
            get => _isContextDataFetched;
            set { _isContextDataFetched = value; OnPropertyChanged(); }
        }
        private bool _canFetchContextData;
        public bool CanFetchContextData
        {
            get => _canFetchContextData;
            set { if (_canFetchContextData != value) { _canFetchContextData = value; OnPropertyChanged(); } }
        }
        private bool _canFilterContextData;
        public bool CanFilterContextData
        {
            get => _canFilterContextData;
            set { if (_canFilterContextData != value) { _canFilterContextData = value; OnPropertyChanged(); } }
        }

        // Expander durumları
        private bool _isTerminologyExpanderExpanded;
        public bool IsTerminologyExpanderExpanded
        {
            get => _isTerminologyExpanderExpanded;
            set { if (_isTerminologyExpanderExpanded != value) { _isTerminologyExpanderExpanded = value; OnPropertyChanged(); } }
        }

        private bool _isContextExpanderExpanded;
        public bool IsContextExpanderExpanded
        {
            get => _isContextExpanderExpanded;
            set { if (_isContextExpanderExpanded != value) { _isContextExpanderExpanded = value; OnPropertyChanged(); } }
        }

        private bool _isTextExpanderExpanded;
        public bool IsTextExpanderExpanded
        {
            get => _isTextExpanderExpanded;
            set { if (_isTextExpanderExpanded != value) { _isTextExpanderExpanded = value; OnPropertyChanged(); } }
        }

        // Constructor
        public GoogleSheetsPanelViewModel(GoogleSheetsService sheetsService, LogService logService, AppSettings appSettings, Pipeline nlp, LocalEmbedder localEmbedder)
        {
            _sheetsService = sheetsService;
            _logService = logService;
            _appSettings = appSettings;
            _nlp = nlp;
            _localEmbedder = localEmbedder;
            SpreadsheetId = string.Empty;
            IsLoading = false;
            IsLoadingSpreadsheets = false;
            IsLoadingSheets = false;
            LoadAccessibleSpreadsheetsCommand = new AsyncRelayCommand(LoadAccessibleSpreadsheetsAsync, () => _sheetsService.IsAuthenticated && !IsLoadingSpreadsheets);
            ListSheetsCommand = new AsyncRelayCommand(ListSheetsAsync, () => SelectedSpreadsheet != null && _sheetsService.IsAuthenticated && !IsLoadingSheets);
            CreateTerminologySheetCommand = new AsyncRelayCommand(CreateTerminologySheetAsync, () => SelectedSpreadsheet != null && _sheetsService.IsAuthenticated && !IsLoading && CanCreateTerminologySheet);
            CreateContextSheetCommand = new AsyncRelayCommand(CreateContextSheetAsync, () => SelectedSpreadsheet != null && _sheetsService.IsAuthenticated && !IsLoading && CanCreateContextSheet);
            AutoFetchCommand = new AsyncRelayCommand(AutoFetchAsync, () => SelectedSpreadsheet != null && _sheetsService.IsAuthenticated && !IsLoading);
            FetchTerminologyDataCommand = new AsyncRelayCommand(() => FetchOrFilterDataAsync(SelectedTerminologySheet, TerminologyFilterEntries, TerminologyData), () => SelectedTerminologySheet != null && _sheetsService.IsAuthenticated && !IsLoading);
            FetchTextDataCommand = new AsyncRelayCommand(() => FetchOrFilterTextDataAsync(SelectedTextSheet, TextFilterEntries, TextData), () => SelectedTextSheet != null && _sheetsService.IsAuthenticated && !IsLoading);
            FetchContextDataCommand = new AsyncRelayCommand(() => FetchOrFilterContextDataAsync(SelectedContextSheet, ContextFilterEntries, ContextData), () => SelectedContextSheet != null && _sheetsService.IsAuthenticated && !IsLoading);
            AddTerminologyFilterCommand = new RelayCommand(() => AddFilter(TerminologyFilterEntries, TerminologyColumnHeaders));
            AddTextFilterCommand = new RelayCommand(() => AddFilter(TextFilterEntries, TextColumnHeaders));
            AddContextFilterCommand = new RelayCommand(() => AddFilter(ContextFilterEntries, ContextColumnHeaders));
            RemoveTerminologyFilterCommand = new RelayCommand<FilterEntry>(filter => RemoveFilter(filter, TerminologyFilterEntries));
            RemoveTextFilterCommand = new RelayCommand<FilterEntry>(filter => RemoveFilter(filter, TextFilterEntries));
            RemoveContextFilterCommand = new RelayCommand<FilterEntry>(filter => RemoveFilter(filter, ContextFilterEntries));
            PropertyChanged += (s, e) =>
            {
                if (
                    e.PropertyName == nameof(IsLoading) ||
                    e.PropertyName == nameof(IsLoadingSpreadsheets) ||
                    e.PropertyName == nameof(IsLoadingSheets) ||
                    e.PropertyName == nameof(SelectedSheet) ||
                    e.PropertyName == nameof(SelectedSpreadsheet) ||
                    e.PropertyName == nameof(SelectedTerminologySheet) ||
                    e.PropertyName == nameof(SelectedTextSheet) ||
                    e.PropertyName == nameof(SelectedContextSheet) ||
                    e.PropertyName == nameof(CanCreateTerminologySheet) ||
                    e.PropertyName == nameof(CanCreateContextSheet)
                )
                {
                    Dispatcher.UIThread.Post(() =>
                    {
                        if (e.PropertyName == nameof(IsLoadingSpreadsheets))
                        {
                            CanLoadSpreadsheets = _sheetsService.IsAuthenticated && !IsLoadingSpreadsheets;
                        }

                        LoadAccessibleSpreadsheetsCommand.NotifyCanExecuteChanged();
                        ListSheetsCommand.NotifyCanExecuteChanged();
                        FetchTerminologyDataCommand.NotifyCanExecuteChanged();
                        FetchTextDataCommand.NotifyCanExecuteChanged();
                        FetchContextDataCommand.NotifyCanExecuteChanged();
                        CreateTerminologySheetCommand.NotifyCanExecuteChanged();
                        CreateContextSheetCommand.NotifyCanExecuteChanged();
                        AutoFetchCommand.NotifyCanExecuteChanged();
                    });
                }
            };

            _sheetsService.AuthenticationStatusChanged += (sender, isAuthenticated) =>
            {
                Dispatcher.UIThread.Post(() =>
                {
                    if (isAuthenticated)
                    {
                        _logService.Info("E-tablo listesi almaya hazır. Event");
                        IsLoading = false;
                        IsLoadingSpreadsheets = false;
                    }
                    else
                    {
                        _logService.Info("Lütfen ayarlardan Google Sheets servis hesabını yapılandırın.");
                    }

                    CanLoadSpreadsheets = isAuthenticated && !IsLoadingSpreadsheets;
                    LoadAccessibleSpreadsheetsCommand.NotifyCanExecuteChanged();
                });
            };

            Dispatcher.UIThread.Post(() =>
            {
                CanLoadSpreadsheets = _sheetsService.IsAuthenticated && !IsLoadingSpreadsheets;
                if (!_sheetsService.IsAuthenticated)
                {
                    _logService.Info("Lütfen ayarlardan Google Sheets servis hesabını yapılandırın.");
                }
                else
                {
                    _logService.Info("E-tablo listesi almaya hazır.");
                }

                LoadAccessibleSpreadsheetsCommand.NotifyCanExecuteChanged();
            });
        }

        // E-tablo seçildiğinde yapılacak işlemler
        private void OnSelectedSpreadsheetChanged(SpreadsheetInfo? value)
        {
            SpreadsheetId = value?.Id ?? string.Empty;

            AvailableSheets.Clear();
            SelectedTerminologySheet = null;
            SelectedTextSheet = null;
            SelectedContextSheet = null;
            TerminologyColumnHeaders.Clear();
            TextColumnHeaders.Clear();
            ContextColumnHeaders.Clear();
            TerminologyFilterEntries.Clear();
            TextFilterEntries.Clear();
            ContextFilterEntries.Clear();
            TerminologyData.Clear();
            TextData.Clear();
            ContextData.Clear();
            CanFetchTerminologyData = false;
            IsTerminologyDataFetched = false;
            CanFetchTextData = false;
            IsTextDataFetched = false;
            CanFetchContextData = false;
            IsContextDataFetched = false;
            CanFilterTerminologyData = false;
            CanFilterTextData = false;
            CanFilterContextData = false;
            CanCreateTerminologySheet = false;
            CanCreateContextSheet = false;

            if (value != null)
            {
                _logService.UpdateProjectName(value.Name);
                _logService.Info($"'{value.Name}' e-tablosu seçildi");
                _logService.Info($"E-tablo '{value.Name}' seçildi. Sayfalar yükleniyor...");
                if (ListSheetsCommand.CanExecute(null))
                {
                    ListSheetsCommand.Execute(null);
                }
            }
            else
            {

                _logService.Info("E-tablo seçimi kaldırıldı");
                _logService.Info("E-tablo seçilmedi.");
            }
            ListSheetsCommand.NotifyCanExecuteChanged();
        }

        // Sayfa seçildiğinde yapılacak işlemler
        private void OnSelectedSheetChanged(SheetHelper? value)
        {
            CanFetchData = value != null;
            CanFilterData = value != null;
            if (value != null && SelectedSpreadsheet != null)
            {
                _logService.Info($"'{value.SheetName}' sayfası seçildi");
                _ = LoadColumnHeadersAsync(value.SheetName, ColumnHeaders);
            }
            else
            {
                _logService.Info("Sayfa seçimi kaldırıldı");
                ColumnHeaders.Clear();
                FilterEntries.Clear();
                CanFilterData = false;
            }
        }

        // Terimce sayfası seçildiğinde yapılacak işlemler
        private void OnSelectedTerminologySheetChanged(SheetHelper? value)
        {
            CanFetchTerminologyData = value != null;
            CanFilterTerminologyData = value != null;
            if (value != null && SelectedSpreadsheet != null)
            {
                _logService.Info($"Terimler için '{value.SheetName}' sayfası seçildi");
                _ = LoadColumnHeadersAsync(value.SheetName, TerminologyColumnHeaders);
            }
            else
            {
                _logService.Info("Terimler sayfası seçimi kaldırıldı");
                TerminologyColumnHeaders.Clear();
                TerminologyFilterEntries.Clear();
                CanFilterTerminologyData = false;
                IsTerminologyDataFetched = false;

            }
        }

        // Metin sayfası seçildiğinde yapılacak işlemler
        private void OnSelectedTextSheetChanged(SheetHelper? value)
        {
            CanFetchTextData = value != null;
            CanFilterTextData = value != null;
            if (value != null && SelectedSpreadsheet != null)
            {
                _logService.Info($"Metinler için '{value.SheetName}' sayfası seçildi");
                _ = LoadColumnHeadersAsync(value.SheetName, TextColumnHeaders);
            }
            else
            {
                _logService.Info("Metinler sayfası seçimi kaldırıldı");
                TextColumnHeaders.Clear();
                TextFilterEntries.Clear();
                CanFilterTextData = false;
                IsTextDataFetched = false;
            }
        }

        // Bağlam sayfası seçildiğinde yapılacak işlemler
        private void OnSelectedContextSheetChanged(SheetHelper? value)
        {
            CanFetchContextData = value != null;
            CanFilterContextData = value != null;
            if (value != null && SelectedSpreadsheet != null)
            {
                _logService.Info($"Bağlamlar için '{value.SheetName}' sayfası seçildi");
                _ = LoadColumnHeadersAsync(value.SheetName, ContextColumnHeaders);
            }
            else
            {
                _logService.Info("Bağlamlar sayfası seçimi kaldırıldı");
                ContextColumnHeaders.Clear();
                ContextFilterEntries.Clear();
                CanFilterContextData = false;
                IsContextDataFetched = false;
            }
        }

        // Erişilebilir e-tabloları yükler
        private async Task LoadAccessibleSpreadsheetsAsync()
        {
            if (!_sheetsService.IsAuthenticated)
            {
                _logService.Error("Kimlik doğrulama yapılmamış. Lütfen Ayarlar'dan Servis Hesabını yapılandırın");
                return;
            }

            IsLoading = true;
            IsLoadingSpreadsheets = true;
            _logService.Info("Erişilebilir e-tablolar listesi alınıyor");

            AccessibleSpreadsheets.Clear();
            AvailableSheets.Clear();
            SelectedSheet = null;
            FilterEntries.Clear();
            TerminologyData.Clear();
            TextData.Clear();
            TerminologyColumnHeaders.Clear();
            TextColumnHeaders.Clear();
            TerminologyFilterEntries.Clear();
            TextFilterEntries.Clear();
            IsTerminologyDataFetched = false;
            IsTextDataFetched = false;
            CanFetchTerminologyData = false;
            CanFetchTextData = false;
            CanFilterTerminologyData = false;
            CanFilterTextData = false;
            CanCreateTerminologySheet = false;
            SelectedTerminologySheet = null;
            SelectedTextSheet = null;
            SelectedSpreadsheet = null;


            try
            {
                var spreadsheets = await _sheetsService.GetAccessibleSpreadsheetsAsync();

                if (spreadsheets != null)
                {
                    foreach (var sp in spreadsheets)
                    {
                        AccessibleSpreadsheets.Add(new SpreadsheetInfo(sp.Name, sp.Id));
                    }
                    _logService.Info($"{AccessibleSpreadsheets.Count} adet erişilebilir e-tablo bulundu");
                    _logService.Info($"{AccessibleSpreadsheets.Count} adet erişilebilir e-tablo bulundu. Birini seçin.");
                    if (!AccessibleSpreadsheets.Any())
                    {
                        _logService.Warning("Erişilebilir e-tablo bulunamadı");
                        _logService.Info("Erişilebilir e-tablo bulunamadı.");
                    }
                }
                else
                {
                    _logService.Error("E-tablolar alınamadı veya bir hata oluştu");
                    _logService.Info("E-tablolar alınamadı veya bir hata oluştu.");
                }
            }
            catch (Exception ex)
            {
                _logService.Error($"Erişilebilir e-tabloları listelerken hata: {ex.Message}");
            }
            finally
            {
                IsLoadingSpreadsheets = false;
                IsLoading = false;
            }
        }

        // Seçili e-tablonun sayfalarını yükler
        private async Task ListSheetsAsync()
        {
            if (!_sheetsService.IsAuthenticated)
            {
                _logService.Error("Kimlik doğrulama yapılmamış. Lütfen Ayarlar'dan Servis Hesabını yapılandırın");
                return;
            }

            if (string.IsNullOrWhiteSpace(SpreadsheetId))
            {
                _logService.Error("Sayfa listesi alınamadı: E-tablo seçilmemiş");
                return;
            }

            IsLoading = true;
            IsLoadingSheets = true;
            _logService.Info("Sayfa listesi alınıyor");
            AvailableSheets.Clear();

            try
            {
                var sheets = await _sheetsService.GetSheetsAsync(SpreadsheetId);
                if (sheets != null)
                {
                    foreach (var sheet in sheets)
                    {
                        AvailableSheets.Add(
                            new SheetHelper(
                                sheet.Properties.Title,
                                sheet.Properties.SheetId
                            )
                        );
                    }

                    // Terimce ve Bağlam sayfalarının varlığını kontrol et
                    var hasTerminologySheet = AvailableSheets.Any(s => s.SheetName == "Terimce");
                    var hasContextSheet = AvailableSheets.Any(s => s.SheetName == "Bağlamlar");
                    CanCreateTerminologySheet = !hasTerminologySheet;
                    CanCreateContextSheet = !hasContextSheet;
                    CreateTerminologySheetCommand.NotifyCanExecuteChanged();
                    CreateContextSheetCommand.NotifyCanExecuteChanged();

                    _logService.Info($"'{SelectedSpreadsheet?.Name}' e-tablosunda {AvailableSheets.Count} sayfa bulundu");
                    _logService.Info($"'{SelectedSpreadsheet?.Name}' e-tablosunda {AvailableSheets.Count} sayfa bulundu. Devam etmek için birini seçin.");
                    if (!AvailableSheets.Any())
                    {
                        _logService.Warning($"'{SelectedSpreadsheet?.Name}' e-tablosunda sayfa bulunamadı");
                        _logService.Info($"'{SelectedSpreadsheet?.Name}' e-tablosunda sayfa bulunamadı.");
                    }
                }
                else
                {
                    _logService.Error("Sayfalar alınamadı veya bir hata oluştu");
                    _logService.Info("Sayfalar alınamadı veya bir hata oluştu.");
                }
            }
            catch (Exception ex)
            {
                _logService.Error($"Sayfa listelerken hata: {ex.Message}");
            }
            finally
            {
                IsLoadingSheets = false;
                IsLoading = false;
            }
        }

        // Belirli bir sayfanın başlıklarını yükler
        private async Task LoadColumnHeadersAsync(string? sheetName, ObservableCollection<string> columnHeaders)
        {
            columnHeaders.Clear();

            if (
                string.IsNullOrWhiteSpace(sheetName)
                || !_sheetsService.IsAuthenticated
                || string.IsNullOrWhiteSpace(SpreadsheetId)
            )
            {
                _logService.Error("Başlıklar yüklenemedi: Geçersiz parametreler");
                return;
            }

            IsLoading = true;
            _logService.Info($"'{sheetName}' sayfası için başlıklar yükleniyor");

            try
            {
                var headerData = await _sheetsService.GetDataAsync(SpreadsheetId, sheetName, "1:1");
                if (headerData != null && headerData.Any())
                {
                    foreach (var headerCell in headerData.First())
                    {
                        var headerText = headerCell?.ToString() ?? string.Empty;
                        columnHeaders.Add(headerText);
                    }
                    _logService.Info($"'{sheetName}' sayfası için başlıklar başarıyla yüklendi");
                    _logService.Info($"{sheetName} için başlıklar yüklendi. Şimdi filtre tanımlayabilir veya veri çekebilirsiniz.");
                }
                else
                {
                    _logService.Warning($"'{sheetName}' sayfası için başlıklar yüklenemedi: Sayfa veya ilk satır boş olabilir");
                    _logService.Info("Başlıklar yüklenemedi. Sayfa boş veya ilk satır boş olabilir.");
                }
            }
            catch (Exception ex)
            {
                _logService.Error($"Başlıkları yüklerken hata: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        // Filtre ekler
        private void AddFilter(ObservableCollection<FilterEntry> filterEntries, ObservableCollection<string> columnHeaders)
        {
            if (columnHeaders.Any())
            {
                filterEntries.Add(new FilterEntry { ColumnName = columnHeaders[0] });
            }
        }

        // Filtreyi kaldırır
        private void RemoveFilter(FilterEntry? filter, ObservableCollection<FilterEntry> filterEntries)
        {
            if (filter != null)
            {
                filterEntries.Remove(filter);
            }
        }

        // Sadece terimler için (TermViewModel)
        private async Task FetchOrFilterDataAsync(SheetHelper? selectedSheet, ObservableCollection<FilterEntry> filterEntries, ObservableCollection<TermViewModel> targetData)
        {
            if (
                selectedSheet == null
                || string.IsNullOrWhiteSpace(selectedSheet.SheetName)
                || !_sheetsService.IsAuthenticated
                || string.IsNullOrWhiteSpace(SpreadsheetId)
            )
            {
                _logService.Error("Veri çekilemedi: Sayfa seçilmemiş veya kimlik doğrulama yapılmamış");
                return;
            }

            IsLoading = true;
            _logService.Info($"'{selectedSheet.SheetName}' sayfasından veriler çekiliyor");
            targetData.Clear();

            try
            {
                var filters = filterEntries
                    .Where(f => !string.IsNullOrWhiteSpace(f.FilterValue))
                    .Select(f => new
                    {
                        ColumnName = f.ColumnName,
                        Value = f.FilterValue,
                        Operator = f.SelectedOperator,
                        Type = f.SelectedType
                    })
                    .ToList();

                if (filters.Any())
                {
                    _logService.Info($"Filtreler uygulanıyor: {string.Join(", ", filters.Select(f => $"{f.ColumnName} {f.Operator} {f.Value}"))}");
                }

                var data = await _sheetsService.GetDataAsync(SpreadsheetId, selectedSheet.SheetName);
                if (data != null && data.Any())
                {
                    var headers = data.First().Select(h => h?.ToString() ?? string.Empty).ToList();

                    // Sütun başlıklarını doğrula
                    var validation = TermViewModel.ValidateHeaders(headers);
                    if (!validation.IsValid)
                    {
                        _logService.Error($"Geçersiz sütun yapısı: {validation.ErrorMessage}");
                        return;
                    }

                    var records = new List<TermViewModel>();

                    for (int i = 1; i < data.Count; i++)
                    {
                        var row = data[i];
                        var record = new TermViewModel();
                        bool matchesFilters = true;
                        if (headers.Contains("#") && headers.IndexOf("#") < row.Count)
                        {
                            if (int.TryParse(row[headers.IndexOf("#")]?.ToString(), out int id))
                            {
                                record.ID = id;
                            }
                        }
                        // Property set

                        record.EN = headers.Contains("EN") && headers.IndexOf("EN") < row.Count ? row[headers.IndexOf("EN")]?.ToString() ?? string.Empty : string.Empty;
                        record.TR = headers.Contains("TR") && headers.IndexOf("TR") < row.Count ? row[headers.IndexOf("TR")]?.ToString() ?? string.Empty : string.Empty;
                        record.Kategori = headers.Contains("Kategori") && headers.IndexOf("Kategori") < row.Count ? row[headers.IndexOf("Kategori")]?.ToString() ?? string.Empty : string.Empty;
                        record.Bilgi = headers.Contains("Bilgi") && headers.IndexOf("Bilgi") < row.Count ? row[headers.IndexOf("Bilgi")]?.ToString() ?? string.Empty : string.Empty;
                        record.Status = headers.Contains("Durum") && headers.IndexOf("Durum") < row.Count && Enum.TryParse(row[headers.IndexOf("Durum")]?.ToString(), out TermStatus status) ? status : TermStatus.İncelemede;

                        // Filtre uygula
                        foreach (var filter in filters)
                        {
                            string stringValue = filter.ColumnName switch
                            {
                                "EN" => record.EN,
                                "TR" => record.TR,
                                "Kategori" => record.Kategori,
                                "Bilgi" => record.Bilgi,
                                "#" => record.ID.ToString(),
                                _ => string.Empty
                            };
                            var filterValue = filter.Value;
                            bool matches = filter.Type switch
                            {
                                "Number" when double.TryParse(stringValue, out double numValue) && double.TryParse(filterValue, out double filterNumValue) =>
                                    filter.Operator switch
                                    {
                                        "=" => numValue == filterNumValue,
                                        "!=" => numValue != filterNumValue,
                                        ">" => numValue > filterNumValue,
                                        ">=" => numValue >= filterNumValue,
                                        "<" => numValue < filterNumValue,
                                        "<=" => numValue <= filterNumValue,
                                        _ => false
                                    },
                                "Boolean" when bool.TryParse(stringValue, out bool boolValue) && bool.TryParse(filterValue, out bool filterBoolValue) =>
                                    filter.Operator switch
                                    {
                                        "=" => boolValue == filterBoolValue,
                                        "!=" => boolValue != filterBoolValue,
                                        _ => false
                                    },
                                "String" =>
                                    filter.Operator switch
                                    {
                                        "=" => stringValue.Equals(filterValue, StringComparison.OrdinalIgnoreCase),
                                        "!=" => !stringValue.Equals(filterValue, StringComparison.OrdinalIgnoreCase),
                                        "Contains" => stringValue.Contains(filterValue, StringComparison.OrdinalIgnoreCase),
                                        "Not Contains" => !stringValue.Contains(filterValue, StringComparison.OrdinalIgnoreCase),
                                        "Regex" => System.Text.RegularExpressions.Regex.IsMatch(stringValue, filterValue),
                                        "Not Regex" => !System.Text.RegularExpressions.Regex.IsMatch(stringValue, filterValue),
                                        _ => false
                                    },
                                _ => stringValue.Equals(filterValue, StringComparison.OrdinalIgnoreCase)
                            };
                            if (!matches)
                            {
                                matchesFilters = false;
                                break;
                            }
                        }
                        if (matchesFilters)
                        {
                            records.Add(record);
                        }
                    }

                    targetData.AddRange(records);
                    if (targetData.Any())
                    {
                        var tasks = targetData.Select(async context =>
                        {
                            context.Lemma = await Task.Run(() => TextProcessingHelper.ProcessTextToLemma(TextProcessingHelper.TermCleanStopWords(context.EN), _nlp));
                            context.Vector = await Task.Run(() => _localEmbedder.Embed(context.EN));
                        });
                        await Task.WhenAll(tasks);
                    }

                    IsTerminologyDataFetched = true;
                    _logService.Info($"{selectedSheet.SheetName} sayfasından {targetData.Count} terim çekildi.");
                    _logService.Info($"'{selectedSheet.SheetName}' sayfasından {targetData.Count} satır veri çekildi");
                }
                else
                {
                    _logService.Warning($"'{selectedSheet.SheetName}' sayfasından veri alınamadı veya boş");
                }
            }
            catch (Exception ex)
            {
                _logService.Error($"Veri çekerken hata: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        // Metinler için (yeni TextData modeli ile)
        private async Task FetchOrFilterTextDataAsync(SheetHelper? selectedSheet, ObservableCollection<FilterEntry> filterEntries, ObservableCollection<Text> targetData)
        {
            if (
                selectedSheet == null
                || string.IsNullOrWhiteSpace(selectedSheet.SheetName)
                || !_sheetsService.IsAuthenticated
                || string.IsNullOrWhiteSpace(SpreadsheetId)
            )
            {
                _logService.Error("Veri çekilemedi: Sayfa seçilmemiş veya kimlik doğrulama yapılmamış");
                return;
            }

            IsLoading = true;
            _logService.Info($"'{selectedSheet.SheetName}' sayfasından çeviri verileri çekiliyor");
            targetData.Clear();

            try
            {
                var data = await _sheetsService.GetDataAsync(SpreadsheetId, selectedSheet.SheetName);
                if (data != null && data.Any())
                {
                    var headers = data.First().Select(h => h?.ToString() ?? string.Empty).ToList();

                    // Sütun başlıklarını validate et
                    var validation = Text.ValidateHeaders(headers);
                    if (!validation.IsValid)
                    {
                        _logService.Error($"Geçersiz sütun yapısı: {validation.ErrorMessage}");
                        return;
                    }

                    _logService.Info($"Sütun yapısı geçerli. Namespace: {validation.HasNamespace}, Key: {validation.HasKey}, Note: {validation.HasNote}");

                    var records = new List<Text>();

                    // Filtreleri hazırla
                    var filters = filterEntries
                        .Where(f => !string.IsNullOrWhiteSpace(f.FilterValue))
                        .Select(f => new
                        {
                            ColumnName = f.ColumnName,
                            Value = f.FilterValue,
                            Operator = f.SelectedOperator,
                            Type = f.SelectedType
                        })
                        .ToList();

                    if (filters.Any())
                    {
                        _logService.Info($"Filtreler uygulanıyor: {string.Join(", ", filters.Select(f => $"{f.ColumnName} {f.Operator} {f.Value}"))}");
                    }

                    // Verileri işle
                    for (int i = 1; i < data.Count; i++)
                    {
                        var row = data[i];
                        var textData = Text.FromSheetRow(headers, row, i);

                        // Filtreleri uygula
                        bool matchesFilters = true;
                        foreach (var filter in filters)
                        {
                            string stringValue = filter.ColumnName.ToUpperInvariant() switch
                            {
                                "#" => textData.ID.ToString(),
                                "EN" => textData.EN,
                                "TR" => textData.TR,
                                "EN-TR" => textData.StatusText,
                                "NAMESPACE" => textData.Namespace ?? string.Empty,
                                "KEY" => textData.Key ?? string.Empty,
                                "NOT" => textData.Note ?? string.Empty,
                                _ => string.Empty
                            };

                            var filterValue = filter.Value;
                            bool matches = filter.Type switch
                            {
                                "Number" when double.TryParse(stringValue, out double numValue) && double.TryParse(filterValue, out double filterNumValue) =>
                                    filter.Operator switch
                                    {
                                        "=" => numValue == filterNumValue,
                                        "!=" => numValue != filterNumValue,
                                        ">" => numValue > filterNumValue,
                                        ">=" => numValue >= filterNumValue,
                                        "<" => numValue < filterNumValue,
                                        "<=" => numValue <= filterNumValue,
                                        _ => false
                                    },
                                "String" =>
                                    filter.Operator switch
                                    {
                                        "=" => stringValue.Equals(filterValue, StringComparison.OrdinalIgnoreCase),
                                        "!=" => !stringValue.Equals(filterValue, StringComparison.OrdinalIgnoreCase),
                                        "Contains" => stringValue.Contains(filterValue, StringComparison.OrdinalIgnoreCase),
                                        "Not Contains" => !stringValue.Contains(filterValue, StringComparison.OrdinalIgnoreCase),
                                        "Regex" => System.Text.RegularExpressions.Regex.IsMatch(stringValue, filterValue),
                                        "Not Regex" => !System.Text.RegularExpressions.Regex.IsMatch(stringValue, filterValue),
                                        _ => false
                                    },
                                _ => stringValue.Equals(filterValue, StringComparison.OrdinalIgnoreCase)
                            };

                            if (!matches)
                            {
                                matchesFilters = false;
                                break;
                            }
                        }

                        if (matchesFilters)
                        {
                            records.Add(textData);
                        }
                    }

                    // Verileri collection'a ekle
                    foreach (var record in records)
                    {
                        targetData.Add(record);
                    }

                    // Lemmatization işlemi
                    var tasks = targetData.Select(async textData =>
                    {
                        textData.Lemma = await Task.Run(() => TextProcessingHelper.ProcessTextToLemma(textData.EN, _nlp));
                        if (textData.IsTranslated)
                        {
                            textData.Vector = await Task.Run(() => _localEmbedder.Embed(textData.EN));
                        }

                    });
                    await Task.WhenAll(tasks);

                    IsTextDataFetched = true;
                    _logService.Info($"{selectedSheet.SheetName} sayfasından {targetData.Count} çeviri verisi çekildi.");

                    // Çeviri durumu istatistikleri
                    var statusCounts = targetData.GroupBy(t => t.Status).ToDictionary(g => g.Key, g => g.Count());
                    var statusInfo = string.Join(", ", statusCounts.Select(kvp => $"{kvp.Key}: {kvp.Value}"));
                    _logService.Info($"Çeviri durumu dağılımı: {statusInfo}");
                }
                else
                {
                    _logService.Warning($"'{selectedSheet.SheetName}' sayfasından veri alınamadı veya boş");
                }
            }
            catch (Exception ex)
            {
                _logService.Error($"Çeviri verileri çekerken hata: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        // Terimce sayfası oluşturur
        private async Task CreateTerminologySheetAsync()
        {
            if (!_sheetsService.IsAuthenticated || string.IsNullOrWhiteSpace(SpreadsheetId))
            {
                return;
            }

            IsLoading = true;
            _logService.Info("Terimce sayfası oluşturuluyor");

            try
            {
                var headers = new List<object> { "#", "EN", "TR", "Kategori", "Bilgi", "Durum" };
                await _sheetsService.CreateSheetAsync(SpreadsheetId, "Terimce", headers);

                _logService.Success("Terimce sayfası başarıyla oluşturuldu.");

                // Sayfa listesini güncelle
                await ListSheetsCommand.ExecuteAsync(null);

                // Yeni oluşturulan Terimce sayfasını seç
                var terminologySheet = AvailableSheets.FirstOrDefault(s => s.SheetName == "Terimce");
                if (terminologySheet != null)
                {
                    SelectedTerminologySheet = terminologySheet;
                    _logService.Info("Terimce sayfası otomatik olarak seçildi");
                }
            }
            catch (Exception ex)
            {
                _logService.Error($"Terimce sayfası oluşturulurken hata: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        // Bağlam sayfası oluşturur
        private async Task CreateContextSheetAsync()
        {
            if (!_sheetsService.IsAuthenticated || string.IsNullOrWhiteSpace(SpreadsheetId))
            {
                return;
            }

            IsLoading = true;
            _logService.Info("Bağlamlar sayfası oluşturuluyor");

            try
            {
                var headers = new List<object> { "#", "Kategori", "Alt Başlık", "İçerik", "URL", "Keywords" };
                await _sheetsService.CreateSheetAsync(SpreadsheetId, "Bağlamlar", headers);

                _logService.Success("Bağlamlar sayfası başarıyla oluşturuldu.");

                // Sayfa listesini güncelle
                await ListSheetsCommand.ExecuteAsync(null);

                // Yeni oluşturulan Bağlamlar sayfasını seç
                var contextSheet = AvailableSheets.FirstOrDefault(s => s.SheetName == "Bağlamlar");
                if (contextSheet != null)
                {
                    SelectedContextSheet = contextSheet;
                    _logService.Info("Bağlamlar sayfası otomatik olarak seçildi");
                }
            }
            catch (Exception ex)
            {
                _logService.Error($"Bağlamlar sayfası oluşturulurken hata: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        // Terimi ContextData içinde bağlam araması yapar
        public async Task<List<Context>> SearchContextsForTerm(TermViewModel term, int pageCount = 5)
        {
            var contexts = new List<Context>();

            if (string.IsNullOrWhiteSpace(term.EN) || !contextDataWithEmbeddings.Any())
            {
                _logService.Warning($"Bağlam araması yapılamadı: Terim boş veya contextDataWithEmbeddings verisi yok", false, LogService.LogState.Context);
                return contexts;
            }

            try
            {
                _logService.Info($"'{term}' terimi için bağlam araması yapılıyor...", true, LogService.LogState.Context);


                var result = LocalEmbedder.FindClosestWithScore(_localEmbedder.Embed(term.EN), contextDataWithEmbeddings, pageCount, 0.7f);

                // Sonuçları kontrol et ve işle
                if (result.Length > 0)
                {
                    foreach (var text in result)
                    {
                        contexts.Add(text.Item);
                    }
                    _logService.Info($"'{term}' terimi için {contexts.Count} bağlam bulundu", true, LogService.LogState.Context);
                }
                else
                {
                    _logService.Info($"'{term}' terimi için hiçbir bağlam bulunamadı", true, LogService.LogState.Context);
                }


                if (contexts.Count < pageCount)
                {
                    contexts.AddRange(ContextData.Where(x => x.Keywords != null && x.Keywords.Any(k => k.Equals(term.Lemma, StringComparison.OrdinalIgnoreCase)) &&
                    !contexts.Any(c => c.ID == x.ID)).Distinct().Take(pageCount - contexts.Count).ToList());
                }

            }
            catch (Exception ex)
            {
                _logService.Error($"Bağlam araması sırasında hata: {ex.Message}", ex, true, LogService.LogState.Context);
            }

            return contexts;
        }

        // Text verisini bağlam araması yapar
        public async Task<List<Context>> SearchContextsForText(Text text, int pageCount = 5)
        {
            var contexts = new List<Context>();

            if (string.IsNullOrWhiteSpace(text.EN) || !contextDataWithEmbeddings.Any())
            {
                _logService.Warning($"Bağlam araması yapılamadı: Terim boş veya contextDataWithEmbeddings verisi yok", false, LogService.LogState.Context);
                return contexts;
            }

            try
            {
                var textPreview = TextProcessingHelper.TruncateText(text.EN, 50);
                _logService.Info($"'{textPreview}' terimi için bağlam araması yapılıyor...", true, LogService.LogState.Context);

                var result = LocalEmbedder.FindClosestWithScore(text.Vector!.Value, contextDataWithEmbeddings, pageCount, 0.7f);

                // Sonuçları kontrol et ve işle
                if (result.Length > 0)
                {
                    foreach (var txt in result)
                    {
                        contexts.Add(txt.Item);
                    }
                    _logService.Info($"'{textPreview}' terimi için {contexts.Count} bağlam bulundu", true, LogService.LogState.Context);
                }
                else
                {
                    _logService.Info($"'{textPreview}' terimi için hiçbir bağlam bulunamadı", true, LogService.LogState.Context);
                }

            }
            catch (Exception ex)
            {
                _logService.Error($"Bağlam araması sırasında hata: {ex.Message}", ex, true, LogService.LogState.Context);
            }

            return contexts;
        }

        // Terimi Terimler içinde bağlam araması yapar
        public async Task<List<TermViewModel>> SearchTermsForTerm(TermViewModel term, List<TermViewModel> excludeList, int pageCount = 5)
        {
            var terms = new List<TermViewModel>();

            if (term == null || string.IsNullOrWhiteSpace(term.EN))
            {
                _logService.Warning($"Terim bağlamı araması yapılamadı: Terim boş veya termDataWithEmbeddings verisi yok", true, LogService.LogState.Stage2);
                return terms;
            }

            try
            {
                _logService.Info($"'{term.EN}' terimi için bağlam araması yapılıyor...", false, LogService.LogState.Stage2);

                termDataWithEmbeddings = TerminologyData
                .Where(t => t.Status == TermStatus.Çevrildi &&
                !t.EN.Equals(term.EN, StringComparison.OrdinalIgnoreCase) &&
                !excludeList.Any(ex => ex.EN.Equals(t.EN, StringComparison.OrdinalIgnoreCase)))
                .Select(c => (c, c.Vector!.Value)).ToList();

                var result = LocalEmbedder.FindClosestWithScore(_localEmbedder.Embed(term.EN), termDataWithEmbeddings, pageCount, 0.7f);

                // Sonuçları kontrol et ve işle
                if (result.Length > 0)
                {
                    foreach (var text in result)
                    {
                        terms.Add(text.Item);
                    }
                    _logService.Info($"'{term}' terimi için {terms.Count} terim bağlamı bulundu", true, LogService.LogState.Context);
                }
                else
                {
                    _logService.Info($"'{term}' terimi için hiçbir terim bağlamı bulunamadı", true, LogService.LogState.Context);
                }


            }
            catch (Exception ex)
            {
                _logService.Error($"Bağlam araması sırasında hata: {ex.Message}", ex, true, LogService.LogState.Context);
            }

            return terms;
        }


        // Benzer metinler için metinlerde bağlam araması yapar
        public async Task<List<Text>> SearchTextsForText(Text text, int pageCount = 5)
        {
            var texts = new List<Text>();

            if (string.IsNullOrWhiteSpace(text.EN))
            {
                _logService.Warning($"Bağlam araması yapılamadı: Text boş", false, LogService.LogState.Translate);
                return texts;
            }

            try
            {
                _logService.Info($"Metin için bağlam araması yapılıyor...", false, LogService.LogState.Translate);


                textDataWithEmbeddings = TextData.Where(t => t.IsTranslated && !t.EN.Equals(text.EN, StringComparison.OrdinalIgnoreCase)).Select(c => (c, c.Vector!.Value)).ToList();
                var result = LocalEmbedder.FindClosestWithScore(text.Vector!.Value, textDataWithEmbeddings, pageCount, 0.75f);

                // Sonuçları kontrol et ve işle
                if (result.Length > 0)
                {
                    foreach (var tex in result)
                    {
                        texts.Add(tex.Item);
                    }
                    _logService.Info($"Metin için {texts.Count} bağlam metni bulundu", false, LogService.LogState.Translate);
                }
                else
                {
                    _logService.Info($"Metin için hiçbir bağlam metni bulunamadı", false, LogService.LogState.Translate);
                }



            }
            catch (Exception ex)
            {
                _logService.Error($"Bağlam araması sırasında hata: {ex.Message}", ex, true, LogService.LogState.Context);
            }

            return texts;
        }

        // Otomatik çekme işlemi
        private async Task AutoFetchAsync()
        {
            if (SelectedSpreadsheet == null || !_sheetsService.IsAuthenticated)
            {
                _logService.Error("Otomatik çekme yapılamadı: E-tablo seçilmemiş veya kimlik doğrulama yapılmamış");
                return;
            }

            IsLoading = true;
            _logService.Warning("Otomatik çekme işlemi başlatılıyor...");

            try
            {
                // Önce sayfaları listele
                await ListSheetsAsync();

                // Varsayılan sayfa isimlerini tanımla
                var defaultTerminologyNames = new[] { "Terimce", "Terimler", "Terminoloji", "Terms", "Terminology" };
                var defaultContextNames = new[] { "Bağlam", "Bağlamlar", "Context", "Contexts", "Bağlamca" };
                var defaultTextNames = new[] { "Metin", "Metinler", "Text", "Texts", "UI", "Arayüz", "Altyazı", "Dialog" };

                // Terimce sayfasını bul
                var terminologySheet = FindSheetByNames(defaultTerminologyNames);
                if (terminologySheet == null)
                {
                    // ValidateHeaders ile kontrol et
                    terminologySheet = await FindSheetByValidation("Terminology");
                }

                // Bağlam sayfasını bul
                var contextSheet = FindSheetByNames(defaultContextNames);
                if (contextSheet == null)
                {
                    // ValidateHeaders ile kontrol et
                    contextSheet = await FindSheetByValidation("Context");
                }

                // Metin sayfasını bul
                var textSheet = FindSheetByNames(defaultTextNames);
                if (textSheet == null)
                {
                    // ValidateHeaders ile kontrol et
                    textSheet = await FindSheetByValidation("Text");
                }

                // Bulunamayan sayfaları oluştur
                if (terminologySheet == null)
                {
                    _logService.Warning("Terimce sayfası bulunamadı, oluşturuluyor...");
                    await CreateTerminologySheetAsync();
                    await ListSheetsAsync(); // Sayfaları yeniden listele
                    terminologySheet = FindSheetByNames(new[] { "Terimce" });
                }

                if (contextSheet == null)
                {
                    _logService.Warning("Bağlam sayfası bulunamadı, oluşturuluyor...");
                    await CreateContextSheetAsync();
                    await ListSheetsAsync(); // Sayfaları yeniden listele
                    contextSheet = FindSheetByNames(new[] { "Bağlamlar" });
                }

                // Sayfaları seç ve verileri çek
                if (terminologySheet != null)
                {
                    IsTerminologyExpanderExpanded = true;
                    SelectedTerminologySheet = terminologySheet;
                    _logService.Info($"Terimce sayfası seçildi: {terminologySheet.SheetName}");
                    await FetchOrFilterDataAsync(SelectedTerminologySheet, TerminologyFilterEntries, TerminologyData);


                }

                if (contextSheet != null)
                {
                    IsContextExpanderExpanded = true;
                    SelectedContextSheet = contextSheet;
                    _logService.Info($"Bağlam sayfası seçildi: {contextSheet.SheetName}");
                    await FetchOrFilterContextDataAsync(SelectedContextSheet, ContextFilterEntries, ContextData);


                }

                if (textSheet != null)
                {
                    IsTextExpanderExpanded = true;
                    SelectedTextSheet = textSheet;
                    _logService.Info($"Metin sayfası seçildi: {textSheet.SheetName}");
                    await FetchOrFilterTextDataAsync(SelectedTextSheet, TextFilterEntries, TextData);


                }
                else
                {
                    _logService.Error("Metin sayfası bulunamadı ve oluşturulmadı (isteğe bağlı)");
                }

                _logService.Success("Otomatik çekme işlemi tamamlandı");
            }
            catch (Exception ex)
            {
                _logService.Error($"Otomatik çekme sırasında hata: {ex.Message}", ex);
            }
            finally
            {
                IsLoading = false;
            }
        }

        // Varsayılan isimlerle sayfa bulma
        private SheetHelper? FindSheetByNames(string[] names)
        {
            foreach (var name in names)
            {
                var sheet = AvailableSheets.FirstOrDefault(s =>
                    s.SheetName.Equals(name, StringComparison.OrdinalIgnoreCase));
                if (sheet != null)
                {
                    return sheet;
                }
            }
            return null;
        }

        // ValidateHeaders ile sayfa bulma
        private async Task<SheetHelper?> FindSheetByValidation(string type)
        {
            foreach (var sheet in AvailableSheets)
            {
                try
                {
                    var data = await _sheetsService.GetDataAsync(SpreadsheetId, sheet.SheetName);
                    if (data != null && data.Any())
                    {
                        var headers = data.First().Select(h => h?.ToString() ?? string.Empty).ToList();

                        bool isValid = type switch
                        {
                            "Terminology" => TermViewModel.ValidateHeaders(headers).IsValid,
                            "Context" => Context.ValidateHeaders(headers).IsValid,
                            "Text" => Text.ValidateHeaders(headers).IsValid,
                            _ => false
                        };

                        if (isValid)
                        {
                            _logService.Info($"{type} sayfası ValidateHeaders ile bulundu: {sheet.SheetName}");
                            return sheet;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logService.Warning($"Sayfa kontrol edilirken hata: {sheet.SheetName} - {ex.Message}");
                }
            }
            return null;
        }



        // Bağlamlar için (Context)
        private async Task FetchOrFilterContextDataAsync(SheetHelper? selectedSheet, ObservableCollection<FilterEntry> filterEntries, ObservableCollection<Context> targetData)
        {
            if (
                selectedSheet == null
                || string.IsNullOrWhiteSpace(selectedSheet.SheetName)
                || !_sheetsService.IsAuthenticated
                || string.IsNullOrWhiteSpace(SpreadsheetId)
            )
            {
                _logService.Error("Veri çekilemedi: Sayfa seçilmemiş veya kimlik doğrulama yapılmamış");
                return;
            }

            IsLoading = true;
            _logService.Info($"'{selectedSheet.SheetName}' sayfasından bağlamlar çekiliyor");
            targetData.Clear();



            try
            {
                var filters = filterEntries
                    .Where(f => !string.IsNullOrWhiteSpace(f.FilterValue))
                    .Select(f => new
                    {
                        ColumnName = f.ColumnName,
                        Value = f.FilterValue,
                        Operator = f.SelectedOperator,
                        Type = f.SelectedType
                    })
                    .ToList();

                if (filters.Count > 0)
                {
                    _logService.Info($"Filtreler uygulanıyor: {string.Join(", ", filters.Select(f => $"{f.ColumnName} {f.Operator} {f.Value}"))}");
                }

                var data = await _sheetsService.GetDataAsync(SpreadsheetId, selectedSheet.SheetName);
                if (data != null && data.Any())
                {
                    var headers = data.First().Select(h => h?.ToString() ?? string.Empty).ToList();

                    // Sütun başlıklarını doğrula
                    var validation = Context.ValidateHeaders(headers);
                    if (!validation.IsValid)
                    {
                        _logService.Error($"Geçersiz sütun yapısı: {validation.ErrorMessage}");
                        return;
                    }

                    var records = new List<Context>();

                    for (int i = 1; i < data.Count; i++)
                    {
                        var row = data[i];
                        var record = new Context();
                        bool matchesFilters = true;

                        // Property set
                        // ID sütunu eklendi
                        if (headers.Contains("#") && headers.IndexOf("#") < row.Count)
                        {
                            if (int.TryParse(row[headers.IndexOf("#")]?.ToString(), out int id))
                            {
                                record.ID = id;
                            }
                        }
                        record.Kategori = headers.Contains("Kategori") && headers.IndexOf("Kategori") < row.Count ? row[headers.IndexOf("Kategori")]?.ToString() ?? string.Empty : string.Empty;
                        record.AltBaslik = headers.Contains("Alt Başlık") && headers.IndexOf("Alt Başlık") < row.Count ? row[headers.IndexOf("Alt Başlık")]?.ToString() ?? string.Empty : string.Empty;
                        record.Icerik = headers.Contains("İçerik") && headers.IndexOf("İçerik") < row.Count ? row[headers.IndexOf("İçerik")]?.ToString() ?? string.Empty : string.Empty;
                        record.URL = headers.Contains("URL") && headers.IndexOf("URL") < row.Count ? row[headers.IndexOf("URL")]?.ToString() ?? string.Empty : string.Empty;

                        // Keywords alanını parse et
                        if (headers.Contains("Keywords") && headers.IndexOf("Keywords") < row.Count)
                        {
                            var keywordsStr = row[headers.IndexOf("Keywords")]?.ToString() ?? string.Empty;
                            if (!string.IsNullOrWhiteSpace(keywordsStr))
                            {
                                record.Keywords = keywordsStr.Split(',').Select(k => k.Trim()).ToList();
                            }
                        }

                        // Filtre uygula
                        foreach (var filter in filters)
                        {
                            string stringValue = filter.ColumnName switch
                            {
                                "#" => record.ID.ToString(),
                                "Kategori" => record.Kategori,
                                "Alt Başlık" => record.AltBaslik,
                                "İçerik" => record.Icerik.Replace("\r\n", " ").Replace("\n", " "),
                                "URL" => record.URL,
                                "Keywords" => record.Keywords != null ? string.Join(", ", record.Keywords) : string.Empty,
                                _ => string.Empty
                            };
                            var filterValue = filter.Value;
                            bool matches = filter.Type switch
                            {
                                "Number" when double.TryParse(stringValue, out double numValue) && double.TryParse(filterValue, out double filterNumValue) =>
                                    filter.Operator switch
                                    {
                                        "=" => numValue == filterNumValue,
                                        "!=" => numValue != filterNumValue,
                                        ">" => numValue > filterNumValue,
                                        ">=" => numValue >= filterNumValue,
                                        "<" => numValue < filterNumValue,
                                        "<=" => numValue <= filterNumValue,
                                        _ => false
                                    },
                                "Boolean" when bool.TryParse(stringValue, out bool boolValue) && bool.TryParse(filterValue, out bool filterBoolValue) =>
                                    filter.Operator switch
                                    {
                                        "=" => boolValue == filterBoolValue,
                                        "!=" => boolValue != filterBoolValue,
                                        _ => false
                                    },
                                "String" =>
                                    filter.Operator switch
                                    {
                                        "=" => stringValue.Equals(filterValue, StringComparison.OrdinalIgnoreCase),
                                        "!=" => !stringValue.Equals(filterValue, StringComparison.OrdinalIgnoreCase),
                                        "Contains" => stringValue.Contains(filterValue, StringComparison.OrdinalIgnoreCase),
                                        "Not Contains" => !stringValue.Contains(filterValue, StringComparison.OrdinalIgnoreCase),
                                        "Regex" => System.Text.RegularExpressions.Regex.IsMatch(stringValue ?? "", filterValue),
                                        "Not Regex" => !System.Text.RegularExpressions.Regex.IsMatch(stringValue ?? "", filterValue),
                                        _ => false
                                    },
                                _ => stringValue.Equals(filterValue, StringComparison.OrdinalIgnoreCase)
                            };
                            if (!matches)
                            {
                                matchesFilters = false;
                                break;
                            }
                        }
                        if (matchesFilters)
                        {
                            records.Add(record);
                        }
                    }

                    targetData.AddRange(records);

                    if (targetData.Count > 0)
                    {
                        // Context'lerin Vector property'sine embedding'leri ata
                        var tasks = targetData.Select(async context =>
                        {
                            context.Lemma = await Task.Run(() => TextProcessingHelper.ProcessTextToLemma(context.Icerik, _nlp));
                            context.Vector = await Task.Run(() => _localEmbedder.Embed(context.Kategori + " - " + context.AltBaslik + "\n" + context.Icerik));
                        });

                        await Task.WhenAll(tasks);
                    }
                    contextDataWithEmbeddings = targetData.Where(c => c.ID != MainContext?.ID).Select(c => (c, c.Vector!.Value)).ToList();

                    IsContextDataFetched = true;
                    _logService.Info($"{selectedSheet.SheetName} sayfasından {targetData.Count} bağlam çekildi.");
                    _logService.Info($"'{selectedSheet.SheetName}' sayfasından {targetData.Count} satır veri çekildi");

                    // Ana bağlam kontrolü - kategori isminde [Ana Bağlam] varsa onu ana bağlam olarak seç
                    MainContext = targetData.FirstOrDefault(c => !string.IsNullOrEmpty(c.Kategori) && c.Kategori.Contains("[Ana Bağlam]"));
                    if (MainContext != null)
                    {
                        MainContextFound?.Invoke(this, MainContext);
                        _logService.Info($"Ana bağlam otomatik olarak seçildi: {MainContext.AltBaslik}", true, LogService.LogState.Context);
                    }
                }
                else
                {
                    _logService.Warning($"'{selectedSheet.SheetName}' sayfasından veri alınamadı veya boş");
                }
            }
            catch (Exception ex)
            {
                _logService.Error($"Veri çekerken hata: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

    }

    // --- Yardımcı Sınıflar ---
    // E-tablo bilgisi için yardımcı sınıf
    public class SpreadsheetInfo
    {
        public string Name { get; set; }
        public string Id { get; set; }
        public SpreadsheetInfo(string name, string id) { Name = name; Id = id; }
        public override string ToString() => Name;
    }
    // Sayfa bilgisi için yardımcı sınıf
    public class SheetHelper
    {
        public string SheetName { get; set; }
        public int? SheetId { get; set; }
        public SheetHelper(string sheetName, int? sheetId) { SheetName = sheetName ?? string.Empty; SheetId = sheetId; }
        public override string ToString() => SheetName;
    }
    // Filtre için yardımcı sınıf
    public partial class FilterEntry : ObservableObject
    {
        [ObservableProperty] private string _columnName = string.Empty;
        [ObservableProperty] private string _filterValue = string.Empty;
        [ObservableProperty] private string _selectedOperator = "=";
        [ObservableProperty] private string _selectedType = "String";
        private static readonly Dictionary<string, List<string>> TypeOperators = new()
        {
            ["String"] = new() { "=", "!=", "Contains", "Not Contains", "Regex", "Not Regex" },
            ["Number"] = new() { "=", "!=", ">", ">=", "<", "<=" },
            ["Boolean"] = new() { "=", "!=" }
        };
        public List<string> AvailableOperators => TypeOperators[SelectedType];
        public static List<string> AvailableTypes => new() { "String", "Number", "Boolean" };
        protected override void OnPropertyChanged(PropertyChangedEventArgs e)
        {
            base.OnPropertyChanged(e);
            if (e.PropertyName == nameof(SelectedType))
            {
                SelectedOperator = TypeOperators[SelectedType][0];
                OnPropertyChanged(nameof(AvailableOperators));
            }
        }
    }
}