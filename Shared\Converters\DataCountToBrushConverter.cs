using System;
using System.Collections.Generic;
using System.Globalization;
using Avalonia.Data.Converters;
using Avalonia.Media;
using TranslationAgent.Features.GoogleSheets.ViewModels;

namespace TranslationAgent.Converters
{
    public class DataCountToBrushConverter : IMultiValueConverter
    {
        private static readonly IBrush RedBrush = new SolidColorBrush(Color.FromRgb(255, 0, 0));
        private static readonly IBrush GreenBrush = new SolidColorBrush(Color.FromRgb(0, 255, 0));

        private static DataCountToBrushConverter? _instance;
        public static DataCountToBrushConverter Instance => _instance ??= new DataCountToBrushConverter();

        public object? Convert(IList<object?> values, Type targetType, object? parameter, CultureInfo culture)
        {
            if (values.Count >= 2 && values[1] is bool isFetched)
            {
                return isFetched ? GreenBrush : RedBrush;
            }
            return RedBrush;
        }

        public object[]? ConvertBack(object? value, Type[] targetTypes, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}