using Avalonia;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Media;
using Avalonia.Platform;
using System;
using System.Collections.Specialized;
using TranslationAgent.Features.GoogleSheets.ViewModels;

namespace TranslationAgent.Features.GoogleSheets.Views
{
    public partial class GoogleSheetsPanelView : UserControl
    {
        private bool _isExpanded = true;
        private const string ArrowRight = "M 4,2 L 12,10 L 4,18";
        private const string ArrowLeft = "M 12,2 L 4,10 L 12,18";
        private const double ExpandedWidth = 300;
        private const double CollapsedWidth = 30;
        private GoogleSheetsPanelViewModel? _viewModel;

        public GoogleSheetsPanelView()
        {
            InitializeComponent();
            UpdatePanelState(true);
            DataContextChanged += OnDataContextChanged;
            AttachExpanderEvents();
        }

        private void AttachExpanderEvents()
        {
            if (TerminologyExpander != null)
                TerminologyExpander.PropertyChanged += OnExpanderPropertyChanged;
            if (ContextExpander != null)
                ContextExpander.PropertyChanged += OnExpanderPropertyChanged;
            if (TextsExpander != null)
                TextsExpander.PropertyChanged += OnExpanderPropertyChanged;
        }

        private void OnExpanderPropertyChanged(object? sender, AvaloniaPropertyChangedEventArgs e)
        {
            if (e.Property.Name != "IsExpanded" || sender is not Expander changedExpander) return;

            if ((bool)e.NewValue! == true && ContentPanel.Extent.Height > ContentPanel.Viewport.Height)
            {
                // Diğer expander'ları kapat
                if (changedExpander != TerminologyExpander && TerminologyExpander != null)
                    TerminologyExpander.IsExpanded = false;
                if (changedExpander != ContextExpander && ContextExpander != null)
                    ContextExpander.IsExpanded = false;
                if (changedExpander != TextsExpander && TextsExpander != null)
                    TextsExpander.IsExpanded = false;
            }
        }

        private void OnToggleClick(object sender, RoutedEventArgs e)
        {
            _isExpanded = !_isExpanded;
            UpdatePanelState(_isExpanded);
        }

        private void UpdatePanelState(bool expanded)
        {
            if (MainPanel == null || ToggleIcon == null || ContentPanel == null || HeaderText == null) return;

            MainPanel.Width = expanded ? ExpandedWidth : CollapsedWidth;
            ContentPanel.IsVisible = expanded;
            HeaderText.IsVisible = expanded;
            ToggleIcon.Data = Geometry.Parse(expanded ? ArrowLeft : ArrowRight);
        }

        private void OnDataContextChanged(object? sender, EventArgs e)
        {
            // Eski ViewModel'den event'leri kaldır
            if (_viewModel != null)
            {
                _viewModel.TerminologyData.CollectionChanged -= OnTerminologyDataChanged;
                _viewModel.TextData.CollectionChanged -= OnTextDataChanged;
                _viewModel.ContextData.CollectionChanged -= OnContextDataChanged;
            }

            // Yeni ViewModel'i ayarla
            _viewModel = DataContext as GoogleSheetsPanelViewModel;

            // Yeni ViewModel'e event'leri ekle
            if (_viewModel != null)
            {
                _viewModel.TerminologyData.CollectionChanged += OnTerminologyDataChanged;
                _viewModel.TextData.CollectionChanged += OnTextDataChanged;
                _viewModel.ContextData.CollectionChanged += OnContextDataChanged;
            }
        }

        private void OnTerminologyDataChanged(object? sender, NotifyCollectionChangedEventArgs e)
        {
            // Yeni öğe eklendiğinde ScrollViewer'ı en aşağıya kaydır
            if (e.Action == NotifyCollectionChangedAction.Add && ContentPanel != null)
            {
                // UI thread'de çalıştır
                Avalonia.Threading.Dispatcher.UIThread.Post(() =>
                {
                    ContentPanel.ScrollToEnd();
                });
            }
        }

        private void OnTextDataChanged(object? sender, NotifyCollectionChangedEventArgs e)
        {
            // Yeni öğe eklendiğinde ScrollViewer'ı en aşağıya kaydır
            if (e.Action == NotifyCollectionChangedAction.Add && ContentPanel != null)
            {
                // UI thread'de çalıştır
                Avalonia.Threading.Dispatcher.UIThread.Post(() =>
                {
                    ContentPanel.ScrollToEnd();
                });
            }
        }

        private void OnContextDataChanged(object? sender, NotifyCollectionChangedEventArgs e)
        {
            // Yeni öğe eklendiğinde ScrollViewer'ı en aşağıya kaydır
            if (e.Action == NotifyCollectionChangedAction.Add && ContentPanel != null)
            {
                // UI thread'de çalıştır
                Avalonia.Threading.Dispatcher.UIThread.Post(() =>
                {
                    ContentPanel.ScrollToEnd();
                });
            }
        }
    }
}