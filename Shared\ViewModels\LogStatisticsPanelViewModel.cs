using System;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;
using CommunityToolkit.Mvvm.ComponentModel;
using TranslationAgent.Services;

namespace TranslationAgent.Shared.ViewModels
{
    public partial class StatisticItem : ObservableObject
    {
        [ObservableProperty]
        private string _label = string.Empty;

        [ObservableProperty]
        private string _value = string.Empty;

        public StatisticItem(string label, string value)
        {
            Label = label;
            Value = value;
        }

        public StatisticItem() { }
    }

    public partial class LogStatisticsPanelViewModel : ObservableObject, IDisposable
    {
        private readonly LogService _logService;
        private readonly LogService.LogState[] _listenedLogStates;

        [ObservableProperty]
        private ObservableCollection<string> _logMessages = new();

        [ObservableProperty]
        private string _logText = string.Empty;

        [ObservableProperty]
        private ObservableCollection<StatisticItem> _statisticItems = new();

        [ObservableProperty]
        private double _overallProgress = 0.0; // 0-100 arası

        [ObservableProperty]
        private string _statisticsTitle = "İstatistikler";

        [ObservableProperty]
        private string _progressLabel = "Genel İlerleme:";

        [ObservableProperty]
        private bool _showProgressBar = true;

        public LogStatisticsPanelViewModel(LogService logService, params LogService.LogState[] logStates)
        {
            _logService = logService;
            _listenedLogStates = logStates?.Length > 0 ? logStates : new[] { LogService.LogState.Main };

            // Belirtilen LogState'lere göre event'lere abone ol
            SubscribeToLogEvents();

            // LogMessages collection değişikliklerini dinle
            LogMessages.CollectionChanged += (s, e) => UpdateLogText();
        }

        private void SubscribeToLogEvents()
        {
            foreach (var logState in _listenedLogStates)
            {
                switch (logState)
                {
                    case LogService.LogState.Main:
                        _logService.MainLogAdded += OnUILogAdded;
                        break;
                    case LogService.LogState.Stage1:
                        _logService.Stage1LogAdded += OnUILogAdded;
                        break;
                    case LogService.LogState.Stage2:
                        _logService.Stage2LogAdded += OnUILogAdded;
                        break;
                    case LogService.LogState.Stage3:
                        _logService.Stage3LogAdded += OnUILogAdded;
                        break;
                    case LogService.LogState.Context:
                        _logService.ContextLogAdded += OnUILogAdded;
                        break;
                    case LogService.LogState.Translate:
                        _logService.TranslateLogAdded += OnUILogAdded;
                        break;
                }
            }
        }

        private void UnsubscribeFromLogEvents()
        {
            foreach (var logState in _listenedLogStates)
            {
                switch (logState)
                {
                    case LogService.LogState.Main:
                        _logService.MainLogAdded -= OnUILogAdded;
                        break;
                    case LogService.LogState.Stage1:
                        _logService.Stage1LogAdded -= OnUILogAdded;
                        break;
                    case LogService.LogState.Stage2:
                        _logService.Stage2LogAdded -= OnUILogAdded;
                        break;
                    case LogService.LogState.Stage3:
                        _logService.Stage3LogAdded -= OnUILogAdded;
                        break;
                    case LogService.LogState.Context:
                        _logService.ContextLogAdded -= OnUILogAdded;
                        break;
                    case LogService.LogState.Translate:
                        _logService.TranslateLogAdded -= OnUILogAdded;
                        break;
                }
            }
        }

        private void OnUILogAdded(string message)
        {
            LogMessages.Add(message);
        }

        private void UpdateLogText()
        {
            LogText = string.Join(Environment.NewLine, LogMessages);
        }



        public void SetStatisticItems(params (string label, string value)[] items)
        {
            StatisticItems.Clear();
            foreach (var (label, value) in items)
            {
                StatisticItems.Add(new StatisticItem(label, value));
            }
        }

        public void UpdateStatisticItem(string label, string newValue)
        {
            var item = StatisticItems.FirstOrDefault(x => x.Label == label);
            if (item != null)
            {
                item.Value = newValue;
            }
        }

        public void SetProgressBar(double progress, bool show = true)
        {
            OverallProgress = progress;
            ShowProgressBar = show;
        }

        public void ClearLogs()
        {
            LogMessages.Clear();
            LogText = string.Empty;
        }

        public void ResetStatistics()
        {
            StatisticItems.Clear();
            OverallProgress = 0;
        }

        public void SetCustomLabels(string? statisticsTitle = null, string? progressLabel = null)
        {
            if (!string.IsNullOrEmpty(statisticsTitle))
                StatisticsTitle = statisticsTitle;
            if (!string.IsNullOrEmpty(progressLabel))
                ProgressLabel = progressLabel;
        }

        public void Dispose()
        {
            // Event aboneliklerini temizle
            UnsubscribeFromLogEvents();
            GC.SuppressFinalize(this);
        }
    }
}
