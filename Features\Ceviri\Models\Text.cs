using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using SmartComponents.LocalEmbeddings;

namespace TranslationAgent.Features.Ceviri.Models
{
    /// <summary>
    /// Google Sheets'ten çeviri verilerini temsil eden model
    /// </summary>
    public class Text : INotifyPropertyChanged
    {
        private int _id;
        private string _en = string.Empty;
        private string _tr = string.Empty;
        private TranslationStatus _status;
        private string? _namespace;
        private string? _key;
        private string? _lemma;
        private string? _note;
        private EmbeddingF32? _vector;


        /// <summary>
        /// Sıra numarası (#)
        /// </summary>
        public int ID
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        /// <summary>
        /// Satır numarası (DataGrid için)
        /// </summary>
        public int RowNumber => _id;

        /// <summary>
        /// İngilizce metin (EN)
        /// </summary>
        public string EN
        {
            get => _en;
            set => SetProperty(ref _en, value ?? string.Empty);
        }

        /// <summary>
        /// Türkçe metin (TR)
        /// </summary>
        public string TR
        {
            get => _tr;
            set => SetProperty(ref _tr, value ?? string.Empty);
        }

        /// <summary>
        /// Çeviri durumu (EN-TR)
        /// </summary>
        public TranslationStatus Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        /// <summary>
        /// Namespace (opsiyonel)
        /// </summary>
        public string? Namespace
        {
            get => _namespace;
            set => SetProperty(ref _namespace, value);
        }

        /// <summary>
        /// Key (opsiyonel)
        /// </summary>
        public string? Key
        {
            get => _key;
            set => SetProperty(ref _key, value);
        }

        /// <summary>
        /// Lemmatized text (NLP işlemi sonucu)
        /// </summary>
        public string? Lemma
        {
            get => _lemma;
            set => SetProperty(ref _lemma, value);
        }

        /// <summary>
        /// Not (opsiyonel)
        /// </summary>
        public string? Note
        {
            get => _note;
            set => SetProperty(ref _note, value);
        }

        /// <summary>
        /// Vector embedding
        /// </summary>
        public EmbeddingF32? Vector
        {
            get => _vector;
            set => SetProperty(ref _vector, value);
        }

        /// <summary>
        /// Namespace sütununun mevcut olup olmadığını kontrol eder
        /// </summary>
        public bool HasNamespace => !string.IsNullOrWhiteSpace(Namespace);

        /// <summary>
        /// Key sütununun mevcut olup olmadığını kontrol eder
        /// </summary>
        public bool HasKey => !string.IsNullOrWhiteSpace(Key);

        /// <summary>
        /// Note sütununun mevcut olup olmadığını kontrol eder
        /// </summary>
        public bool HasNote => !string.IsNullOrWhiteSpace(Note);

        /// <summary>
        /// Çeviri durumunu string olarak döndürür
        /// </summary>
        public string StatusText => Status.ToString();

        /// <summary>
        /// Çevirilmesi gereken bir metin olup olmadığını kontrol eder
        /// </summary>
        public bool NeedsTranslation => Status == TranslationStatus.EN;

        /// <summary>
        /// Çevirisi tamamlanmış bir metin olup olmadığını kontrol eder
        /// </summary>
        public bool IsTranslated => Status == TranslationStatus.TR;

        /// <summary>
        /// Duplicate bir metin olup olmadığını kontrol eder
        /// </summary>
        public bool IsDuplicate => Status == TranslationStatus.DUPE;

        /// <summary>
        /// Boş bir metin olup olmadığını kontrol eder
        /// </summary>
        public bool IsNull => Status == TranslationStatus.NULL;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// String değerini TranslationStatus enum'una çevirir
        /// </summary>
        /// <param name="statusValue">String değer</param>
        /// <returns>TranslationStatus enum değeri</returns>
        public static TranslationStatus ParseStatus(string? statusValue)
        {
            if (string.IsNullOrWhiteSpace(statusValue))
                return TranslationStatus.NULL;

            return statusValue.ToUpperInvariant() switch
            {
                "EN" => TranslationStatus.EN,
                "TR" => TranslationStatus.TR,
                "NULL" => TranslationStatus.NULL,
                "DUPE" => TranslationStatus.DUPE,
                _ => TranslationStatus.NULL
            };
        }

        /// <summary>
        /// Google Sheets satırından TextData oluşturur
        /// </summary>
        /// <param name="headers">Sütun başlıkları</param>
        /// <param name="row">Satır verileri</param>
        /// <param name="rowIndex">Satır indeksi</param>
        /// <returns>TextData nesnesi</returns>
        public static Text FromSheetRow(List<string> headers, IList<object> row, int rowIndex)
        {
            var textData = new Text { ID = rowIndex };

            for (int i = 0; i < headers.Count && i < row.Count; i++)
            {
                var header = headers[i];
                var value = row[i]?.ToString() ?? string.Empty;

                switch (header.ToUpperInvariant())
                {
                    case "#":
                        if (int.TryParse(value, out int rowNum))
                            textData.ID = rowNum;
                        break;
                    case "EN":
                        textData.EN = value;
                        break;
                    case "TR":
                        textData.TR = value;
                        break;
                    case "EN-TR":
                        textData.Status = ParseStatus(value);
                        break;
                    case "NAMESPACE":
                        textData.Namespace = value;
                        break;
                    case "KEY":
                        textData.Key = value;
                        break;
                    case "NOT":
                        textData.Note = value;
                        break;
                }
            }

            return textData;
        }

        /// <summary>
        /// Sütun başlıklarının geçerliliğini kontrol eder
        /// </summary>
        /// <param name="headers">Sütun başlıkları</param>
        /// <returns>Validation sonucu</returns>
        public static (bool IsValid, string ErrorMessage, bool HasNamespace, bool HasKey, bool HasNote) ValidateHeaders(List<string> headers)
        {
            var upperHeaders = headers.Select(h => h.ToUpperInvariant()).ToList();

            // Gerekli sütunları kontrol et
            var requiredColumns = new[] { "#", "EN", "TR", "EN-TR" };
            var missingColumns = requiredColumns.Where(col => !upperHeaders.Contains(col)).ToList();

            if (missingColumns.Any())
            {
                return (false, $"Gerekli sütunlar eksik: {string.Join(", ", missingColumns)}", false, false, false);
            }

            // Opsiyonel sütunları kontrol et
            bool hasNamespace = upperHeaders.Contains("NAMESPACE");
            bool hasKey = upperHeaders.Contains("KEY");
            bool hasNote = upperHeaders.Contains("NOT");

            return (true, string.Empty, hasNamespace, hasKey, hasNote);
        }
    }
}
