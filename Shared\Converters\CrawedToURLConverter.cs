using System;
using System.Globalization;
using Abot2.Poco;
using Avalonia.Data.Converters;

namespace TranslationAgent.Converters
{
    public class CrawedToURLConverter : IValueConverter
    {
        public static readonly CrawedToURLConverter Instance = new();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is CrawledPage page)
            {
                var uri = page.Uri;

                if (uri.AbsolutePath == "/")
                {
                    return uri.Host;
                }
                // Varsayılan olarak tam URL'i döndür
                return uri.AbsolutePath;
            }
            return string.Empty;
        }

        public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
