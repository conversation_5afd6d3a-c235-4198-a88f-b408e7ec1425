using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Abot2.Core;
using Abot2.Crawler;
using Abot2.Poco;
using TranslationAgent.Features.Baglam.Models;
using TranslationAgent.Services;

namespace TranslationAgent.Services
{
    public class CrawlerService
    {
        private readonly LogService _logService;
        public List<string> _crawledUrls = new List<string>();
        private int _crawledPages = 0;


        // Crawl edilmeyecek dosya uzantıları
        private readonly HashSet<string> _excludedExtensions = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg", ".webp", ".ico", ".tiff", ".tif",
            ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".rtf",
            ".zip", ".rar", ".7z", ".tar", ".gz", ".exe", ".msi", ".dmg", ".deb", ".rpm",
            ".mp3", ".wav", ".flac", ".aac", ".ogg", ".wma", ".m4a",
            ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v",
            ".css", ".js", ".json", ".xml", ".rss", ".atom", ".sitemap",
            ".woff", ".woff2", ".ttf", ".eot", ".otf"
        };

        public CrawlerService(LogService logService)
        {
            _logService = logService;
        }

        /// <summary>
        /// URL'nin web sayfası olup olmadığını kontrol eder
        /// </summary>
        /// <param name="uri">Kontrol edilecek URI</param>
        /// <returns>Web sayfası ise true, dosya ise false</returns>
        private bool IsWebPage(Uri uri)
        {
            try
            {
                var path = uri.AbsolutePath.ToLowerInvariant();

                // Eğer path "/" ile bitiyorsa veya uzantısı yoksa web sayfası olarak kabul et
                if (path == "/" || path.EndsWith("/") || !Path.HasExtension(path))
                {
                    return true;
                }

                // Dosya uzantısını al
                var extension = Path.GetExtension(path);

                // Eğer uzantı excluded listesinde varsa crawl etme
                if (_excludedExtensions.Contains(extension))
                {
                    return false;
                }

                // HTML uzantıları için özel kontrol
                var htmlExtensions = new[] { ".html", ".htm", ".php", ".asp", ".aspx", ".jsp", ".cfm" };
                if (htmlExtensions.Contains(extension))
                {
                    return true;
                }

                // Diğer durumlar için varsayılan olarak web sayfası kabul et
                return true;
            }
            catch
            {
                // Hata durumunda güvenli tarafta kal ve crawl et
                return true;
            }
        }


        public async Task CrawlWebsiteAsync(string url, ObservableCollection<CrawledPage> crawledPages, int maxPages = 1, int maxDepth = 1, bool respectRobotsTxt = false, CancellationTokenSource cancellationToken = default)
        {
            try
            {


                // Abot crawler konfigürasyonu
                var config = new CrawlConfiguration
                {
                    MaxConcurrentThreads = 1,
                    MaxPagesToCrawl = maxPages,
                    MaxPagesToCrawlPerDomain = maxPages,
                    MaxCrawlDepth = maxDepth,
                    IsUriRecrawlingEnabled = false,
                    IsExternalPageCrawlingEnabled = false,
                    IsExternalPageLinksCrawlingEnabled = false,
                    DownloadableContentTypes = "text/html, text/plain",
                    HttpServicePointConnectionLimit = 200,
                    HttpRequestTimeoutInSeconds = 15,
                    HttpRequestMaxAutoRedirects = 7,
                    IsHttpRequestAutoRedirectsEnabled = true,
                    UserAgentString = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                    IsRespectRobotsDotTextEnabled = respectRobotsTxt,
                    IsRespectMetaRobotsNoFollowEnabled = false,
                    IsRespectHttpXRobotsTagHeaderNoFollowEnabled = false,
                    IsRespectAnchorRelNoFollowEnabled = false,
                    IsIgnoreRobotsDotTextIfRootDisallowedEnabled = true,
                    IsSendingCookiesEnabled = false,
                    MinCrawlDelayPerDomainMilliSeconds = 100,
                };

                var crawler = new PoliteWebCrawler(config);



                // URL filtreleme - sadece web sayfalarını crawl et
                crawler.ShouldCrawlPageDecisionMaker = (pageToCrawl, crawlContext) =>
                {
                    var uri = pageToCrawl.Uri;

                    if (!IsWebPage(uri))
                    {
                        // _logService.Info($"Dosya uzantısı nedeniyle atlandı: {uri}", true, LogService.LogState.Context);
                        return new CrawlDecision { Allow = false, Reason = "Dosya uzantısı filtrelendi" };
                    }
    
                    _crawledUrls.Add(uri.ToString()); // Taranan sayfalar listesine ekle

                    return new CrawlDecision { Allow = true };
                };

     

                // Sayfa işlendiğinde çalışacak event handler
                crawler.PageCrawlCompleted += (sender, e) =>
                {
                    _crawledPages++;
                    var crawledPage = e.CrawledPage;
                    if (crawledPage.HttpResponseMessage?.IsSuccessStatusCode == true)
                    {
                        crawledPages.Add(crawledPage);
                        _logService.Info($"Sayfa başarıyla tarandı: {crawledPage.Uri.ToString()}", true, LogService.LogState.Context);
                        _logService.Info($"İlerleme {_crawledPages} / {_crawledUrls.Count}", true, LogService.LogState.Context);
                        if (cancellationToken.Token.IsCancellationRequested)
                        {
                            crawler.Dispose();
                            _logService.Info("Tarama işlemi durduruldu.", true, LogService.LogState.Context);
                        }


                    }
                    else
                    {
                        _logService.Info($"Sayfa taranamadı: {crawledPage.Uri} - Status: {crawledPage.HttpResponseMessage?.StatusCode}", true, LogService.LogState.Context);
                    }
                };

                // Hata durumunda çalışacak event handler
                crawler.PageCrawlDisallowed += (sender, e) =>
                {
                    var reason = e.DisallowedReason;
                    if (reason.Contains("robots.txt"))
                    {
                        _logService.Warning($"Sayfa robots.txt tarafından engellendi: {e.PageToCrawl.Uri}. 'Robots.txt dosyasına saygı göster' seçeneğini kapatarak bu kısıtlamayı aşabilirsiniz.", true, LogService.LogState.Context);
                    }
                    else
                    {
                        _logService.Info($"Sayfa taranması engellendi: {e.PageToCrawl.Uri} - Sebep: {reason}", true, LogService.LogState.Context); ;
                    }
                };

                // CancellationToken kontrolü
                cancellationToken.Token.ThrowIfCancellationRequested();

                var crawlResult = await crawler.CrawlAsync(new Uri(url));

                if (crawlResult.ErrorOccurred)
                {
                    if (!cancellationToken.Token.IsCancellationRequested)
                        _logService.Error($"Tarama sırasında hata oluştu: {crawlResult.ErrorException?.Message}", crawlResult.ErrorException, true, LogService.LogState.Context);
                }

            }
            catch (Exception ex)
            {
                _logService.Error($"Web sitesi taranırken beklenmeyen hata oluştu: {ex.Message}", ex, true, LogService.LogState.Context);

            }

        }
    }
}