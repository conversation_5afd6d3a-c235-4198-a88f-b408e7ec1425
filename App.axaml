<Application
    x:Class="TranslationAgent.App"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:TranslationAgent.Converters"
    RequestedThemeVariant="Default">
    <!--  "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options.  -->

    <Application.Resources>
        <converters:BoolToRedBackgroundConverter x:Key="BoolToRedBackgroundConverter" />
    </Application.Resources>

    <Application.Styles>
        <FluentTheme />
        <StyleInclude Source="avares://Avalonia.Controls.DataGrid/Themes/Fluent.xaml" />
    </Application.Styles>
</Application>
