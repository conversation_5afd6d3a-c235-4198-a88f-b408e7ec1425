using System.Linq;
using Avalonia.Controls;
using Avalonia.Controls.Generators;
using Avalonia.Controls.Primitives;
using Avalonia.Markup.Xaml;
using Avalonia.Media;
using Avalonia.Styling;
using Avalonia.VisualTree;
using TranslationAgent.Features.Settings.ViewModels;

namespace TranslationAgent.Features.Settings.Views
{
    public partial class SettingsView : UserControl
    {
        private SettingsViewModel ViewModel => DataContext as SettingsViewModel;
        private DataGrid _keysDataGrid;
        private TabControl _settingsTabControl;

        public SettingsView()
        {
            InitializeComponent();
            DataContextChanged += SettingsView_DataContextChanged;
        }

        private void SettingsView_DataContextChanged(object sender, System.EventArgs e)
        {
            if (DataContext is SettingsViewModel viewModel)
            {
                _keysDataGrid = this.FindControl<DataGrid>("GeminiKeysGrid");
                _settingsTabControl = this.FindControl<TabControl>("SettingsTabControl");

                if (_keysDataGrid != null)
                {
                    _keysDataGrid.LoadingRow += OnDataGridLoadingRow;
                    _keysDataGrid.SelectionChanged += DataGrid_SelectionChanged;
                }
            }
        }

        private void OnDataGridLoadingRow(object sender, DataGridRowEventArgs e)
        {
            if (e.Row.DataContext is GeminiKeyViewModel keyViewModel && keyViewModel.IsQuotaZero)
            {
                e.Row.Background = new SolidColorBrush(Color.Parse("#33FF0000"));
            }
            else
            {
                e.Row.Background = new SolidColorBrush(Colors.Transparent);
            }
        }

        private void DataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ViewModel != null && sender is DataGrid dataGrid)
            {
                ViewModel.SelectedApiKey = dataGrid.SelectedItem as GeminiKeyViewModel;
            }
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }
    }
}