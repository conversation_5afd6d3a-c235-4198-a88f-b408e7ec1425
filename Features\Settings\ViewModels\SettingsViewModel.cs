using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using TranslationAgent.Services;

namespace TranslationAgent.Features.Settings.ViewModels
{
    public class GeminiKeyViewModel : INotifyPropertyChanged
    {
        private string _key;
        private int _dailyQuotaFlash2_0;
        private int _dailyQuotaFlash2_5;
        private int _remainingQuotaFlash2_0;
        private int _remainingQuotaFlash2_5;
        private DateTime _lastResetDate;

        public string Key
        {
            get => _key;
            set
            {
                _key = value;
                OnPropertyChanged();
            }
        }

        public int DailyQuotaFlash2_0
        {
            get => _dailyQuotaFlash2_0;
            set
            {
                _dailyQuotaFlash2_0 = value;
                OnPropertyChanged();
            }
        }

        public int DailyQuotaFlash2_5
        {
            get => _dailyQuotaFlash2_5;
            set
            {
                _dailyQuotaFlash2_5 = value;
                OnPropertyChanged();
            }
        }

        public int RemainingQuotaFlash2_0
        {
            get => _remainingQuotaFlash2_0;
            set
            {
                _remainingQuotaFlash2_0 = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsQuotaZero));
            }
        }

        public int RemainingQuotaFlash2_5
        {
            get => _remainingQuotaFlash2_5;
            set
            {
                _remainingQuotaFlash2_5 = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(IsQuotaZero));
            }
        }

        public bool IsQuotaZero => RemainingQuotaFlash2_0 == 0 || RemainingQuotaFlash2_5 == 0;

        public DateTime LastResetDate
        {
            get => _lastResetDate;
            set
            {
                _lastResetDate = value;
                OnPropertyChanged();
            }
        }

        public string QuotaStatusFlash2_0 => $"{RemainingQuotaFlash2_0}/{DailyQuotaFlash2_0}";
        public string QuotaStatusFlash2_5 => $"{RemainingQuotaFlash2_5}/{DailyQuotaFlash2_5}";

        public void UpdateQuotaStatus()
        {
            OnPropertyChanged(nameof(QuotaStatusFlash2_0));
            OnPropertyChanged(nameof(QuotaStatusFlash2_5));
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class TerminologySettingsViewModel : INotifyPropertyChanged
    {
        private string _stage1Prompt;
        private string _stage1Function;
        private string _stage2Prompt;
        private string _stage2Function;
        private int _stage1BatchSize = 1;
        private int _stage2BatchSize = 1;
        private AIModel _stage1AIModel = AIModel.GeminiFlash2_0;
        private AIModel _stage2AIModel = AIModel.GeminiFlash2_0;
        private string _stage3Prompt;
        private string _stage3Function;
        private int _stage3BatchSize = 1;
        private AIModel _stage3AIModel = AIModel.GeminiFlash2_0;

        public Array AvailableAIModels => Enum.GetValues(typeof(AIModel));

        public AIModel Stage1AIModel
        {
            get => _stage1AIModel;
            set
            {
                if (_stage1AIModel != value)
                {
                    _stage1AIModel = value;
                    OnPropertyChanged();
                }
            }
        }

        public AIModel Stage2AIModel
        {
            get => _stage2AIModel;
            set
            {
                if (_stage2AIModel != value)
                {
                    _stage2AIModel = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Stage1Prompt
        {
            get => _stage1Prompt;
            set
            {
                if (_stage1Prompt != value)
                {
                    _stage1Prompt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Stage1Function
        {
            get => _stage1Function;
            set
            {
                if (_stage1Function != value)
                {
                    _stage1Function = value;
                    OnPropertyChanged();
                }
            }
        }

        public int Stage1BatchSize
        {
            get => _stage1BatchSize;
            set
            {
                if (_stage1BatchSize != value)
                {
                    _stage1BatchSize = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Stage2Prompt
        {
            get => _stage2Prompt;
            set
            {
                if (_stage2Prompt != value)
                {
                    _stage2Prompt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Stage2Function
        {
            get => _stage2Function;
            set
            {
                if (_stage2Function != value)
                {
                    _stage2Function = value;
                    OnPropertyChanged();
                }
            }
        }

        public int Stage2BatchSize
        {
            get => _stage2BatchSize;
            set
            {
                if (_stage2BatchSize != value)
                {
                    _stage2BatchSize = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Stage3Prompt
        {
            get => _stage3Prompt;
            set
            {
                if (_stage3Prompt != value)
                {
                    _stage3Prompt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Stage3Function
        {
            get => _stage3Function;
            set
            {
                if (_stage3Function != value)
                {
                    _stage3Function = value;
                    OnPropertyChanged();
                }
            }
        }

        public int Stage3BatchSize
        {
            get => _stage3BatchSize;
            set
            {
                if (_stage3BatchSize != value)
                {
                    _stage3BatchSize = value;
                    OnPropertyChanged();
                }
            }
        }

        public AIModel Stage3AIModel
        {
            get => _stage3AIModel;
            set
            {
                if (_stage3AIModel != value)
                {
                    _stage3AIModel = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class TranslationSettingsViewModel : INotifyPropertyChanged
    {
        private AIModel _aiModel = AIModel.GeminiFlash2_5;
        private string _prompt;
        private string _prompt2;
        private string _function;
        private int _batchSize = 5;
        private TranslationQuality _quality = TranslationQuality.Eco;

        public Array AvailableAIModels => Enum.GetValues(typeof(AIModel));
        public Array AvailableQualities => Enum.GetValues(typeof(TranslationQuality));

        public AIModel AIModel
        {
            get => _aiModel;
            set
            {
                if (_aiModel != value)
                {
                    _aiModel = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Prompt
        {
            get => _prompt;
            set
            {
                if (_prompt != value)
                {
                    _prompt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Prompt2
        {
            get => _prompt2;
            set
            {
                if (_prompt2 != value)
                {
                    _prompt2 = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Function
        {
            get => _function;
            set
            {
                if (_function != value)
                {
                    _function = value;
                    OnPropertyChanged();
                }
            }
        }

        public int BatchSize
        {
            get => _batchSize;
            set
            {
                if (_batchSize != value)
                {
                    _batchSize = value;
                    OnPropertyChanged();
                }
            }
        }

        public TranslationQuality Quality
        {
            get => _quality;
            set
            {
                if (_quality != value)
                {
                    _quality = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class ContextSettingsViewModel : INotifyPropertyChanged
    {
        private AIModel _defaultAIModel = AIModel.GeminiFlash2_0;
        private int _defaultPageCount = 1;
        private int _defaultMaxDepth = 1;
        private string _prompt;
        private string _contextFunction;

        public Array AvailableAIModels => Enum.GetValues(typeof(AIModel));

        public AIModel DefaultAIModel
        {
            get => _defaultAIModel;
            set
            {
                if (_defaultAIModel != value)
                {
                    _defaultAIModel = value;
                    OnPropertyChanged();
                }
            }
        }

        public int DefaultPageCount
        {
            get => _defaultPageCount;
            set
            {
                if (_defaultPageCount != value)
                {
                    _defaultPageCount = value;
                    OnPropertyChanged();
                }
            }
        }

        public int DefaultMaxDepth
        {
            get => _defaultMaxDepth;
            set
            {
                if (_defaultMaxDepth != value)
                {
                    _defaultMaxDepth = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Prompt
        {
            get => _prompt;
            set
            {
                if (_prompt != value)
                {
                    _prompt = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ContextFunction
        {
            get => _contextFunction;
            set
            {
                if (_contextFunction != value)
                {
                    _contextFunction = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class SettingsViewModel : INotifyPropertyChanged
    {
        private string _serviceAccountJson;
        private string _newGeminiKey;
        private int _newKeyQuotaFlash2_0 = 1500;
        private int _newKeyQuotaFlash2_5 = 500;
        private AppSettings _appSettings;
        private readonly GoogleSheetsService _googleSheetsService;
        private readonly LogService _logService;

        private readonly GeminiService _geminiService;
        private GeminiKeyViewModel _selectedApiKey;
        private string _sourceTextColumnHeader;
        private string _textStatusColumnHeader;
        private bool _hardLog;
        private string _claudeApiKey;

        private ObservableCollection<GeminiKeyViewModel> _geminiKeys;
        public ObservableCollection<GeminiKeyViewModel> GeminiKeys
        {
            get => _geminiKeys;
            private set
            {
                if (_geminiKeys != null)
                {
                    _geminiKeys.CollectionChanged -= GeminiKeys_CollectionChanged;
                    foreach (var key in _geminiKeys)
                    {
                        key.PropertyChanged -= GeminiKey_PropertyChanged;
                    }
                }

                _geminiKeys = value;

                if (_geminiKeys != null)
                {
                    _geminiKeys.CollectionChanged += GeminiKeys_CollectionChanged;
                    foreach (var key in _geminiKeys)
                    {
                        key.PropertyChanged += GeminiKey_PropertyChanged;
                    }
                }

                OnPropertyChanged();
                UpdateTotalQuotas();
            }
        }

        private void GeminiKeys_CollectionChanged(object sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            if (e.NewItems != null)
            {
                foreach (GeminiKeyViewModel item in e.NewItems)
                {
                    item.PropertyChanged += GeminiKey_PropertyChanged;
                }
            }

            if (e.OldItems != null)
            {
                foreach (GeminiKeyViewModel item in e.OldItems)
                {
                    item.PropertyChanged -= GeminiKey_PropertyChanged;
                }
            }

            UpdateTotalQuotas();
        }

        private void GeminiKey_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            switch (e.PropertyName)
            {
                case nameof(GeminiKeyViewModel.DailyQuotaFlash2_0):
                case nameof(GeminiKeyViewModel.DailyQuotaFlash2_5):
                case nameof(GeminiKeyViewModel.RemainingQuotaFlash2_0):
                case nameof(GeminiKeyViewModel.RemainingQuotaFlash2_5):
                    UpdateTotalQuotas();
                    break;
            }
        }

        private void UpdateTotalQuotas()
        {
            OnPropertyChanged(nameof(TotalDailyQuotaFlash2_0));
            OnPropertyChanged(nameof(TotalDailyQuotaFlash2_5));
            OnPropertyChanged(nameof(TotalRemainingQuotaFlash2_0));
            OnPropertyChanged(nameof(TotalRemainingQuotaFlash2_5));
            OnPropertyChanged(nameof(TotalQuotaStatusFlash2_0));
            OnPropertyChanged(nameof(TotalQuotaStatusFlash2_5));
        }

        public int TotalDailyQuotaFlash2_0 => GeminiKeys?.Sum(k => k.DailyQuotaFlash2_0) ?? 0;
        public int TotalDailyQuotaFlash2_5 => GeminiKeys?.Sum(k => k.DailyQuotaFlash2_5) ?? 0;
        public int TotalRemainingQuotaFlash2_0 => GeminiKeys?.Sum(k => k.RemainingQuotaFlash2_0) ?? 0;
        public int TotalRemainingQuotaFlash2_5 => GeminiKeys?.Sum(k => k.RemainingQuotaFlash2_5) ?? 0;

        public string TotalQuotaStatusFlash2_0 => $"{TotalRemainingQuotaFlash2_0}/{TotalDailyQuotaFlash2_0}";
        public string TotalQuotaStatusFlash2_5 => $"{TotalRemainingQuotaFlash2_5}/{TotalDailyQuotaFlash2_5}";


        public bool HardLog
        {
            get => _hardLog;
            set
            {
                if (_hardLog != value)
                {
                    _hardLog = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SourceTextColumnHeader
        {
            get => _sourceTextColumnHeader;
            set
            {
                if (_sourceTextColumnHeader != value)
                {
                    _sourceTextColumnHeader = value;
                    OnPropertyChanged();
                }
            }
        }

        public string TextStatusColumnHeader
        {
            get => _textStatusColumnHeader;
            set
            {
                if (_textStatusColumnHeader != value)
                {
                    _textStatusColumnHeader = value;
                    OnPropertyChanged();
                }
            }
        }

        public TerminologySettingsViewModel TerminologySettings { get; }
        public TranslationSettingsViewModel TranslationSettings { get; }
        public ContextSettingsViewModel ContextSettings { get; }

        public GeminiKeyViewModel SelectedApiKey
        {
            get => _selectedApiKey;
            set
            {
                if (_selectedApiKey != value)
                {
                    _selectedApiKey = value;
                    OnPropertyChanged();
                    ((RelayCommand<GeminiKeyViewModel>)RemoveGeminiKeyCommand).NotifyCanExecuteChanged();
                }
            }
        }

        public string NewGeminiKey
        {
            get => _newGeminiKey;
            set
            {
                if (_newGeminiKey != value)
                {
                    _newGeminiKey = value;
                    OnPropertyChanged();
                }
            }
        }

        public int NewKeyQuotaFlash2_0
        {
            get => _newKeyQuotaFlash2_0;
            set
            {
                if (_newKeyQuotaFlash2_0 != value)
                {
                    _newKeyQuotaFlash2_0 = value;
                    OnPropertyChanged();
                }
            }
        }

        public int NewKeyQuotaFlash2_5
        {
            get => _newKeyQuotaFlash2_5;
            set
            {
                if (_newKeyQuotaFlash2_5 != value)
                {
                    _newKeyQuotaFlash2_5 = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ServiceAccountJson
        {
            get => _serviceAccountJson;
            set
            {
                if (_serviceAccountJson != value)
                {
                    _serviceAccountJson = value;
                    OnPropertyChanged();
                }
            }
        }

        public string ClaudeApiKey
        {
            get => _claudeApiKey;
            set
            {
                if (_claudeApiKey != value)
                {
                    _claudeApiKey = value;
                    OnPropertyChanged();
                }
            }
        }

        public ICommand SaveSettingsAsyncCommand { get; }
        public IAsyncRelayCommand AddGeminiKeyCommand { get; }
        public IRelayCommand<GeminiKeyViewModel> RemoveGeminiKeyCommand { get; }

        public SettingsViewModel(AppSettings appSettings, GoogleSheetsService googleSheetsService, GeminiService geminiService, LogService logService)
        {

            _logService = logService;
            _appSettings = appSettings ?? AppSettings.Load();
            _googleSheetsService = googleSheetsService;
            _geminiService = geminiService;
            ServiceAccountJson = _appSettings.General.ServiceAccountJson;
            ClaudeApiKey = _appSettings.General.ClaudeApiKey;
            SourceTextColumnHeader = _appSettings.General.SourceTextColumnHeader;
            TextStatusColumnHeader = _appSettings.General.TextStatusColumnHeader;
            HardLog = _appSettings.General.HardLog;

            // Initialize settings view models
            TerminologySettings = new TerminologySettingsViewModel
            {
                Stage1Prompt = _appSettings.Terminology.Stage1Prompt,
                Stage1Function = _appSettings.Terminology.Stage1Function,
                Stage1BatchSize = _appSettings.Terminology.Stage1BatchSize,
                Stage1AIModel = _appSettings.Terminology.Stage1AIModel,
                Stage2Prompt = _appSettings.Terminology.Stage2Prompt,
                Stage2Function = _appSettings.Terminology.Stage2Function,
                Stage2BatchSize = _appSettings.Terminology.Stage2BatchSize,
                Stage2AIModel = _appSettings.Terminology.Stage2AIModel,
                Stage3Prompt = _appSettings.Terminology.Stage3Prompt,
                Stage3Function = _appSettings.Terminology.Stage3Function,
                Stage3BatchSize = _appSettings.Terminology.Stage3BatchSize,
                Stage3AIModel = _appSettings.Terminology.Stage3AIModel
            };
            TranslationSettings = new TranslationSettingsViewModel
            {
                AIModel = _appSettings.Translation.AIModel,
                Prompt = _appSettings.Translation.Prompt,
                Prompt2 = _appSettings.Translation.Prompt2,
                Function = _appSettings.Translation.Function,
                BatchSize = _appSettings.Translation.BatchSize,
                Quality = _appSettings.Translation.Quality
            };
            ContextSettings = new ContextSettingsViewModel
            {
                DefaultAIModel = _appSettings.Context.DefaultAIModel,
                DefaultPageCount = _appSettings.Context.DefaultPageCount,
                DefaultMaxDepth = _appSettings.Context.DefaultMaxDepth,
                Prompt = _appSettings.Context.Prompt,
                ContextFunction = _appSettings.Context.ContextFunction
            };

            GeminiKeys = new ObservableCollection<GeminiKeyViewModel>();

            SaveSettingsAsyncCommand = new AsyncRelayCommand(SaveSettingsAsync);
            AddGeminiKeyCommand = new AsyncRelayCommand(ExecuteAddGeminiKeyAsync, CanExecuteAddGeminiKey);
            RemoveGeminiKeyCommand = new RelayCommand<GeminiKeyViewModel>(ExecuteRemoveGeminiKey, CanExecuteRemoveGeminiKey);

            PropertyChanged += (s, e) =>
            {
                if (e.PropertyName == nameof(NewGeminiKey) ||
                    e.PropertyName == nameof(NewKeyQuotaFlash2_0) ||
                    e.PropertyName == nameof(NewKeyQuotaFlash2_5))
                {
                    ((AsyncRelayCommand)AddGeminiKeyCommand).NotifyCanExecuteChanged();
                }
            };

            _geminiService.QuotaUpdated += OnGeminiQuotaUpdated;

            LoadGeminiKeys();
        }

        private void OnGeminiQuotaUpdated(object? sender, string apiKey)
        {
            var keyViewModel = GeminiKeys.FirstOrDefault(k => k.Key == apiKey);
            if (keyViewModel != null)
            {
                var updatedKey = _appSettings.General.GeminiApiKeys.FirstOrDefault(k => k.Key == apiKey);
                if (updatedKey != null)
                {
                    keyViewModel.RemainingQuotaFlash2_0 = updatedKey.RemainingQuotaFlash2_0;
                    keyViewModel.RemainingQuotaFlash2_5 = updatedKey.RemainingQuotaFlash2_5;
                }
            }
        }

        private void LoadGeminiKeys()
        {
            try
            {
                _logService.Info($"LoadGeminiKeys başlatılıyor. _appSettings.General.GeminiApiKeys sayısı: {_appSettings?.General?.GeminiApiKeys?.Count ?? -1}");

                if (_appSettings?.General?.GeminiApiKeys != null)
                {
                    foreach (var key in _appSettings.General.GeminiApiKeys)
                    {
                        // _logService.Info($"API anahtarı ViewModel'e dönüştürülüyor: {key.Key.Substring(0, Math.Min(5, key.Key.Length))}...");

                        // Eğer anahtar zaten eklenmiş ise atla
                        if (GeminiKeys.Any(k => k.Key == key.Key))
                        {
                            // _logService.Info($"Anahtar zaten eklenmiş, atlanıyor: {key.Key.Substring(0, Math.Min(5, key.Key.Length))}...");
                            continue;
                        }

                        var geminiKeyViewModel = new GeminiKeyViewModel
                        {
                            Key = key.Key,
                            DailyQuotaFlash2_0 = key.DailyQuotaFlash2_0,
                            DailyQuotaFlash2_5 = key.DailyQuotaFlash2_5,
                            RemainingQuotaFlash2_0 = key.RemainingQuotaFlash2_0,
                            RemainingQuotaFlash2_5 = key.RemainingQuotaFlash2_5,
                            LastResetDate = key.LastResetDate
                        };

                        GeminiKeys.Add(geminiKeyViewModel);
                        //_logService.Info($"ViewModel eklendi. GeminiKeys sayısı: {GeminiKeys.Count}");
                    }
                }
                else
                {
                    _logService.Warning("_appSettings veya _appSettings.General.GeminiApiKeys null.");
                }

                _logService.Success($"API anahtarları yüklendi. Toplam {GeminiKeys.Count} anahtar bulundu.");
                _logService.Info($"LoadGeminiKeys tamamlandı. Görünen anahtar sayısı: {GeminiKeys.Count}");
            }
            catch (Exception ex)
            {
                _logService.Warning("API anahtarları yüklenirken hata oluştu!");
                _logService.Error("API anahtarları yüklenirken hata", ex);
            }
        }

        private bool CanExecuteAddGeminiKey()
        {
            var canExecute = !string.IsNullOrWhiteSpace(NewGeminiKey) &&
                   NewKeyQuotaFlash2_0 > 0 &&
                   NewKeyQuotaFlash2_5 > 0 &&
                   !GeminiKeys.Any(k => k.Key == NewGeminiKey);
            return canExecute;
        }

        private async Task ExecuteAddGeminiKeyAsync()
        {
            try
            {
                if (CanExecuteAddGeminiKey())
                {
                    var newKey = new GeminiKeyViewModel
                    {
                        Key = NewGeminiKey,
                        DailyQuotaFlash2_0 = NewKeyQuotaFlash2_0,
                        DailyQuotaFlash2_5 = NewKeyQuotaFlash2_5,
                        RemainingQuotaFlash2_0 = NewKeyQuotaFlash2_0,
                        RemainingQuotaFlash2_5 = NewKeyQuotaFlash2_5,
                        LastResetDate = DateTime.Today
                    };

                    _appSettings.AddGeminiApiKey(NewGeminiKey, NewKeyQuotaFlash2_0, NewKeyQuotaFlash2_5);
                    await Task.Run(() => _appSettings.Save());
                    GeminiKeys.Add(newKey);

                    // Alanları temizle
                    NewGeminiKey = string.Empty;
                    NewKeyQuotaFlash2_0 = _newKeyQuotaFlash2_0;
                    NewKeyQuotaFlash2_5 = _newKeyQuotaFlash2_5;

                    _logService.Success("Gemini API anahtarı başarıyla eklendi.");
                }
                else
                {
                    _logService.Warning("API anahtarı eklenemedi. Lütfen girilen bilgileri kontrol edin.");
                }
            }
            catch (Exception ex)
            {
                _logService.Error("API anahtarı ekleme hatası", ex);
            }
        }

        private bool CanExecuteRemoveGeminiKey(GeminiKeyViewModel keyViewModel)
        {
            return keyViewModel != null;
        }

        private void ExecuteRemoveGeminiKey(GeminiKeyViewModel keyViewModel)
        {
            try
            {
                if (keyViewModel != null)
                {
                    var key = _appSettings.General.GeminiApiKeys.Find(k => k.Key == keyViewModel.Key);
                    if (key != null)
                    {
                        _appSettings.General.GeminiApiKeys.Remove(key);
                        _appSettings.Save();
                        GeminiKeys.Remove(keyViewModel);

                        _logService.Success("Gemini API anahtarı başarıyla kaldırıldı.");
                    }
                }
            }
            catch (Exception ex)
            {
                _logService.Error("API anahtarı kaldırma hatası", ex);
            }
        }

        private async Task SaveSettingsAsync()
        {
            try
            {
                // Save General Settings
                _appSettings.General.ServiceAccountJson = ServiceAccountJson;
                _appSettings.General.ClaudeApiKey = ClaudeApiKey;
                _appSettings.General.SourceTextColumnHeader = SourceTextColumnHeader;
                _appSettings.General.TextStatusColumnHeader = TextStatusColumnHeader;
                _appSettings.General.HardLog = HardLog;

                // Save Terminology Settings
                _appSettings.Terminology.Stage1Prompt = TerminologySettings.Stage1Prompt;
                _appSettings.Terminology.Stage1Function = TerminologySettings.Stage1Function;
                _appSettings.Terminology.Stage1BatchSize = TerminologySettings.Stage1BatchSize;
                _appSettings.Terminology.Stage1AIModel = TerminologySettings.Stage1AIModel;
                _appSettings.Terminology.Stage2Prompt = TerminologySettings.Stage2Prompt;
                _appSettings.Terminology.Stage2Function = TerminologySettings.Stage2Function;
                _appSettings.Terminology.Stage2BatchSize = TerminologySettings.Stage2BatchSize;
                _appSettings.Terminology.Stage2AIModel = TerminologySettings.Stage2AIModel;
                _appSettings.Terminology.Stage3Prompt = TerminologySettings.Stage3Prompt;
                _appSettings.Terminology.Stage3Function = TerminologySettings.Stage3Function;
                _appSettings.Terminology.Stage3BatchSize = TerminologySettings.Stage3BatchSize;
                _appSettings.Terminology.Stage3AIModel = TerminologySettings.Stage3AIModel;

                // Save Translation Settings
                _appSettings.Translation.AIModel = TranslationSettings.AIModel;
                _appSettings.Translation.Prompt = TranslationSettings.Prompt;
                _appSettings.Translation.Prompt2 = TranslationSettings.Prompt2;
                _appSettings.Translation.Function = TranslationSettings.Function;
                _appSettings.Translation.BatchSize = TranslationSettings.BatchSize;
                _appSettings.Translation.Quality = TranslationSettings.Quality;

                // Save Context Settings
                _appSettings.Context.DefaultAIModel = ContextSettings.DefaultAIModel;
                _appSettings.Context.DefaultPageCount = ContextSettings.DefaultPageCount;
                _appSettings.Context.DefaultMaxDepth = ContextSettings.DefaultMaxDepth;
                _appSettings.Context.Prompt = ContextSettings.Prompt;
                _appSettings.Context.ContextFunction = ContextSettings.ContextFunction;

                _appSettings.Save();
                _logService.Success("Ayarlar başarıyla kaydedildi");

                _logService.Info("Kimlik doğrulama deneniyor...");

                await _googleSheetsService.AuthenticateAsync(ServiceAccountJson);

                if (_googleSheetsService.IsAuthenticated)
                {
                    _logService.Info("Ayarlar kaydedildi ve kimlik doğrulama başarılı.");
                }
                else
                {
                    _logService.Warning("Ayarlar kaydedildi, fakat kimlik doğrulama başarısız. Konsol hatalarını kontrol edin.");
                }
            }
            catch (Exception ex)
            {
                _logService.Error("Ayarlar kaydetme veya kimlik doğrulama sırasında hata oluştu", ex);
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}