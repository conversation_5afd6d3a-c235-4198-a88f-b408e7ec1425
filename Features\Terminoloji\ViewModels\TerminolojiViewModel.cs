using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Threading;
using System.Threading.Tasks;
using Catalyst;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using TranslationAgent.Helpers;
using TranslationAgent.Features.GoogleSheets.ViewModels;
using TranslationAgent.Features.Terminoloji.Models;
using TranslationAgent.Services;
using TranslationAgent.Shared.ViewModels;
using SmartComponents.LocalEmbeddings;
using TranslationAgent.Features.Terminoloji.ViewModels;
using TranslationAgent.Features.Ceviri.ViewModels; // Add this line

namespace TranslationAgent.Features.Terminoloji.ViewModels
{
    public partial class TerminolojiViewModel : ObservableObject, IDisposable
    {
        public GoogleSheetsService GoogleSheetsService;
        [ObservableProperty]
        private GoogleSheetsPanelViewModel _googleSheetsPanelVm;
        public CeviriViewModel CeviriViewModel { get; }
        public GeminiService GeminiService;
        public LogService LogService;
        private AppSettings _appSettings;
        public AppSettings AppSettings => _appSettings;
        [ObservableProperty]
        private Pipeline _nlp;
        private readonly PhaseOneProcessor _phaseOneProcessor;
        private readonly PhaseTwoProcessor _phaseTwoProcessor;
        private readonly PhaseThreeProcessor _phaseThreeProcessor; // Add this line
        private readonly LocalEmbedder _localEmbedder;

        // Yeni Özellikler
        [ObservableProperty]
        private bool _isPhaseOneActive = true; // True: Aşama 1
        [ObservableProperty]
        private bool _isPhaseTwoActive = false; // True: Aşama 2
        [ObservableProperty]
        private bool _isPhaseThreeActive = false; // True: Aşama 3

        [ObservableProperty]
        private LogStatisticsPanelViewModel _logStatisticsPanelVm;

        [ObservableProperty]
        private bool _isProcessing = false;

        [ObservableProperty]
        private bool _isPaused = false;

        [ObservableProperty]
        private string _pauseToggleButtonText = "Duraklat";


        // Aşama 1 istatistikleri için ek özellikler
        [ObservableProperty]
        private int _itemsProcessedCount = 0;

        [ObservableProperty]
        private int _totalItemsToProcess = 0;

        // Model İstatistikleri için yeni özellikler
        [ObservableProperty]
        private int _currentModelDailyQuota = 0;

        [ObservableProperty]
        private int _currentModelRemainingQuota = 0;

        [ObservableProperty]
        private string _requiredQuota = string.Empty;

        [ObservableProperty]
        private bool _isQuotaExceeded = false;

        [ObservableProperty]
        private string _estimatedRemainingTime = string.Empty;

        [ObservableProperty]
        private DateTime _processStartTime;

        [ObservableProperty]
        private bool _showRemainingTime = false;

        // CancellationToken için yeni özellikler
        private CancellationTokenSource? _cancellationTokenSource;
        public CancellationToken CancellationToken => _cancellationTokenSource?.Token ?? CancellationToken.None;

        [ObservableProperty]
        private bool _isOperationInProgress = false; // Butonları deaktif etmek için

        // Text Context Column özellikleri
        [ObservableProperty]
        private ObservableCollection<string> _selectedTextContextColumns = new();

        [ObservableProperty]
        private ObservableCollection<string> _availableTextContextColumns = new();

        /// <summary>
        /// Text Context Column ayarının görünür olup olmadığını kontrol eder
        /// </summary>
        public bool IsTextContextColumnVisible => GoogleSheetsPanelVm?.TextData?.Any(t => t.HasKey || t.HasNamespace) ?? false;

        // Düzenleme için yeni özellikler
        [ObservableProperty]
        private TermViewModel? _selectedTerm;

        [ObservableProperty]
        private bool _isEditMode = false;

        [ObservableProperty]
        private string _editEN = string.Empty;

        [ObservableProperty]
        private string _editTR = string.Empty;

        [ObservableProperty]
        private string _editKategori = string.Empty;

        [ObservableProperty]
        private string _editBilgi = string.Empty;

        [ObservableProperty]
        private TermStatus _editStatus; // Yeni: Düzenleme modunda terim durumu

        // Yeni terim ekleme için özellikler
        [ObservableProperty]
        private bool _isAddMode = false;

        [ObservableProperty]
        private string _newEN = string.Empty;

        [ObservableProperty]
        private string _newTR = string.Empty;

        [ObservableProperty]
        private string _newKategori = string.Empty;

        [ObservableProperty]
        private string _newBilgi = string.Empty;

        // Filtrelenmiş metinleri kullanma seçeneği
        [ObservableProperty]
        private bool _useFilteredTexts = false;

        public IAsyncRelayCommand TestGeminiCommand { get; }

        public TerminolojiViewModel(
            GoogleSheetsService googleSheetsService,
            GoogleSheetsPanelViewModel googleSheetsPanelVm,
            CeviriViewModel ceviriViewModel,
            GeminiService geminiService,
            LogService logService,
            AppSettings appSettings,
            Pipeline nlp,
            LocalEmbedder localEmbedder)
        {
            _googleSheetsPanelVm = googleSheetsPanelVm;
            GoogleSheetsService = googleSheetsService;
            CeviriViewModel = ceviriViewModel;
            GeminiService = geminiService;
            LogService = logService;
            _appSettings = appSettings;
            _nlp = nlp;
            _phaseOneProcessor = new PhaseOneProcessor(this);
            _phaseTwoProcessor = new PhaseTwoProcessor(this);
            _phaseThreeProcessor = new PhaseThreeProcessor(this); // Yeni
            _localEmbedder = localEmbedder;

            // Log ve İstatistik Panel ViewModel'ini başlat - Terminoloji için Main, Stage1, Stage2 loglarını dinle
            _logStatisticsPanelVm = new LogStatisticsPanelViewModel(logService,
                LogService.LogState.Main,
                LogService.LogState.Stage1,
                LogService.LogState.Stage2,
                LogService.LogState.Stage3); // Yeni

            // Terminoloji sayfası için özel istatistik yapısını ayarla
            _logStatisticsPanelVm.SetCustomLabels("Terminoloji İstatistikleri", "Genel İlerleme:");
            _logStatisticsPanelVm.SetStatisticItems(
                ("Toplam Bulunan Terim:", "0"),
                ("Çevrilen Terim:", "0"),
                ("İncelemede:", "0")
            );

            InitializeCommands();
            LogService.Info("Terminoloji modülü başlatıldı.", true);

            UpdateModelQuotaStatistics();
            GoogleSheetsPanelVm.PropertyChanged += OnGoogleSheetsPanelPropertyChanged;

            // TerminologyData collection'ındaki değişiklikleri dinle
            GoogleSheetsPanelVm.TerminologyData.CollectionChanged += TerminologyData_CollectionChanged;

            // Text Context Column'ları güncelle
            UpdateTextContextColumns();

            // Model değişikliklerini dinle
            if (_appSettings.Terminology is INotifyPropertyChanged terminologySettings)
            {
                terminologySettings.PropertyChanged += OnTerminologySettingsPropertyChanged;
            }
        }

        private void InitializeCommands()
        {
            ProcessStartCommand = new RelayCommand(StartProcessing, () => !IsProcessing && !IsOperationInProgress);
            ProcessPauseToggleCommand = new RelayCommand(PauseOrResumeProcessing, () => IsProcessing && !IsOperationInProgress);
            ProcessStopCommand = new RelayCommand(StopProcessing, () => IsProcessing);

            // Düzenleme komutları
            StartEditCommand = new RelayCommand<TermViewModel>(StartEdit, term => term != null && !IsEditMode && !IsAddMode && !IsProcessing);
            SaveEditCommand = new AsyncRelayCommand(SaveEdit, () => IsEditMode && !IsProcessing);
            CancelEditCommand = new RelayCommand(CancelEdit, () => IsEditMode);
            DeleteTermCommand = new AsyncRelayCommand<TermViewModel>(DeleteTerm, term => term != null && !IsEditMode && !IsAddMode && !IsProcessing);
            RefreshTerminologyDataCommand = new AsyncRelayCommand(RefreshTerminologyData, () => !IsProcessing && !IsEditMode && !IsAddMode);

            // Yeni terim ekleme komutları
            StartAddCommand = new RelayCommand(StartAdd, () => !IsEditMode && !IsAddMode && !IsProcessing);
            SaveAddCommand = new AsyncRelayCommand(SaveAdd, () => IsAddMode && !IsProcessing);
            CancelAddCommand = new RelayCommand(CancelAdd, () => IsAddMode);
        }

        // Yeni Komutlar
        public IRelayCommand ProcessStartCommand { get; private set; }
        public IRelayCommand ProcessPauseToggleCommand { get; private set; }
        public IRelayCommand ProcessStopCommand { get; private set; }

        // Düzenleme komutları
        public IRelayCommand<TermViewModel> StartEditCommand { get; private set; }
        public IAsyncRelayCommand SaveEditCommand { get; private set; }
        public IRelayCommand CancelEditCommand { get; private set; }
        public IAsyncRelayCommand<TermViewModel> DeleteTermCommand { get; private set; }
        public IAsyncRelayCommand RefreshTerminologyDataCommand { get; private set; }

        // Yeni terim ekleme komutları
        public IRelayCommand StartAddCommand { get; private set; }
        public IAsyncRelayCommand SaveAddCommand { get; private set; }
        public IRelayCommand CancelAddCommand { get; private set; }

        private async void StartProcessing()
        {
            // Yeni CancellationTokenSource oluştur
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = new CancellationTokenSource();

            IsProcessing = true;
            IsPaused = false;
            IsOperationInProgress = false;
            PauseToggleButtonText = "Duraklat";
            ProcessStartTime = DateTime.Now;
            EstimatedRemainingTime = string.Empty;
            ShowRemainingTime = true;
            NotifyRelatedCommands();

            LogService.Info("İşlem başlatıldı.", true);
            LogStatisticsPanelVm.ClearLogs();

            ItemsProcessedCount = 0;
            TotalItemsToProcess = 0;

            var totalTerms = GoogleSheetsPanelVm.TerminologyData.Count;
            var translatedTerms = GoogleSheetsPanelVm.TerminologyData.Count(t =>
                !string.IsNullOrWhiteSpace(t.TR));
            var reviewTerms = totalTerms - translatedTerms;

            // İstatistikleri güncelle
            LogStatisticsPanelVm.SetStatisticItems(
                ("Toplam Bulunan Terim:", totalTerms.ToString()),
                ("Çevrilen Terim:", translatedTerms.ToString()),
                ("İncelemede:", reviewTerms.ToString())
            );

            // Progress bar'ı güncelle
            var progress = totalTerms > 0 ? (double)translatedTerms / totalTerms * 100 : 0;
            LogStatisticsPanelVm.SetProgressBar(progress);

            // Model istatistiklerini güncelle
            UpdateModelQuotaStatistics();

            try
            {
                if (!GoogleSheetsPanelVm.IsTextDataFetched)
                {
                    LogService.Error("Lütfen Google Sheets panelinden 'Metinler' verilerini getirin.", null, true);
                    IsProcessing = false;
                }
                else if (!GoogleSheetsPanelVm.IsTerminologyDataFetched)
                {
                    LogService.Error("Lütfen Google Sheets panelinden 'Terimler' verilerini getirin.", null, true);
                    IsProcessing = false;
                }
                else if (IsPhaseOneActive)
                {
                    if (!GoogleSheetsPanelVm.TextData.Any())
                    {
                        LogService.Error($"'{GoogleSheetsPanelVm.SelectedTextSheet.SheetName}' sayfasında işlenecek metin bulunamadı. Lütfen Google Sheets panelinden metinleri getirin.", null, true);
                        IsProcessing = false;
                    }

                    else
                    {
                        await _phaseOneProcessor.ProcessAsync();
                    }
                }
                else if (IsPhaseTwoActive) // Aşama 2 aktifse
                {
                    if (!GoogleSheetsPanelVm.TextData.Any() || !GoogleSheetsPanelVm.TerminologyData.Any())
                    {
                        LogService.Error($"'{GoogleSheetsPanelVm.SelectedTextSheet.SheetName}' sayfasında işlenecek metin bulunamadı veya '{GoogleSheetsPanelVm.SelectedTerminologySheet.SheetName}' sayfasında terim bulunamadı. Lütfen Google Sheets panelinden verileri getirin.", null, true);
                        IsProcessing = false;
                    }
                    else
                    {
                        await _phaseTwoProcessor.ProcessAsync();
                    }
                }
                else if (IsPhaseThreeActive) // Aşama 3 aktifse
                {
                    if (!GoogleSheetsPanelVm.TextData.Any())
                    {
                        LogService.Error($"'{GoogleSheetsPanelVm.SelectedTextSheet.SheetName}' sayfasında işlenecek metin bulunamadı. Lütfen Google Sheets panelinden metinleri getirin.", null, true);
                        IsProcessing = false;
                    }
                    else if (!GoogleSheetsPanelVm.IsContextDataFetched)
                    {
                        LogService.Error("Lütfen Google Sheets panelinden 'Bağlamlar' verilerini getirin.", null, true);
                        IsProcessing = false;
                    }
                    else
                    {
                        await _phaseThreeProcessor.ProcessAsync(); // Yeni

                    }
                }

                // Normal tamamlanma - durumu sıfırla
                IsProcessing = false;
                IsPaused = false;
                IsOperationInProgress = false;
                PauseToggleButtonText = "Duraklat";
                ShowRemainingTime = false;
                ItemsProcessedCount = 0;
                TotalItemsToProcess = 0;
                // işlem bittiğinde istatistikleri güncelle
                UpdateStatistics();
                LogService.Info("İşlem tamamlandı.", true);
                NotifyRelatedCommands();
            }
            catch (OperationCanceledException)
            {
                // İptal edildi - durumu sıfırla
                IsProcessing = false;
                IsPaused = false;
                IsOperationInProgress = false;
                PauseToggleButtonText = "Duraklat";
                ShowRemainingTime = false;
                LogService.Info("İşlem iptal edildi.", true);
                NotifyRelatedCommands();
            }
            catch (Exception ex)
            {
                LogService.Error("İşlem sırasında beklenmeyen hata oluştu", ex, true);
                // Hata durumunda da durumu sıfırla
                IsProcessing = false;
                IsPaused = false;
                IsOperationInProgress = false;
                PauseToggleButtonText = "Duraklat";
                ShowRemainingTime = false;
                NotifyRelatedCommands();
            }
        }

        private void PauseOrResumeProcessing()
        {
            if (IsOperationInProgress)
                return; // Eğer bir işlem devam ediyorsa duraklat/devam et butonunu deaktif et

            IsPaused = !IsPaused;
            PauseToggleButtonText = IsPaused ? "Devam Et" : "Duraklat";

            if (IsPaused)
            {
                IsOperationInProgress = true;
                LogService.Warning("İşlem duraklatılıyor... Mevcut işlem tamamlanana kadar bekleyiniz...", true);
                NotifyRelatedCommands();
            }
            else
            {
                LogService.Info("İşlem devam edecek.", true);
                // IsOperationInProgress = false; // Bu HandlePauseIfNeeded metodunda yapılacak
            }
        }

        private void StopProcessing()
        {
            LogService.Warning("İşlem durduruluyor...", true);

            // CancellationToken'ı iptal et
            _cancellationTokenSource?.Cancel();

            // Butonları deaktif et - işlem tamamen durana kadar
            IsOperationInProgress = true;
            NotifyRelatedCommands();

            // Not: Gerçek durum sıfırlama OperationCanceledException catch bloğunda yapılacak
        }

        public void NotifyRelatedCommands()
        {
            ((RelayCommand)ProcessStartCommand).NotifyCanExecuteChanged();
            ((RelayCommand)ProcessPauseToggleCommand).NotifyCanExecuteChanged();
            ((RelayCommand)ProcessStopCommand).NotifyCanExecuteChanged();
            NotifyAllCommands();
        }

        partial void OnIsProcessingChanged(bool value)
        {
            NotifyRelatedCommands();
        }

        partial void OnIsOperationInProgressChanged(bool value)
        {
            NotifyRelatedCommands();
        }

        partial void OnIsEditModeChanged(bool value)
        {
            NotifyRelatedCommands();
        }

        partial void OnIsAddModeChanged(bool value)
        {
            NotifyRelatedCommands();
        }

        partial void OnIsPhaseOneActiveChanged(bool value)
        {
            if (value) // Aşama 1 aktifse diğerlerini kapat
            {
                IsPhaseTwoActive = false;
                IsPhaseThreeActive = false;
            }
            UpdatePhaseRelatedProperties();
        }

        partial void OnIsPhaseTwoActiveChanged(bool value)
        {
            if (value) // Aşama 2 aktifse diğerlerini kapat
            {
                IsPhaseOneActive = false;
                IsPhaseThreeActive = false;
            }
            UpdatePhaseRelatedProperties();
        }

        partial void OnIsPhaseThreeActiveChanged(bool value)
        {
            if (value) // Aşama 3 aktifse diğerlerini kapat
            {
                IsPhaseOneActive = false;
                IsPhaseTwoActive = false;
            }
            UpdatePhaseRelatedProperties();
        }

        private void UpdatePhaseRelatedProperties()
        {
            ShowRemainingTime = false;
            EstimatedRemainingTime = string.Empty;
            ItemsProcessedCount = 0;
            TotalItemsToProcess = 0;

            // Faz değiştiğinde istatistikleri güncelle
            UpdateStatistics();
            UpdateModelQuotaStatistics();
            NotifyRelatedCommands();
        }

        private void OnGoogleSheetsPanelPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(GoogleSheetsPanelViewModel.IsTerminologyDataFetched) &&
                GoogleSheetsPanelVm.IsTerminologyDataFetched)
            {
                UpdateStatistics();
                UpdateModelQuotaStatistics();
            }
            else if (e.PropertyName == nameof(GoogleSheetsPanelViewModel.IsTextDataFetched) &&
                GoogleSheetsPanelVm.IsTextDataFetched)
            {
                UpdateTextContextColumns();
                OnPropertyChanged(nameof(IsTextContextColumnVisible));
                UpdateModelQuotaStatistics();
            }
            else if (e.PropertyName == nameof(GoogleSheetsPanelViewModel.TextData) &&
                GoogleSheetsPanelVm.TextData != null)
            {
                UpdateModelQuotaStatistics();
            }
            else if (e.PropertyName == nameof(GoogleSheetsPanelViewModel.TerminologyData) &&
                GoogleSheetsPanelVm.TerminologyData != null)
            {
                UpdateStatistics();
                UpdateModelQuotaStatistics();
            }
            else if (e.PropertyName == nameof(GoogleSheetsPanelViewModel.SelectedSpreadsheet) &&
                GoogleSheetsPanelVm.SelectedSpreadsheet != null)
            {
                // Yeni e-tablo seçildiğinde istatistikleri sıfırla
                LogStatisticsPanelVm.ResetStatistics();
                UpdateModelQuotaStatistics();
            }
        }

        public void UpdateStatistics()
        {
            try
            {
                if (GoogleSheetsPanelVm.TerminologyData == null || !GoogleSheetsPanelVm.TerminologyData.Any())
                {
                    LogService.Info("Terimce sayfasında veri bulunamadı.", true, LogService.LogState.Main);
                    return;
                }

                var totalTerms = GoogleSheetsPanelVm.TerminologyData.Count;
                var translatedTerms = GoogleSheetsPanelVm.TerminologyData.Count(t =>
                    !string.IsNullOrWhiteSpace(t.TR));
                var reviewTerms = totalTerms - translatedTerms;

                // İstatistikleri güncelle
                LogStatisticsPanelVm.SetStatisticItems(
                    ("Toplam Bulunan Terim:", totalTerms.ToString()),
                    ("Çevrilen Terim:", translatedTerms.ToString()),
                    ("İncelemede:", reviewTerms.ToString())
                );

                // Progress bar'ı güncelle
                var progress = totalTerms > 0 ? (double)ItemsProcessedCount / TotalItemsToProcess * 100 : 0;
                LogStatisticsPanelVm.SetProgressBar(progress);

                if (IsProcessing && ItemsProcessedCount > 0)
                {
                    var elapsedTime = DateTime.Now - ProcessStartTime;
                    var itemsPerMinute = ItemsProcessedCount / elapsedTime.TotalMinutes;
                    var remainingItems = TotalItemsToProcess - ItemsProcessedCount;

                    if (itemsPerMinute > 0)
                    {
                        var estimatedRemainingMinutes = remainingItems / itemsPerMinute;
                        var remainingTime = TimeSpan.FromMinutes(estimatedRemainingMinutes);
                        if (remainingTime.TotalHours >= 1)
                        {
                            EstimatedRemainingTime = $"{(int)remainingTime.TotalHours} saat {remainingTime.Minutes} dk";
                        }
                        else
                        {
                            EstimatedRemainingTime = $"{remainingTime.Minutes} dk";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"İstatistikler güncellenirken hata oluştu: {ex.Message}", ex, true);
            }
        }

        public void UpdateModelQuotaStatistics()
        {
            try
            {
                var selectedModel = IsPhaseOneActive ? AppSettings.Terminology.Stage1AIModel : AppSettings.Terminology.Stage2AIModel;
                var batchSize = IsPhaseOneActive ? AppSettings.Terminology.Stage1BatchSize : AppSettings.Terminology.Stage2BatchSize;

                // Seçili modele göre günlük toplam ve kalan kotayı al
                if (selectedModel == AIModel.GeminiFlash2_0)
                {
                    CurrentModelDailyQuota = AppSettings.General.GeminiApiKeys.Sum(k => k.DailyQuotaFlash2_0);
                    CurrentModelRemainingQuota = AppSettings.General.GeminiApiKeys.Sum(k => k.RemainingQuotaFlash2_0);
                }
                else
                {
                    CurrentModelDailyQuota = AppSettings.General.GeminiApiKeys.Sum(k => k.DailyQuotaFlash2_5);
                    CurrentModelRemainingQuota = AppSettings.General.GeminiApiKeys.Sum(k => k.RemainingQuotaFlash2_5);
                }


                int requiredQuotaCount = (int)Math.Ceiling((double)TotalItemsToProcess / batchSize);
                IsQuotaExceeded = requiredQuotaCount > CurrentModelDailyQuota;

                RequiredQuota = $"{requiredQuotaCount}{(IsQuotaExceeded ? " !" : "")}";

                LogService.Info($"Model istatistikleri güncellendi: Günlük Kota: {CurrentModelDailyQuota}, Kalan: {CurrentModelRemainingQuota}, İhtiyaç: {requiredQuotaCount}");
            }
            catch (Exception ex)
            {
                LogService.Error("Model istatistikleri güncellenirken hata oluştu", ex, true);
            }
        }

        private void OnTerminologySettingsPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(TerminologySettings.Stage1AIModel) ||
                e.PropertyName == nameof(TerminologySettings.Stage2AIModel) ||
                e.PropertyName == nameof(TerminologySettings.Stage1BatchSize) ||
                e.PropertyName == nameof(TerminologySettings.Stage2BatchSize))
            {
                UpdateModelQuotaStatistics();
            }
        }

        private void TerminologyData_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            // Collection değişikliklerini dinle (inline editing kaldırıldığı için sadece event dinleme amaçlı)
        }

        // OnTermPropertyChanged metodu kaldırıldı - artık inline edit kullanıyoruz

        #region Edit Operations (Panel Düzenleme İşlemleri)

        /// <summary>
        /// Düzenleme modunu başlatır ve seçili terimin bilgilerini düzenleme alanlarına yükler
        /// </summary>
        private void StartEdit(TermViewModel? term)
        {
            if (term == null) return;

            SelectedTerm = term;
            IsEditMode = true;

            // Mevcut değerleri düzenleme alanlarına yükle
            LoadTermToEditFields(term);
            EditStatus = term.Status; // SelectedTerm'in Status'unu EditStatus'a yükle

            LogService.Info($"'{term.EN}' terimi düzenleme moduna alındı.", true);
            NotifyEditCommands();
        }

        /// <summary>
        /// Terimin değerlerini düzenleme alanlarına yükler
        /// </summary>
        private void LoadTermToEditFields(TermViewModel term)
        {
            EditEN = term.EN ?? string.Empty;
            EditTR = term.TR ?? string.Empty;
            EditKategori = term.Kategori ?? string.Empty;
            EditBilgi = term.Bilgi ?? string.Empty;
        }

        /// <summary>
        /// Düzenleme panelindeki değişiklikleri kaydeder
        /// </summary>
        private async Task SaveEdit()
        {
            if (SelectedTerm == null) return;

            try
            {
                // Orijinal değerleri al
                var originalTerm = TermManagementHelper.CloneTerm(SelectedTerm);

                // Düzenlenmiş terim verilerini al
                var updatedTerm = new TermViewModel
                {
                    ID = originalTerm.ID, // ID'yi koru
                    EN = EditEN,
                    TR = EditTR,
                    Kategori = EditKategori,
                    Bilgi = EditBilgi,
                    Status = EditStatus // EditStatus'u SelectedTerm.Status'a geri ata
                };

                // Google Sheets'e güncellemeyi gönder
                await TermManagementHelper.UpdateTermInGoogleSheets(
                    GoogleSheetsService, GoogleSheetsPanelVm, originalTerm, updatedTerm, LogService);

                SelectedTerm.Lemma = await Task.Run(() => TextProcessingHelper.ProcessTextToLemma(SelectedTerm.EN, Nlp));
                SelectedTerm.Vector = await Task.Run(() => _localEmbedder.Embed(SelectedTerm.EN));

                // Local veriyi güncelle
                TermManagementHelper.ApplyEditChangesToTerm(SelectedTerm, EditEN, EditTR, EditKategori, EditBilgi, EditStatus);

                LogService.Info($"'{originalTerm.EN}' terimi güncellendi.", true);

                // Düzenleme modunu kapat
                ExitEditMode();
            }
            catch (Exception ex)
            {
                LogService.Error($"Terim güncellenirken hata oluştu: {ex.Message}", ex, true);
            }
        }

        /// <summary>
        /// Düzenleme işlemini iptal eder
        /// </summary>
        private void CancelEdit()
        {
            ExitEditMode();
            LogService.Info("Düzenleme iptal edildi.", true);
        }

        // DisableTermPropertyChanged ve EnableTermPropertyChanged metodları kaldırıldı - artık kullanmıyoruz

        /// <summary>
        /// Düzenleme modundan çıkar
        /// </summary>
        private void ExitEditMode()
        {
            IsEditMode = false;
            SelectedTerm = null;
            ClearEditFields();
            UpdateStatistics();
            NotifyEditCommands();
        }

        /// <summary>
        /// Düzenleme alanlarını temizler
        /// </summary>
        private void ClearEditFields()
        {
            EditEN = string.Empty;
            EditTR = string.Empty;
            EditKategori = string.Empty;
            EditBilgi = string.Empty;
        }

        #endregion



        #region Term Management Operations (Terim Yönetimi İşlemleri)

        /// <summary>
        /// Terimi hem Google Sheets'ten hem de yerel listeden siler
        /// </summary>
        private async Task DeleteTerm(TermViewModel? term)
        {
            if (term == null) return;

            try
            {
                // Google Sheets'ten sil
                await TermManagementHelper.DeleteTermFromGoogleSheets(GoogleSheetsService, GoogleSheetsPanelVm, term, LogService);

                // Local veriyi güncelle
                GoogleSheetsPanelVm.TerminologyData.Remove(term);
                UpdateStatistics();
                LogService.Info($"'{term.EN}' terimi başarıyla silindi.", true);
            }
            catch (Exception ex)
            {
                LogService.Error($"Terim silinirken hata oluştu: {ex.Message}", ex, true);
            }
        }

        /// <summary>
        /// Terminoloji verilerini Google Sheets'ten yeniler
        /// </summary>
        private async Task RefreshTerminologyData()
        {
            try
            {
                LogService.Info("Terminoloji verileri Google Sheets'ten yenileniyor...", true);
                await GoogleSheetsPanelVm.FetchTerminologyDataCommand.ExecuteAsync(null);
                LogService.Info("Terminoloji verileri başarıyla yenilendi.", true);
            }
            catch (Exception ex)
            {
                LogService.Error($"Terminoloji verileri yenilenirken hata oluştu: {ex.Message}", ex, true);
            }
        }

        #endregion

        #region Add Operations (Yeni Terim Ekleme İşlemleri)

        /// <summary>
        /// Yeni terim ekleme modunu başlatır
        /// </summary>
        private void StartAdd()
        {
            IsAddMode = true;
            ClearAddFields();
            LogService.Info("Yeni terim ekleme modu başlatıldı.", true);
            NotifyAllCommands();
        }

        /// <summary>
        /// Yeni terimi kaydeder
        /// </summary>
        private async Task SaveAdd()
        {
            try
            {
                // Validasyon
                if (string.IsNullOrWhiteSpace(NewEN))
                {
                    LogService.Warning("Terim alanı boş olamaz.", true);
                    return;
                }

                var newId = GoogleSheetsPanelVm.TerminologyData.Any() ? GoogleSheetsPanelVm.TerminologyData.Max(t => t.ID) + 1 : 1;

                // Yeni terim oluştur
                var newTerm = TermManagementHelper.SanitizeTerm(new TermViewModel
                {
                    ID = newId,
                    EN = NewEN,
                    TR = NewTR,
                    Kategori = NewKategori,
                    Bilgi = NewBilgi,
                    Status = TermStatus.İncelemede // Yeni terim varsayılan olarak "İncelemede"
                });

                // Google Sheets'e ekle
                await TermManagementHelper.AddTermToGoogleSheets(GoogleSheetsService, GoogleSheetsPanelVm, newTerm, LogService);

                // Vektörü oluştur
                newTerm.Vector = await Task.Run(() => _localEmbedder.Embed(newTerm.EN));
                newTerm.Lemma = await Task.Run(() => TextProcessingHelper.ProcessTextToLemma(newTerm.EN, Nlp));

                // Local listeye ekle
                GoogleSheetsPanelVm.TerminologyData.Add(newTerm);

                // Ekleme modunu kapat
                ExitAddMode();

                LogService.Info($"'{newTerm.EN}' terimi başarıyla eklendi.", true);
            }
            catch (Exception ex)
            {
                LogService.Error($"Yeni terim eklenirken hata oluştu: {ex.Message}", ex, true);
            }
        }

        /// <summary>
        /// Yeni terim ekleme işlemini iptal eder
        /// </summary>
        private void CancelAdd()
        {
            ExitAddMode();
            LogService.Info("Yeni terim ekleme iptal edildi.", true);
        }



        /// <summary>
        /// Ekleme modundan çıkar
        /// </summary>
        private void ExitAddMode()
        {
            IsAddMode = false;
            ClearAddFields();
            UpdateStatistics();
            NotifyAllCommands();
        }

        /// <summary>
        /// Ekleme alanlarını temizler
        /// </summary>
        private void ClearAddFields()
        {
            NewEN = string.Empty;
            NewTR = string.Empty;
            NewKategori = string.Empty;
            NewBilgi = string.Empty;
        }

        #endregion

        #region Command Notifications (Komut Bildirimleri)

        /// <summary>
        /// Düzenleme komutlarının durumunu günceller
        /// </summary>
        private void NotifyEditCommands()
        {
            ((RelayCommand<TermViewModel>)StartEditCommand).NotifyCanExecuteChanged();
            ((AsyncRelayCommand)SaveEditCommand).NotifyCanExecuteChanged();
            ((RelayCommand)CancelEditCommand).NotifyCanExecuteChanged();
            ((AsyncRelayCommand<TermViewModel>)DeleteTermCommand).NotifyCanExecuteChanged();
            ((AsyncRelayCommand)RefreshTerminologyDataCommand).NotifyCanExecuteChanged();
        }

        /// <summary>
        /// Ekleme komutlarının durumunu günceller
        /// </summary>
        private void NotifyAddCommands()
        {
            ((RelayCommand)StartAddCommand).NotifyCanExecuteChanged();
            ((AsyncRelayCommand)SaveAddCommand).NotifyCanExecuteChanged();
            ((RelayCommand)CancelAddCommand).NotifyCanExecuteChanged();
        }

        /// <summary>
        /// Tüm terminoloji komutlarının durumunu günceller
        /// </summary>
        private void NotifyAllCommands()
        {
            NotifyEditCommands();
            NotifyAddCommands();
        }

        #endregion

        #region Text Context Column Operations (Metin Bağlam Sütunu İşlemleri)

        /// <summary>
        /// Text Context Column seçeneklerini günceller
        /// </summary>
        private void UpdateTextContextColumns()
        {
            try
            {
                AvailableTextContextColumns.Clear();

                if (GoogleSheetsPanelVm?.TextData != null && GoogleSheetsPanelVm.TextData.Any())
                {
                    // Key sütunu varsa ekle
                    if (GoogleSheetsPanelVm.TextData.Any(t => t.HasKey))
                    {
                        AvailableTextContextColumns.Add("Key");
                    }

                    // Namespace sütunu varsa ekle
                    if (GoogleSheetsPanelVm.TextData.Any(t => t.HasNamespace))
                    {
                        AvailableTextContextColumns.Add("Namespace");
                    }

                    // Seçili sütunları güncelle - mevcut olmayan sütunları kaldır
                    var itemsToRemove = SelectedTextContextColumns.Where(col => !AvailableTextContextColumns.Contains(col)).ToList();
                    foreach (var item in itemsToRemove)
                    {
                        SelectedTextContextColumns.Remove(item);
                    }

                }
                else
                {
                    SelectedTextContextColumns.Clear();
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"Text Context Column seçenekleri güncellenirken hata oluştu: {ex.Message}", ex, true);
            }
        }

        /// <summary>
        /// Seçili metin bağlam sütunlarına göre bağlam bilgisini döndürür
        /// </summary>
        /// <param name="text">Metin objesi</param>
        /// <returns>Bağlam bilgisi</returns>
        public string GetTextContextInfo(dynamic? text, string column)
        {
            if (!SelectedTextContextColumns.Any() || text == null || string.IsNullOrWhiteSpace(column))
                return string.Empty;

            return column switch
            {
                "Key" => text?.Key ?? string.Empty,
                "Namespace" => text?.Namespace ?? string.Empty,
                _ => string.Empty
            };
        }

        #endregion

        public void Dispose()
        {
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();

            if (GoogleSheetsPanelVm?.TerminologyData != null)
            {
                GoogleSheetsPanelVm.TerminologyData.CollectionChanged -= TerminologyData_CollectionChanged;
            }

            GC.SuppressFinalize(this);
        }

        [RelayCommand]
        private void ToggleTextContextColumn(string columnName)
        {
            if (SelectedTextContextColumns.Contains(columnName))
            {
                SelectedTextContextColumns.Remove(columnName);
            }
            else
            {
                SelectedTextContextColumns.Add(columnName);
            }
        }
    }
}
