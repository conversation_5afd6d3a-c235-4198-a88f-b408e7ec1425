using System;
using System.Globalization;
using Avalonia.Data.Converters;

namespace TranslationAgent.Converters
{
    public class BoolToTextConverter : IValueConverter
    {
        public static readonly BoolToTextConverter Instance = new();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return boolValue ? "Taranıyor..." : "Çek";
            }
            return "Çek";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
