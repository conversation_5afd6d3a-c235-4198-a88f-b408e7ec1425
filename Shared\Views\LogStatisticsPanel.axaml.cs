using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using Avalonia.Threading;
using TranslationAgent.Shared.ViewModels;
using System;
using System.Collections.Specialized;

namespace TranslationAgent.Shared.Views
{
    public partial class LogStatisticsPanel : UserControl
    {
        private ScrollViewer? _logScrollViewer;

        public LogStatisticsPanel()
        {
            InitializeComponent();
            _logScrollViewer = this.FindControl<ScrollViewer>("LogScrollViewer");

            // DataContext değiştiğinde event'leri bağla
            DataContextChanged += OnDataContextChanged;
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        private void OnDataContextChanged(object? sender, EventArgs e)
        {
            if (DataContext is LogStatisticsPanelViewModel viewModel)
            {
                // LogMessages collection'ındaki değişiklikleri dinle
                viewModel.LogMessages.CollectionChanged += OnLogMessagesChanged;
            }
        }

        private void OnLogMessagesChanged(object? sender, NotifyCollectionChangedEventArgs e)
        {
            if (e.Action == NotifyCollectionChangedAction.Add)
            {
                // UI thread'de scroll işlemini gerçekleştir
                Dispatcher.UIThread.Post(() =>
                {
                    _logScrollViewer?.ScrollToEnd();
                });
            }
        }

        protected override void OnDetachedFromVisualTree(VisualTreeAttachmentEventArgs e)
        {
            // Event aboneliklerini temizle
            if (DataContext is LogStatisticsPanelViewModel viewModel)
            {
                viewModel.LogMessages.CollectionChanged -= OnLogMessagesChanged;
            }

            base.OnDetachedFromVisualTree(e);
        }
    }
}
