using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using Abot2.Poco;
using AngleSharp.Dom;
using Catalyst;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Mosaik.Core;
using TranslationAgent.Features.Baglam.Models;
using TranslationAgent.Features.GoogleSheets.ViewModels;
using TranslationAgent.Services;
using TranslationAgent.Shared.ViewModels;
using TranslationAgent.Helpers;
using SmartComponents.LocalEmbeddings;

namespace TranslationAgent.Features.Baglam.ViewModels
{
    public partial class BaglamViewModel : ObservableObject
    {
        private readonly AppSettings _appSettings;
        private readonly CrawlerService _crawlerService;
        private readonly LogService _logService;
        private readonly GeminiService _geminiService;
        private readonly GoogleSheetsService _googleSheetsService;
        private readonly GoogleSheetsPanelViewModel _googleSheetsPanelViewModel;
        private Pipeline _nlp;
        private LocalEmbedder _localEmbedder;

        // CancellationToken için özellikler
        private CancellationTokenSource? _cancellationTokenSource;
        public CancellationToken CancellationToken => _cancellationTokenSource?.Token ?? CancellationToken.None;

        [ObservableProperty]
        private string _webAddress = string.Empty;

        [ObservableProperty]
        private ObservableCollection<CrawledPage> _crawledUrls;

        [ObservableProperty]
        private CrawledPage? _selectedPage;

        [ObservableProperty]
        private Context? _selectedContext;


        // Düzenleme için yeni özellikler
        [ObservableProperty]
        private bool _isEditMode = false;

        [ObservableProperty]
        private string _editKategori = string.Empty;

        [ObservableProperty]
        private string _editAltBaslik = string.Empty;

        [ObservableProperty]
        private string _editIcerik = string.Empty;

        [ObservableProperty]
        private string _editURL = string.Empty;

        [ObservableProperty]
        private string _editKeywords = string.Empty;

        // Yeni bağlam ekleme için özellikler
        [ObservableProperty]
        private bool _isAddMode = false;

        [ObservableProperty]
        private string _newKategori = string.Empty;

        [ObservableProperty]
        private string _newAltBaslik = string.Empty;

        [ObservableProperty]
        private string _newIcerik = string.Empty;

        [ObservableProperty]
        private string _newURL = string.Empty;

        [ObservableProperty]
        private string _newKeywords = string.Empty;




        [ObservableProperty]
        private AIModel _selectedAIModel;

        [ObservableProperty]
        private ObservableCollection<AIModel> _availableAIModels;


        [ObservableProperty]
        private bool _isCrawling;

        [ObservableProperty]
        private bool _isProcessing;

        [ObservableProperty]
        private LogStatisticsPanelViewModel _logStatisticsPanelVm;

        public GoogleSheetsPanelViewModel GoogleSheetsPanelVm => _googleSheetsPanelViewModel;

        public AppSettings AppSettings => _appSettings;

        [ObservableProperty]
        private int _processedPages;

        [ObservableProperty]
        private int _foundContexts;

        [ObservableProperty]
        private int _errorCount;

        [ObservableProperty]
        private int _crawledPages;

        // Progress durumları için enum
        public enum ProgressState
        {
            Crawling,
            Processing
        }

        private ProgressState _currentProgressState = ProgressState.Crawling;

        // Property değişikliklerini dinlemek için partial metodlar
        partial void OnProcessedPagesChanged(int value)
        {
            LogStatisticsPanelVm.UpdateStatisticItem("İşlenen Sayfa:", ProcessedPages.ToString());
            UpdateStatistics();
        }

        partial void OnFoundContextsChanged(int value)
        {
            LogStatisticsPanelVm.UpdateStatisticItem("Bulunan Bağlam:", FoundContexts.ToString());
            UpdateStatistics();
        }

        partial void OnErrorCountChanged(int value)
        {
            LogStatisticsPanelVm.UpdateStatisticItem("Hata Sayısı:", ErrorCount.ToString());

        }

        partial void OnCrawledPagesChanged(int value)
        {
            LogStatisticsPanelVm.UpdateStatisticItem("Taranan Sayfa:", CrawledPages.ToString());
            UpdateStatistics();
        }

        partial void OnCrawledUrlsChanged(ObservableCollection<CrawledPage> value)
        {
            // Eski collection'dan event'leri kaldır
            if (value != null)
            {
                // Yeni collection'a event ekle
                value.CollectionChanged += CrawledUrls_CollectionChanged;
                // Mevcut sayıyı güncelle
                CrawledPages = value.Count;
            }
        }

        partial void OnIsCrawlingChanged(bool value)
        {
            StopCrawlCommand?.NotifyCanExecuteChanged();
        }

        private void CrawledUrls_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            CrawledPages = CrawledUrls?.Count ?? 0;
        }

        public BaglamViewModel(AppSettings appSettings, CrawlerService crawlerService, LogService logService, GeminiService geminiService, GoogleSheetsService googleSheetsService, GoogleSheetsPanelViewModel googleSheetsPanelViewModel, Pipeline nlp, LocalEmbedder localEmbedder)
        {
            _appSettings = appSettings;
            _crawlerService = crawlerService;
            _logService = logService;
            _geminiService = geminiService;
            _googleSheetsService = googleSheetsService;
            _googleSheetsPanelViewModel = googleSheetsPanelViewModel;
            _nlp = nlp;
            _localEmbedder = localEmbedder;
            _crawledUrls = new ObservableCollection<CrawledPage>();
            _crawledUrls.CollectionChanged += CrawledUrls_CollectionChanged;
            _availableAIModels = new ObservableCollection<AIModel>((AIModel[])_appSettings.General.AvailableAIModels);

            // Varsayılan değerleri ayarlardan al
            _selectedAIModel = _appSettings.Context.DefaultAIModel;


            // İstatistik değişkenlerini sıfırla
            ProcessedPages = 0;
            FoundContexts = 0;
            ErrorCount = 0;
            CrawledPages = 0;
            _currentProgressState = ProgressState.Crawling;

            // Log ve İstatistik Panel ViewModel'ini başlat ve özel etiketler ayarla - Bağlam için Context loglarını dinle
            _logStatisticsPanelVm = new LogStatisticsPanelViewModel(logService, LogService.LogState.Context);
            _logStatisticsPanelVm.SetCustomLabels("Bağlam İstatistikleri", "İşleme İlerlemesi:");

            // Bağlam sayfası için özel istatistik yapısını ayarla
            _logStatisticsPanelVm.SetStatisticItems(
                ("Taranan Sayfa:", "0"),
                ("İşlenen Sayfa:", "0"),
                ("Bulunan Bağlam:", "0"),
                ("Hata Sayısı:", "0")
            );

            // ContextData collection'ındaki değişiklikleri dinle
            _googleSheetsPanelViewModel.ContextData.CollectionChanged += ContextData_CollectionChanged;

            // Ana bağlam bulunduğunda tetiklenen event'i dinle
            _googleSheetsPanelViewModel.MainContextFound += OnMainContextFound;

            // Düzenleme ve ekleme komutlarını initialize et
            InitializeEditCommands();
        }

        private void ContextData_CollectionChanged(object? sender, System.Collections.Specialized.NotifyCollectionChangedEventArgs e)
        {
            // Collection değişikliklerini dinle (inline editing kaldırıldığı için sadece event dinleme amaçlı)
        }

        #region Command Initialization (Komut Başlatma)

        /// <summary>
        /// Düzenleme ve ekleme komutlarını başlatır
        /// </summary>
        private void InitializeEditCommands()
        {
            // Düzenleme komutları
            StartEditCommand = new RelayCommand<Context>(StartEdit, context => context != null && !IsEditMode && !IsAddMode);
            SaveEditCommand = new AsyncRelayCommand(SaveEdit, () => IsEditMode);
            CancelEditCommand = new RelayCommand(CancelEdit, () => IsEditMode);

            // Yeni bağlam ekleme komutları
            StartAddCommand = new RelayCommand(StartAdd, () => !IsEditMode && !IsAddMode);
            SaveAddCommand = new AsyncRelayCommand(SaveAdd, () => IsAddMode);
            CancelAddCommand = new RelayCommand(CancelAdd, () => IsAddMode);

            // Tarama durdurma komutu
            StopCrawlCommand = new RelayCommand(StopCrawl, () => IsCrawling);

            // İşleme durdurma komutu
            StopProcessingCommand = new RelayCommand(StopProcessing, () => IsProcessing);
        }

        // Komut property'leri
        public IRelayCommand<Context>? StartEditCommand { get; private set; }
        public IAsyncRelayCommand? SaveEditCommand { get; private set; }
        public IRelayCommand? CancelEditCommand { get; private set; }
        public IRelayCommand? StartAddCommand { get; private set; }
        public IAsyncRelayCommand? SaveAddCommand { get; private set; }
        public IRelayCommand? CancelAddCommand { get; private set; }
        public IRelayCommand? StopCrawlCommand { get; private set; }
        public IRelayCommand? StopProcessingCommand { get; private set; }

        // Property değişikliklerini dinlemek için partial metodlar
        partial void OnIsEditModeChanged(bool value)
        {
            NotifyEditCommands();
        }

        partial void OnIsAddModeChanged(bool value)
        {
            NotifyEditCommands();
        }

        partial void OnIsProcessingChanged(bool value)
        {
            StopProcessingCommand?.NotifyCanExecuteChanged();
        }

        #endregion

        /// <summary>
        /// Tarama işlemini durdurur
        /// </summary>
        private void StopCrawl()
        {
            if (IsCrawling && _cancellationTokenSource != null && !_cancellationTokenSource.IsCancellationRequested)
            {
                _logService.Info("Tarama işlemi durduruluyor...", true, LogService.LogState.Context);
                try
                {
                    _cancellationTokenSource.Cancel();
                }
                catch (OperationCanceledException)
                {
                    _logService.Warning("Tarama işlemi iptal edildi.", true, LogService.LogState.Context);
                }
            }
        }

        /// <summary>
        /// İşleme işlemini durdurur
        /// </summary>
        private void StopProcessing()
        {
            if (IsProcessing && _cancellationTokenSource != null && !_cancellationTokenSource.IsCancellationRequested)
            {
                _logService.Info("İşleme işlemi durduruluyor...", true, LogService.LogState.Context);
                try
                {
                    _cancellationTokenSource.Cancel();
                }
                catch (OperationCanceledException)
                {
                    _logService.Warning("İşleme işlemi iptal edildi.", true, LogService.LogState.Context);
                }
            }
        }

        [RelayCommand]
        private async Task CrawlAsync()
        {
            if (string.IsNullOrWhiteSpace(WebAddress))
            {
                _logService.Warning("Web adresi boş olamaz.");
                return;
            }

            if (IsCrawling)
            {
                _logService.Warning("Tarama zaten devam ediyor.");
                return;
            }

            try
            {
                // Yeni CancellationTokenSource oluştur
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = new CancellationTokenSource();

                IsCrawling = true;
                _currentProgressState = ProgressState.Crawling;
                _logService.Info($"Web sitesi taranmaya başlanıyor: {WebAddress}", true, LogService.LogState.Context);

                // Mevcut listeyi temizle
                CrawledUrls.Clear();

                // İstatistikleri sıfırla
                ProcessedPages = 0;
                FoundContexts = 0;
                ErrorCount = 0;
                UpdateStatistics();
                _crawlerService._crawledUrls.Clear();

                var crawledPages = _googleSheetsPanelViewModel.ContextData.Select(c => c.URL).Distinct().ToList();
                _crawlerService._crawledUrls.AddRange(crawledPages); // Zaten taranan sayfaları ekleyerek tarama süresini kısalt
                // Crawler servisini kullanarak web sitesini tara
                await _crawlerService.CrawlWebsiteAsync(WebAddress, CrawledUrls, _appSettings.Context.DefaultPageCount, _appSettings.Context.DefaultMaxDepth, respectRobotsTxt: false, cancellationToken: _cancellationTokenSource);

                if (CrawledUrls.Count == 0)
                {
                    _logService.Warning("Hiçbir sayfa taranamadı.", true, LogService.LogState.Context);
                }
                else
                {
                    _logService.Info($"Toplam {CrawledUrls.Count} sayfa tarandı.", true, LogService.LogState.Context);
                }
            }
            catch (OperationCanceledException)
            {
                _logService.Warning("Tarama işlemi kullanıcı tarafından durduruldu.", true, LogService.LogState.Context);
            }
            catch (Exception ex)
            {
                _logService.Error($"Web sitesi taranırken hata oluştu: {ex.Message}", ex, true, LogService.LogState.Context);
            }
            finally
            {
                IsCrawling = false;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }

        [RelayCommand]
        private void RemoveSelectedPage()
        {
            if (SelectedPage == null)
            {
                _logService.Warning("Silinecek sayfa seçilmemiş.", true, LogService.LogState.Context);
                return;
            }

            try
            {
                var pageToRemove = SelectedPage;
                CrawledUrls.Remove(pageToRemove);
                SelectedPage = null;
                _logService.Info($"Sayfa listeden silindi: {pageToRemove.Uri?.AbsoluteUri}", true, LogService.LogState.Context);
            }
            catch (Exception ex)
            {
                _logService.Error($"Sayfa silinirken hata oluştu: {ex.Message}", ex, true, LogService.LogState.Context);
            }
        }

        [RelayCommand]
        private async Task DeleteSelectedContextAsync()
        {
            if (SelectedContext == null)
            {
                _logService.Warning("Silinecek bağlam seçilmemiş.", true, LogService.LogState.Context);
                return;
            }

            try
            {
                var contextToDelete = SelectedContext;

                // Google Sheets'ten sil
                if (_googleSheetsService.IsAuthenticated &&
                    _googleSheetsPanelViewModel.SelectedContextSheet != null &&
                    !string.IsNullOrWhiteSpace(_googleSheetsPanelViewModel.SpreadsheetId))
                {
                    await ContextHelper.DeleteContextFromGoogleSheets(_googleSheetsService, _googleSheetsPanelViewModel, contextToDelete, _logService);
                }

                // Local collection'dan sil
                _googleSheetsPanelViewModel.ContextData.Remove(contextToDelete);

                SelectedContext = null;
                _logService.Info($"Bağlam başarıyla silindi: {contextToDelete.AltBaslik}", true, LogService.LogState.Context);
            }
            catch (Exception ex)
            {
                _logService.Error($"Bağlam silinirken hata oluştu: {ex.Message}", ex, true, LogService.LogState.Context);
            }
        }

        [RelayCommand]
        private async Task RefreshContextDataAsync()
        {
            try
            {
                if (_googleSheetsPanelViewModel.SelectedContextSheet != null)
                {
                    _logService.Info("Bağlam verileri yenileniyor...", true, LogService.LogState.Context);
                    await _googleSheetsPanelViewModel.FetchContextDataCommand.ExecuteAsync(null);
                    _logService.Info("Bağlam verileri başarıyla yenilendi.", true, LogService.LogState.Context);
                }
                else
                {
                    _logService.Warning("Bağlam sayfası seçilmemiş. Veriler yenilenemedi.", true, LogService.LogState.Context);
                }
            }
            catch (Exception ex)
            {
                _logService.Error($"Bağlam verileri yenilenirken hata oluştu: {ex.Message}", ex, true, LogService.LogState.Context);
            }
        }

        [RelayCommand]
        private async Task SetAsMainContextAsync()
        {
            if (SelectedContext == null)
            {
                _logService.Warning("Ana bağlam olarak seçilecek bağlam seçilmemiş.", true, LogService.LogState.Context);
                return;
            }

            var contextToPin = SelectedContext;
            var originalContext = ContextHelper.CloneContext(SelectedContext);

            // Eski ana bağlamdan "[Ana Bağlam]" yazısını kaldır
            if (_googleSheetsPanelViewModel.MainContext != null && !string.IsNullOrEmpty(_googleSheetsPanelViewModel.MainContext.Kategori))
            {
                if (_googleSheetsPanelViewModel.MainContext.Kategori.Equals("[Ana Bağlam]"))
                {
                    _googleSheetsPanelViewModel.MainContext.Kategori = _googleSheetsPanelViewModel.MainContext.Kategori.Replace("[Ana Bağlam]", "").Trim();
                }
                else
                    _googleSheetsPanelViewModel.MainContext.Kategori = _googleSheetsPanelViewModel.MainContext.Kategori.Replace(" [Ana Bağlam]", "").Trim();
            }

            _googleSheetsPanelViewModel.MainContext = contextToPin;

            // Yeni ana bağlama "[Ana Bağlam]" yazısını ekle
            if (!string.IsNullOrEmpty(_googleSheetsPanelViewModel.MainContext.Kategori))
            {
                if (!_googleSheetsPanelViewModel.MainContext.Kategori.Contains("[Ana Bağlam]"))
                {
                    _googleSheetsPanelViewModel.MainContext.Kategori = _googleSheetsPanelViewModel.MainContext.Kategori + " [Ana Bağlam]";
                }
            }
            else
            {
                _googleSheetsPanelViewModel.MainContext.Kategori = "[Ana Bağlam]";
            }

            // Ana bağlamı listenin en üstüne pinle
            var contextData = _googleSheetsPanelViewModel.ContextData;
            if (contextData.Any(c => c.ID == contextToPin.ID))
            {
                // Önce listeden kaldır
                contextData.Remove(contextToPin);
                // Sonra en üste ekle
                contextData.Insert(0, contextToPin);
                // Seçimi koru
                SelectedContext = contextToPin;
            }

            // Google sheets'te güncelle
            await ContextHelper.UpdateContextInGoogleSheets(_googleSheetsService, _googleSheetsPanelViewModel, originalContext, contextToPin, _logService);


            _logService.Info($"Ana bağlam seçildi ve en üste pinlendi: {_googleSheetsPanelViewModel.MainContext.AltBaslik}", true, LogService.LogState.Context);
        }

        private void OnMainContextFound(object? sender, Context mainContext)
        {
            // Ana bağlamı otomatik olarak seç ve en üste pinle
            _googleSheetsPanelViewModel.MainContext = mainContext;

            // Ana bağlamı listenin en üstüne pinle
            var contextData = _googleSheetsPanelViewModel.ContextData;
            if (contextData.Any(c => c.ID == mainContext.ID))
            {
                // Önce listeden kaldır
                contextData.Remove(mainContext);
                // Sonra en üste ekle
                contextData.Insert(0, mainContext);
            }
        }

        [RelayCommand]
        private async Task SendModelAsync()
        {
            if (CrawledUrls.Count == 0)
            {
                _logService.Warning("İşlenecek sayfa bulunamadı. Önce web sitesini taramalısınız.", true, LogService.LogState.Context);
                return;
            }

            if (IsProcessing)
            {
                _logService.Warning("İşleme zaten devam ediyor.", true, LogService.LogState.Context);
                return;
            }

            // Bağlam sayfası seçilmiş mi kontrol et
            if (_googleSheetsPanelViewModel.SelectedContextSheet == null)
            {
                _logService.Warning("Bağlam sayfası seçilmemiş. Lütfen önce Google Sheets sekmesinden bir bağlam sayfası seçin.", true, LogService.LogState.Context);
                return;
            }

            if (!_googleSheetsService.IsAuthenticated)
            {
                _logService.Warning("Google Sheets kimlik doğrulaması yapılmamış. Lütfen ayarlardan yapılandırın.", true, LogService.LogState.Context);
                return;
            }

            try
            {
                // Yeni CancellationTokenSource oluştur
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = new CancellationTokenSource();

                IsProcessing = true;
                _logService.Info("Sayfalar işlenmeye başlanıyor...", true, LogService.LogState.Context);

                // Progress state'i processing'e çevir
                _currentProgressState = ProgressState.Processing;

                // İstatistikleri sıfırla (TotalPages hariç)
                ProcessedPages = 0;
                FoundContexts = 0;
                ErrorCount = 0;
                UpdateStatistics();


                foreach (var page in CrawledUrls)
                {
                    // İptal kontrolü
                    if (_cancellationTokenSource?.Token.IsCancellationRequested == true)
                    {
                        _logService.Warning("İşleme işlemi iptal edildi.", true, LogService.LogState.Context);
                        break;
                    }

                    if (string.IsNullOrWhiteSpace(page.AngleSharpHtmlDocument.Body.TextContent)
                        || string.IsNullOrWhiteSpace(page.AngleSharpHtmlDocument.Body.InnerHtml))
                    {
                        _logService.Warning($"Sayfa içeriği boş: {page?.Uri?.AbsoluteUri ?? "Bilinmeyen URL"}", true, LogService.LogState.Context);
                        ErrorCount++;
                        continue;
                    }
                    if (_googleSheetsPanelViewModel.ContextData.Any(c => c.URL == page.Uri?.AbsoluteUri))
                    {
                        _logService.Warning($"Sayfa zaten işlendi: {page?.Uri?.AbsoluteUri ?? "Bilinmeyen URL"}", true, LogService.LogState.Context);
                        ErrorCount++;
                        continue;
                    }

                    int retryCount = 0;
                    bool success = false;
                    Exception? lastException = null;
                    SelectedPage = page;
                    _logService.Info($"Sayfa işleniyor: {page.Uri?.AbsoluteUri}", true, LogService.LogState.Context);
                    while (retryCount < 3 && !success)
                    {
                        try
                        {
                            if (retryCount == 2 && SelectedAIModel != AIModel.GeminiFlash2_5_Lite)
                            {
                                SelectedAIModel = AIModel.GeminiFlash2_5_Lite;
                                _logService.Info($"3. deneme AI modeli değiştirildi: {SelectedAIModel}", true, LogService.LogState.Context);
                            }

                            var result = await _geminiService.GenerateContentWithJsonToolsAsync(
                                systemprompt: _appSettings.Context.Prompt,
                                prompt: "Veri Adresi:" + page.Uri?.AbsoluteUri + "\n<kaynak_veri>\n" + page.AngleSharpHtmlDocument.Body.TextContent + "\n</kaynak_veri>",
                                toolsJson: _appSettings.Context.ContextFunction,
                                aiModel: SelectedAIModel,
                                logState: LogService.LogState.Context,
                                cancellationToken: _cancellationTokenSource.Token
                            );

                            if (result.HasValue)
                            {
                                _logService.Info($"Sayfa başarıyla işlendi: {page.Uri?.AbsoluteUri}", true, LogService.LogState.Context);
                                success = true;
                                ProcessedPages++;

                                // JSON result'ı parse et ve Context listesine ekle
                                var pageContexts = await ParseAndAddContexts(result.Value, page.Uri?.AbsoluteUri ?? "");
                                _logService.Info($"Toplam {pageContexts.Count} bağlam Google Sheets'e toplu olarak ekleniyor...", true, LogService.LogState.Context);
                                await AddContextsToGoogleSheetsBatchAsync(pageContexts);
                                FoundContexts += pageContexts.Count;
                                _logService.Info($"Tüm bağlamlar Google Sheets'e başarıyla eklendi.", true, LogService.LogState.Context);
                            }
                            else
                            {
                                throw new Exception("AI yanıtı boş geldi");
                            }
                        }
                        catch (Exception ex)
                        {
                            lastException = ex;
                            retryCount++;
                            if (retryCount < 3)
                            {
                                _logService.Warning($"Sayfa işlenirken hata oluştu, yeniden deneniyor ({retryCount}/3): {ex.Message}", true, LogService.LogState.Context);
                                await Task.Delay(1000 * retryCount); // Her denemede biraz daha bekle
                            }
                        }
                    }

                    if (!success && _cancellationTokenSource?.Token.IsCancellationRequested == false)
                    {
                        _logService.Error($"Sayfa işlenirken hata oluştu (3 deneme sonrası): {page.Uri?.AbsoluteUri}", lastException, true, LogService.LogState.Context);
                        ErrorCount++;
                    }
                }

                _logService.Info($"İşlem tamamlandı.", true, LogService.LogState.Context);
            }
            catch (OperationCanceledException)
            {
                _logService.Warning("İşleme işlemi kullanıcı tarafından durduruldu.", true, LogService.LogState.Context);
            }
            catch (Exception ex)
            {
                _logService.Error($"Sayfalar işlenirken hata oluştu: {ex.Message}", ex, true, LogService.LogState.Context);
            }
            finally
            {
                IsProcessing = false;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }

        private async Task<List<Context>> ParseAndAddContexts(JsonElement result, string sourceUrl)
        {
            var contexts = new List<Context>();

            try
            {
                // JSON object olup olmadığını kontrol et
                if (result.ValueKind == JsonValueKind.Object)
                {
                    // "contexts" property'sini ara
                    if (result.TryGetProperty("contexts", out var contextsElement))
                    {
                        if (contextsElement.ValueKind == JsonValueKind.Array)
                        {
                            foreach (var contextElement in contextsElement.EnumerateArray())
                            {
                                var context = ParseContextFromJson(contextElement, sourceUrl);
                                if (context != null)
                                {

                                    context.Lemma = TextProcessingHelper.ProcessTextToLemma(context.Icerik, _nlp);


                                    if (_googleSheetsPanelViewModel.ContextData.Any(c => c.Lemma == context.Lemma))
                                    {
                                        _logService.Info($"Bağlam zaten mevcut: {context.Kategori} - {context.AltBaslik}", true, LogService.LogState.Context);
                                        continue;
                                    }

                                    // Vektörü oluştur
                                    try
                                    {
                                        var vector = _localEmbedder.Embed(context.Kategori + " - " + context.AltBaslik + "\n" + context.Icerik);
                                        context.Vector = vector;
                                    }
                                    catch (Exception ex)
                                    {
                                        _logService.Warning($"Bağlam vektör oluşturulurken hata: {context.AltBaslik} - {ex.Message}", true, LogService.LogState.Context);
                                        // Hata durumunda Vector null kalacak ama işleme devam et
                                    }

                                    // Bağlamı listeye ekle (Google Sheets'e toplu ekleme için)
                                    contexts.Add(context);
                                    _googleSheetsPanelViewModel.ContextData.Add(context);
                                    _logService.Info($"Bağlam eklendi: {context.Kategori} - {context.AltBaslik}", true, LogService.LogState.Context);
                                }
                            }
                        }
                        else
                        {
                            _logService.Warning($"'contexts' property'si array değil: {contextsElement.ValueKind}", true, LogService.LogState.Context);
                        }
                    }
                    else
                    {
                        _logService.Warning("JSON'da 'contexts' property'si bulunamadı", true, LogService.LogState.Context);
                    }
                }
                else
                {
                    _logService.Warning($"Beklenmeyen JSON formatı: Object bekleniyor, {result.ValueKind} geldi", true, LogService.LogState.Context);
                }

                return contexts;
            }
            catch (Exception ex)
            {
                _logService.Error($"JSON parse edilirken hata oluştu: {ex.Message}", ex, true, LogService.LogState.Context);
                return contexts;
            }
        }

        private async Task AddContextsToGoogleSheetsBatchAsync(List<Context> contexts)
        {
            if (!_googleSheetsService.IsAuthenticated || _googleSheetsPanelViewModel.SelectedContextSheet == null || contexts.Count == 0)
            {
                return; // Sessizce çık
            }

            try
            {
                // Seçili bağlam sayfasına bağlamları toplu ekle
                var selectedSheet = _googleSheetsPanelViewModel.SelectedContextSheet;
                var selectedSpreadsheet = _googleSheetsPanelViewModel.SelectedSpreadsheet;

                if (selectedSpreadsheet == null)
                {
                    return; // Sessizce çık
                }

                // Tüm bağlamları satır verilerine dönüştür
                var allRowsData = new List<IList<object>>();
                foreach (var context in contexts)
                {
                    var rowData = new List<object>
                    {
                        context.ID,
                        context.Kategori ?? "",
                        context.AltBaslik ?? "",
                        context.Icerik ?? "",
                        context.URL ?? "",
                        context.Keywords != null ? string.Join(", ", context.Keywords) : ""
                    };
                    allRowsData.Add(rowData);
                }

                // Toplu olarak Google Sheets'e ekle
                await _googleSheetsService.AppendSheetDataAsync(selectedSpreadsheet.Id, selectedSheet.SheetName, allRowsData);
                _logService.Info($"Toplam {contexts.Count} bağlam '{selectedSheet.SheetName}' sayfasına toplu olarak eklendi.", true, LogService.LogState.Context);
            }
            catch (Exception ex)
            {
                _logService.Warning($"Bağlamlar Google Sheets'e toplu eklenirken hata: {ex.Message}", true, LogService.LogState.Context);
            }
        }

        private Context? ParseContextFromJson(JsonElement contextElement, string sourceUrl)
        {
            try
            {
                if (contextElement.ValueKind != JsonValueKind.Object)
                {
                    _logService.Warning("Geçersiz context formatı: Object bekleniyor", false, LogService.LogState.Context);
                    return null;
                }

                var context = new Context();

                // ID ata
                var newId = _googleSheetsPanelViewModel.ContextData.Any() ? _googleSheetsPanelViewModel.ContextData.Max(c => c.ID) + 1 : 1;
                context.ID = newId;

                // JSON'dan Context özelliklerini al
                if (contextElement.TryGetProperty("category", out var categoryElement))
                    context.Kategori = categoryElement.GetString() ?? "";

                if (contextElement.TryGetProperty("sub_title", out var subTitleElement))
                    context.AltBaslik = subTitleElement.GetString() ?? "";

                if (contextElement.TryGetProperty("content", out var contentElement))
                    context.Icerik = contentElement.GetString() ?? "";

                if (contextElement.TryGetProperty("keywords", out var keywordsElement))
                {
                    if (keywordsElement.ValueKind == JsonValueKind.Array)
                    {
                        foreach (var keyword in keywordsElement.EnumerateArray())
                        {
                            if (context.Keywords == null)
                            {
                                context.Keywords = new List<string>();
                            }
                            var keywrd = keyword.GetString();
                            if (!string.IsNullOrWhiteSpace(keywrd))
                            {
                                keywrd = TextProcessingHelper.ProcessTextToLemma(TextProcessingHelper.TermCleanStopWords(keywrd), _nlp);
                                context.Keywords.Add(keywrd);
                            }

                        }

                    }
                }



                // URL'yi kaynak URL olarak ayarla
                context.URL = sourceUrl;

                // Gerekli alanların dolu olup olmadığını kontrol et
                if (string.IsNullOrWhiteSpace(context.Kategori) ||
                    string.IsNullOrWhiteSpace(context.Icerik))
                {
                    _logService.Warning("Eksik context verisi: Kategori ve İçerik alanları zorunludur", false, LogService.LogState.Context);
                    return null;
                }

                return context;
            }
            catch (Exception ex)
            {
                _logService.Error($"Context parse edilirken hata oluştu: {ex.Message}", ex, false, LogService.LogState.Context);
                return null;
            }
        }





        private void UpdateStatistics()
        {

            // Progress bar'ı progress state'e göre güncelle
            double progress = 0;
            string progressLabel = "";

            switch (_currentProgressState)
            {
                case ProgressState.Crawling:
                    // Crawling sırasında progress bar
                    progress = CrawledPages > 0 ? (double)CrawledPages / _crawlerService._crawledUrls.Count * 100 : 0;
                    progressLabel = "Tarama İlerlemesi:";
                    break;

                case ProgressState.Processing:
                    // Processing sırasında işlenen sayfa / toplam sayfa
                    progress = CrawledPages > 0 ? (double)_crawlerService._crawledUrls.Count / CrawledPages * 100 : 0;
                    progressLabel = "İşleme İlerlemesi:";
                    break;
            }

            LogStatisticsPanelVm.SetCustomLabels(progressLabel: progressLabel);
            LogStatisticsPanelVm.SetProgressBar(progress);
        }

        #region Edit Operations (Panel Düzenleme İşlemleri)

        /// <summary>
        /// Düzenleme modunu başlatır ve seçili bağlamın bilgilerini düzenleme alanlarına yükler
        /// </summary>
        private void StartEdit(Context? context)
        {
            if (context == null) return;

            SelectedContext = context;
            IsEditMode = true;

            // Mevcut değerleri düzenleme alanlarına yükle
            LoadContextToEditFields(context);

            _logService.Info($"'{context.AltBaslik}' bağlamı düzenleme moduna alındı.", true, LogService.LogState.Context);
            NotifyEditCommands();
        }

        /// <summary>
        /// Bağlamın değerlerini düzenleme alanlarına yükler
        /// </summary>
        private void LoadContextToEditFields(Context context)
        {
            EditKategori = context.Kategori ?? string.Empty;
            EditAltBaslik = context.AltBaslik ?? string.Empty;
            EditIcerik = context.Icerik ?? string.Empty;
            EditURL = context.URL ?? string.Empty;
            EditKeywords = context.Keywords != null ? string.Join(", ", context.Keywords) : string.Empty;
        }

        /// <summary>
        /// Düzenleme panelindeki değişiklikleri kaydeder
        /// </summary>
        private async Task SaveEdit()
        {
            if (SelectedContext == null) return;

            try
            {
                // Orijinal değerleri mevcut context'ten al (panel düzenleme için)
                var originalContext = ContextHelper.CloneContext(SelectedContext);

                var GetEditedContextData = ContextHelper.GetEditedContextData(SelectedContext.ID, EditKategori, EditAltBaslik, EditIcerik, EditURL, EditKeywords);
                // Google Sheets'e güncellemeyi gönder
                await ContextHelper.UpdateContextInGoogleSheets(_googleSheetsService, _googleSheetsPanelViewModel, originalContext, GetEditedContextData, _logService);
                SelectedContext.Lemma = await Task.Run(() => TextProcessingHelper.ProcessTextToLemma(SelectedContext.Icerik, _nlp));
                SelectedContext.Vector = await Task.Run(() => _localEmbedder.Embed(SelectedContext.Kategori + " - " + SelectedContext.AltBaslik + "\n" + SelectedContext.Icerik));

                // Local veriyi güncelle
                ContextHelper.ApplyEditChangesToContext(SelectedContext, SelectedContext.ID, EditKategori, EditAltBaslik, EditIcerik, EditURL, EditKeywords);

                _logService.Info($"'{originalContext.AltBaslik}' bağlamı '{EditAltBaslik}' olarak güncellendi.", true, LogService.LogState.Context);

                // Düzenleme modunu kapat
                ExitEditMode();
            }
            catch (Exception ex)
            {
                _logService.Error($"Bağlam güncellenirken hata oluştu: {ex.Message}", ex, true, LogService.LogState.Context);
            }
        }

        /// <summary>
        /// Düzenleme işlemini iptal eder
        /// </summary>
        private void CancelEdit()
        {
            ExitEditMode();
            _logService.Info("Düzenleme iptal edildi.", true, LogService.LogState.Context);
        }


        /// <summary>
        /// Düzenleme modundan çıkar
        /// </summary>
        private void ExitEditMode()
        {
            IsEditMode = false;
            SelectedContext = null;
            ClearEditFields();
            NotifyEditCommands();
        }

        /// <summary>
        /// Düzenleme alanlarını temizler
        /// </summary>
        private void ClearEditFields()
        {
            EditKategori = string.Empty;
            EditAltBaslik = string.Empty;
            EditIcerik = string.Empty;
            EditURL = string.Empty;
            EditKeywords = string.Empty;
        }

        #endregion

        #region Add Operations (Yeni Bağlam Ekleme İşlemleri)

        /// <summary>
        /// Yeni bağlam ekleme modunu başlatır
        /// </summary>
        private void StartAdd()
        {
            IsAddMode = true;
            ClearAddFields();
            _logService.Info("Yeni bağlam ekleme modu başlatıldı.", true, LogService.LogState.Context);
            NotifyEditCommands();
        }

        /// <summary>
        /// Yeni bağlamı kaydeder
        /// </summary>
        private async Task SaveAdd()
        {
            try
            {
                // Validasyon
                if (!ValidateNewContext())
                    return;

                var newId = _googleSheetsPanelViewModel.ContextData.Any() ? _googleSheetsPanelViewModel.ContextData.Max(c => c.ID) + 1 : 1;

                // Yeni bağlam oluştur
                var newContext = ContextHelper.CreateNewContextFromFields(newId, NewKategori, NewAltBaslik, NewIcerik, NewURL, NewKeywords);

                // Google Sheets'e ekle
                await ContextHelper.AddContextToGoogleSheets(_googleSheetsService, _googleSheetsPanelViewModel, newContext, _logService);
                newContext.Lemma = await Task.Run(() => TextProcessingHelper.ProcessTextToLemma(newContext.Icerik, _nlp));
                newContext.Vector = await Task.Run(() => _localEmbedder.Embed(newContext.Kategori + " - " + newContext.AltBaslik + "\n" + newContext.Icerik));
                // Local listeye ekle
                _googleSheetsPanelViewModel.ContextData.Add(newContext);

                // Ekleme modunu kapat
                ExitAddMode();

                _logService.Info($"'{newContext.AltBaslik}' bağlamı başarıyla eklendi.", true, LogService.LogState.Context);
            }
            catch (Exception ex)
            {
                _logService.Error($"Yeni bağlam eklenirken hata oluştu: {ex.Message}", ex, true, LogService.LogState.Context);
            }
        }

        /// <summary>
        /// Yeni bağlam ekleme işlemini iptal eder
        /// </summary>
        private void CancelAdd()
        {
            ExitAddMode();
            _logService.Info("Yeni bağlam ekleme iptal edildi.", true, LogService.LogState.Context);
        }

        /// <summary>
        /// Yeni bağlam verilerini doğrular
        /// </summary>
        private bool ValidateNewContext()
        {
            var validation = ContextHelper.ValidateNewContext(NewKategori, NewIcerik);
            if (!validation.IsValid)
            {
                _logService.Warning(validation.ErrorMessage, true, LogService.LogState.Context);
                return false;
            }
            return true;
        }

        /// <summary>
        /// Ekleme modundan çıkar
        /// </summary>
        private void ExitAddMode()
        {
            IsAddMode = false;
            ClearAddFields();
            NotifyEditCommands();
        }

        /// <summary>
        /// Ekleme alanlarını temizler
        /// </summary>
        private void ClearAddFields()
        {
            NewKategori = string.Empty;
            NewAltBaslik = string.Empty;
            NewIcerik = string.Empty;
            NewURL = string.Empty;
            NewKeywords = string.Empty;
        }

        #endregion

        #region Command Notifications (Komut Bildirimleri)

        /// <summary>
        /// Düzenleme komutlarının durumunu günceller
        /// </summary>
        private void NotifyEditCommands()
        {
            StartEditCommand?.NotifyCanExecuteChanged();
            SaveEditCommand?.NotifyCanExecuteChanged();
            CancelEditCommand?.NotifyCanExecuteChanged();
            StartAddCommand?.NotifyCanExecuteChanged();
            SaveAddCommand?.NotifyCanExecuteChanged();
            CancelAddCommand?.NotifyCanExecuteChanged();
        }

        #endregion


    }
}