using System;
using System.Globalization;
using Avalonia.Data.Converters;

namespace TranslationAgent.Converters
{
    public class AIModelConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is AIModel model)
            {
                return model switch
                {
                    AIModel.GeminiFlash2_5_Lite => "Gemini 2.5 Flash Lite",
                    AIModel.GeminiFlash2_0 => "Gemini 2.0 Flash",
                    AIModel.GeminiFlash2_5 => "Gemini 2.5 Flash",
                    AIModel.Claude35Sonnet => "Claude 3.5 Sonnet",
                    AIModel.Claude37Sonnet => "Claude 3.7 Sonnet",
                    AIModel.Claude4Sonnet => "Claude 4 Sonnet",
                    _ => model.ToString()
                };
            }
            return value?.ToString() ?? string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}